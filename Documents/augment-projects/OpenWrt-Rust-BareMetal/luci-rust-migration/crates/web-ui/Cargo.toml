[package]
name = "luci-web-ui"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
description = "LuCI Web UI - Leptos-based frontend for OpenWrt administration"
keywords.workspace = true
categories.workspace = true

[[bin]]
name = "web-ui"
path = "src/main.rs"

[dependencies]
# Workspace dependencies
luci-shared-types = { path = "../shared-types" }
luci-package-manager = { path = "../package-manager" }

# Leptos framework
leptos = { workspace = true }
leptos_meta = { workspace = true }
leptos_router = { workspace = true }

# Logging
log = { workspace = true }
env_logger = { workspace = true }

# Serialization
serde = { workspace = true }
serde_json = { workspace = true }

# HTTP client for server functions
reqwest = { workspace = true }
urlencoding = { workspace = true }

# Utilities
uuid = { workspace = true }
chrono = { workspace = true }

# Logging
tracing = { workspace = true }

# Error handling
anyhow = { workspace = true }
thiserror = { workspace = true }

# Web APIs
web-sys = { version = "0.3", features = [
    "Window",
    "Storage",
    "File",
    "FileList",
    "FileReader",
    "Blob",
    "DataTransfer",
    "DragEvent",
    "HtmlInputElement",
    "Event",
    "EventTarget",
    "MouseEvent",
    "FormData",
    "console",
    "Document",
    "Element",
    "HtmlElement",
] }
wasm-bindgen = "0.2"
js-sys = "0.3"
gloo-timers = { version = "0.3", features = ["futures"] }

# File browser specific dependencies
human_bytes = "0.4"
gloo-net = { version = "0.6", features = ["http"] }

# Advanced forms dependencies
regex = "1.10"

[features]
default = ["ssr"]
ssr = ["leptos/ssr"]
hydrate = ["leptos/hydrate"]
