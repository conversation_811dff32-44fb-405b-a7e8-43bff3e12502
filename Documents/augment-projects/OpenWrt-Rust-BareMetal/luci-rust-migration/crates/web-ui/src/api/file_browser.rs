//! File browser API client
//!
//! This module provides API functions for file browser operations,
//! including directory listing, file operations, and file uploads.

use crate::types::file_browser::*;
use gloo_net::http::Request;
use serde_json;
use wasm_bindgen::JsValue;
use web_sys::{FormData, File};

/// API client for file browser operations
pub struct FileBrowserApi {
    base_url: String,
}

impl FileBrowserApi {
    /// Create a new file browser API client
    pub fn new(base_url: String) -> Self {
        Self { base_url }
    }

    /// Get directory listing
    pub async fn list_directory(&self, path: &str) -> Result<DirectoryListing, String> {
        let url = format!("{}/api/files/list", self.base_url);
        
        let response = Request::get(&url)
            .query([("path", path)])
            .send()
            .await
            .map_err(|e| format!("Failed to send request: {}", e))?;

        if !response.ok() {
            return Err(format!("HTTP error: {}", response.status()));
        }

        let listing: DirectoryListing = response
            .json()
            .await
            .map_err(|e| format!("Failed to parse response: {}", e))?;

        Ok(listing)
    }

    /// Get file information
    pub async fn get_file_info(&self, path: &str) -> Result<FileInfo, String> {
        let url = format!("{}/api/files/info", self.base_url);
        
        let response = Request::get(&url)
            .query([("path", path)])
            .send()
            .await
            .map_err(|e| format!("Failed to send request: {}", e))?;

        if !response.ok() {
            return Err(format!("HTTP error: {}", response.status()));
        }

        let file_info: FileInfo = response
            .json()
            .await
            .map_err(|e| format!("Failed to parse response: {}", e))?;

        Ok(file_info)
    }

    /// Upload files to a directory
    pub async fn upload_files(
        &self,
        files: Vec<File>,
        target_path: &str,
        overwrite: bool,
    ) -> Result<Vec<FileUploadResponse>, String> {
        let url = format!("{}/api/files/upload", self.base_url);
        
        let form_data = FormData::new()
            .map_err(|_| "Failed to create form data")?;

        // Add files to form data
        for (index, file) in files.iter().enumerate() {
            form_data
                .append_with_blob_and_filename(&format!("file_{}", index), file, &file.name())
                .map_err(|_| "Failed to append file to form data")?;
        }

        // Add metadata
        form_data
            .append_with_str("target_path", target_path)
            .map_err(|_| "Failed to append target path")?;
        
        form_data
            .append_with_str("overwrite", &overwrite.to_string())
            .map_err(|_| "Failed to append overwrite flag")?;

        let response = Request::post(&url)
            .body(form_data)
            .send()
            .await
            .map_err(|e| format!("Failed to send upload request: {}", e))?;

        if !response.ok() {
            return Err(format!("Upload failed with HTTP error: {}", response.status()));
        }

        let upload_responses: Vec<FileUploadResponse> = response
            .json()
            .await
            .map_err(|e| format!("Failed to parse upload response: {}", e))?;

        Ok(upload_responses)
    }

    /// Perform file operation (delete, copy, move, etc.)
    pub async fn file_operation(&self, request: FileOperationRequest) -> Result<FileOperationResponse, String> {
        let url = format!("{}/api/files/operation", self.base_url);
        
        let response = Request::post(&url)
            .json(&request)
            .map_err(|e| format!("Failed to serialize request: {}", e))?
            .send()
            .await
            .map_err(|e| format!("Failed to send request: {}", e))?;

        if !response.ok() {
            return Err(format!("HTTP error: {}", response.status()));
        }

        let operation_response: FileOperationResponse = response
            .json()
            .await
            .map_err(|e| format!("Failed to parse response: {}", e))?;

        Ok(operation_response)
    }

    /// Search for files
    pub async fn search_files(&self, request: FileSearchRequest) -> Result<FileSearchResponse, String> {
        let url = format!("{}/api/files/search", self.base_url);
        
        let response = Request::post(&url)
            .json(&request)
            .map_err(|e| format!("Failed to serialize request: {}", e))?
            .send()
            .await
            .map_err(|e| format!("Failed to send request: {}", e))?;

        if !response.ok() {
            return Err(format!("HTTP error: {}", response.status()));
        }

        let search_response: FileSearchResponse = response
            .json()
            .await
            .map_err(|e| format!("Failed to parse response: {}", e))?;

        Ok(search_response)
    }

    /// Get file preview
    pub async fn get_file_preview(&self, path: &str) -> Result<FilePreview, String> {
        let url = format!("{}/api/files/preview", self.base_url);
        
        let response = Request::get(&url)
            .query([("path", path)])
            .send()
            .await
            .map_err(|e| format!("Failed to send request: {}", e))?;

        if !response.ok() {
            return Err(format!("HTTP error: {}", response.status()));
        }

        let preview: FilePreview = response
            .json()
            .await
            .map_err(|e| format!("Failed to parse response: {}", e))?;

        Ok(preview)
    }

    /// Download file
    pub async fn download_file(&self, path: &str) -> Result<Vec<u8>, String> {
        let url = format!("{}/api/files/download", self.base_url);
        
        let response = Request::get(&url)
            .query([("path", path)])
            .send()
            .await
            .map_err(|e| format!("Failed to send request: {}", e))?;

        if !response.ok() {
            return Err(format!("HTTP error: {}", response.status()));
        }

        let bytes = response
            .binary()
            .await
            .map_err(|e| format!("Failed to get binary data: {}", e))?;

        Ok(bytes)
    }

    /// Create directory
    pub async fn create_directory(&self, path: &str, permissions: Option<u32>) -> Result<FileOperationResponse, String> {
        let request = FileOperationRequest {
            source_path: path.to_string(),
            target_path: None,
            operation: FileOperation::CreateDirectory,
            overwrite: false,
        };

        self.file_operation(request).await
    }

    /// Delete file or directory
    pub async fn delete(&self, path: &str) -> Result<FileOperationResponse, String> {
        let request = FileOperationRequest {
            source_path: path.to_string(),
            target_path: None,
            operation: FileOperation::Delete,
            overwrite: false,
        };

        self.file_operation(request).await
    }

    /// Copy file or directory
    pub async fn copy(&self, source: &str, target: &str, overwrite: bool) -> Result<FileOperationResponse, String> {
        let request = FileOperationRequest {
            source_path: source.to_string(),
            target_path: Some(target.to_string()),
            operation: FileOperation::Copy,
            overwrite,
        };

        self.file_operation(request).await
    }

    /// Move/rename file or directory
    pub async fn move_file(&self, source: &str, target: &str, overwrite: bool) -> Result<FileOperationResponse, String> {
        let request = FileOperationRequest {
            source_path: source.to_string(),
            target_path: Some(target.to_string()),
            operation: FileOperation::Move,
            overwrite,
        };

        self.file_operation(request).await
    }

    /// Change file permissions
    pub async fn change_permissions(&self, path: &str, permissions: u32) -> Result<FileOperationResponse, String> {
        let request = FileOperationRequest {
            source_path: path.to_string(),
            target_path: None,
            operation: FileOperation::ChangePermissions(permissions),
            overwrite: false,
        };

        self.file_operation(request).await
    }

    /// Change file owner
    pub async fn change_owner(&self, path: &str, owner: &str) -> Result<FileOperationResponse, String> {
        let request = FileOperationRequest {
            source_path: path.to_string(),
            target_path: None,
            operation: FileOperation::ChangeOwner(owner.to_string()),
            overwrite: false,
        };

        self.file_operation(request).await
    }
}

/// Default file browser API client
pub fn get_file_browser_api() -> FileBrowserApi {
    FileBrowserApi::new("".to_string()) // Use relative URLs
}
