//! Terminal API Client
//! 
//! Provides HTTP client functionality for terminal operations including:
//! - SSH connection management
//! - Command execution
//! - Session management
//! - Real-time terminal communication

use gloo_net::http::Request;
use serde::{Deserialize, Serialize};
use std::rc::Rc;

/// Terminal API client
#[derive(Clone)]
pub struct TerminalApi {
    base_url: String,
}

/// SSH connection configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SshConnection {
    pub host: String,
    pub port: u16,
    pub username: String,
    pub password: Option<String>,
    pub private_key: Option<String>,
    pub timeout: u32,
}

/// Terminal session information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TerminalSession {
    pub id: String,
    pub host: String,
    pub port: u16,
    pub username: String,
    pub connected_at: String,
    pub last_activity: String,
    pub status: String,
}

/// Terminal command request
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TerminalCommand {
    pub session_id: String,
    pub command: String,
    pub timeout: u32,
}

/// Command execution result
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CommandResult {
    pub output: String,
    pub error: Option<String>,
    pub exit_code: i32,
    pub execution_time: u64,
}

/// Terminal session list response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionListResponse {
    pub sessions: Vec<TerminalSession>,
    pub total: usize,
}

/// API error response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiError {
    pub error: String,
    pub code: Option<String>,
    pub details: Option<String>,
}

impl TerminalApi {
    /// Create new terminal API client
    pub fn new(base_url: String) -> Self {
        Self { base_url }
    }

    /// Connect to SSH server
    pub async fn connect_ssh(&self, connection: SshConnection) -> Result<TerminalSession, String> {
        let url = format!("{}/api/terminal/ssh/connect", self.base_url);
        
        let response = Request::post(&url)
            .header("Content-Type", "application/json")
            .json(&connection)
            .map_err(|e| format!("Failed to serialize request: {}", e))?
            .send()
            .await
            .map_err(|e| format!("Request failed: {}", e))?;

        if response.ok() {
            response
                .json::<TerminalSession>()
                .await
                .map_err(|e| format!("Failed to parse response: {}", e))
        } else {
            let error: ApiError = response
                .json()
                .await
                .unwrap_or_else(|_| ApiError {
                    error: "Unknown error occurred".to_string(),
                    code: None,
                    details: None,
                });
            Err(error.error)
        }
    }

    /// Disconnect from SSH server
    pub async fn disconnect_ssh(&self, session_id: &str) -> Result<(), String> {
        let url = format!("{}/api/terminal/ssh/disconnect/{}", self.base_url, session_id);
        
        let response = Request::delete(&url)
            .send()
            .await
            .map_err(|e| format!("Request failed: {}", e))?;

        if response.ok() {
            Ok(())
        } else {
            let error: ApiError = response
                .json()
                .await
                .unwrap_or_else(|_| ApiError {
                    error: "Failed to disconnect".to_string(),
                    code: None,
                    details: None,
                });
            Err(error.error)
        }
    }

    /// Execute command in terminal session
    pub async fn execute_command(&self, command: TerminalCommand) -> Result<CommandResult, String> {
        let url = format!("{}/api/terminal/execute", self.base_url);
        
        let response = Request::post(&url)
            .header("Content-Type", "application/json")
            .json(&command)
            .map_err(|e| format!("Failed to serialize request: {}", e))?
            .send()
            .await
            .map_err(|e| format!("Request failed: {}", e))?;

        if response.ok() {
            response
                .json::<CommandResult>()
                .await
                .map_err(|e| format!("Failed to parse response: {}", e))
        } else {
            let error: ApiError = response
                .json()
                .await
                .unwrap_or_else(|_| ApiError {
                    error: "Command execution failed".to_string(),
                    code: None,
                    details: None,
                });
            Err(error.error)
        }
    }

    /// Get active terminal sessions
    pub async fn get_sessions(&self) -> Result<SessionListResponse, String> {
        let url = format!("{}/api/terminal/sessions", self.base_url);
        
        let response = Request::get(&url)
            .send()
            .await
            .map_err(|e| format!("Request failed: {}", e))?;

        if response.ok() {
            response
                .json::<SessionListResponse>()
                .await
                .map_err(|e| format!("Failed to parse response: {}", e))
        } else {
            let error: ApiError = response
                .json()
                .await
                .unwrap_or_else(|_| ApiError {
                    error: "Failed to get sessions".to_string(),
                    code: None,
                    details: None,
                });
            Err(error.error)
        }
    }

    /// Get terminal session details
    pub async fn get_session(&self, session_id: &str) -> Result<TerminalSession, String> {
        let url = format!("{}/api/terminal/sessions/{}", self.base_url, session_id);
        
        let response = Request::get(&url)
            .send()
            .await
            .map_err(|e| format!("Request failed: {}", e))?;

        if response.ok() {
            response
                .json::<TerminalSession>()
                .await
                .map_err(|e| format!("Failed to parse response: {}", e))
        } else {
            let error: ApiError = response
                .json()
                .await
                .unwrap_or_else(|_| ApiError {
                    error: "Session not found".to_string(),
                    code: None,
                    details: None,
                });
            Err(error.error)
        }
    }

    /// Send raw input to terminal session (for interactive commands)
    pub async fn send_input(&self, session_id: &str, input: &str) -> Result<(), String> {
        let url = format!("{}/api/terminal/input/{}", self.base_url, session_id);
        
        #[derive(Serialize)]
        struct InputRequest {
            input: String,
        }
        
        let request_body = InputRequest {
            input: input.to_string(),
        };
        
        let response = Request::post(&url)
            .header("Content-Type", "application/json")
            .json(&request_body)
            .map_err(|e| format!("Failed to serialize request: {}", e))?
            .send()
            .await
            .map_err(|e| format!("Request failed: {}", e))?;

        if response.ok() {
            Ok(())
        } else {
            let error: ApiError = response
                .json()
                .await
                .unwrap_or_else(|_| ApiError {
                    error: "Failed to send input".to_string(),
                    code: None,
                    details: None,
                });
            Err(error.error)
        }
    }

    /// Get terminal output (for polling-based updates)
    pub async fn get_output(&self, session_id: &str, since: Option<u64>) -> Result<String, String> {
        let mut url = format!("{}/api/terminal/output/{}", self.base_url, session_id);
        
        if let Some(timestamp) = since {
            url = format!("{}?since={}", url, timestamp);
        }
        
        let response = Request::get(&url)
            .send()
            .await
            .map_err(|e| format!("Request failed: {}", e))?;

        if response.ok() {
            #[derive(Deserialize)]
            struct OutputResponse {
                output: String,
            }
            
            let output_response: OutputResponse = response
                .json()
                .await
                .map_err(|e| format!("Failed to parse response: {}", e))?;
                
            Ok(output_response.output)
        } else {
            let error: ApiError = response
                .json()
                .await
                .unwrap_or_else(|_| ApiError {
                    error: "Failed to get output".to_string(),
                    code: None,
                    details: None,
                });
            Err(error.error)
        }
    }

    /// Resize terminal (for responsive terminal emulation)
    pub async fn resize_terminal(&self, session_id: &str, rows: u16, cols: u16) -> Result<(), String> {
        let url = format!("{}/api/terminal/resize/{}", self.base_url, session_id);
        
        #[derive(Serialize)]
        struct ResizeRequest {
            rows: u16,
            cols: u16,
        }
        
        let request_body = ResizeRequest { rows, cols };
        
        let response = Request::post(&url)
            .header("Content-Type", "application/json")
            .json(&request_body)
            .map_err(|e| format!("Failed to serialize request: {}", e))?
            .send()
            .await
            .map_err(|e| format!("Request failed: {}", e))?;

        if response.ok() {
            Ok(())
        } else {
            let error: ApiError = response
                .json()
                .await
                .unwrap_or_else(|_| ApiError {
                    error: "Failed to resize terminal".to_string(),
                    code: None,
                    details: None,
                });
            Err(error.error)
        }
    }
}

/// Get terminal API instance
pub fn get_terminal_api() -> Rc<TerminalApi> {
    Rc::new(TerminalApi::new("".to_string())) // Base URL will be relative
}
