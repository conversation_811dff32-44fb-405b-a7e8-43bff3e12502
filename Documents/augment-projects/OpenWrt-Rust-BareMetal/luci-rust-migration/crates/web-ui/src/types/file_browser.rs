//! File browser types and data structures
//!
//! This module defines the data structures used by the file browser component
//! for representing files, directories, and file operations.

use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

/// File information structure
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub struct FileInfo {
    /// File name
    pub name: String,
    /// Full file path
    pub path: String,
    /// File size in bytes
    pub size: u64,
    /// File type (file, directory, symlink)
    pub file_type: FileType,
    /// File permissions
    pub permissions: FilePermissions,
    /// Last modified timestamp
    pub modified: DateTime<Utc>,
    /// Created timestamp
    pub created: Option<DateTime<Utc>>,
    /// MIME type for files
    pub mime_type: Option<String>,
    /// Whether the file is hidden
    pub is_hidden: bool,
    /// File owner
    pub owner: Option<String>,
    /// File group
    pub group: Option<String>,
}

/// File type enumeration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
pub enum FileType {
    /// Regular file
    File,
    /// Directory
    Directory,
    /// Symbolic link
    Symlink,
    /// Block device
    BlockDevice,
    /// Character device
    CharDevice,
    /// Named pipe (FIFO)
    Fifo,
    /// Socket
    Socket,
}

/// File permissions structure
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct FilePermissions {
    /// Octal permission value (e.g., 755)
    pub mode: u32,
    /// Human-readable permission string (e.g., "rwxr-xr-x")
    pub readable: String,
    /// Whether the current user can read
    pub can_read: bool,
    /// Whether the current user can write
    pub can_write: bool,
    /// Whether the current user can execute
    pub can_execute: bool,
}

/// Directory listing response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DirectoryListing {
    /// Current directory path
    pub path: String,
    /// Parent directory path (None if root)
    pub parent: Option<String>,
    /// List of files and directories
    pub entries: Vec<FileInfo>,
    /// Total number of entries
    pub total_count: usize,
    /// Whether there are more entries (for pagination)
    pub has_more: bool,
}

/// File upload request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileUploadRequest {
    /// Target directory path
    pub target_path: String,
    /// Whether to overwrite existing files
    pub overwrite: bool,
    /// File permissions to set
    pub permissions: Option<u32>,
}

/// File upload response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileUploadResponse {
    /// Uploaded file information
    pub file_info: FileInfo,
    /// Upload success status
    pub success: bool,
    /// Error message if upload failed
    pub error: Option<String>,
}

/// File operation request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileOperationRequest {
    /// Source file path
    pub source_path: String,
    /// Target file path (for copy/move operations)
    pub target_path: Option<String>,
    /// Operation type
    pub operation: FileOperation,
    /// Whether to overwrite existing files
    pub overwrite: bool,
}

/// File operation types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum FileOperation {
    /// Delete file or directory
    Delete,
    /// Copy file or directory
    Copy,
    /// Move/rename file or directory
    Move,
    /// Create directory
    CreateDirectory,
    /// Change permissions
    ChangePermissions(u32),
    /// Change owner
    ChangeOwner(String),
}

/// File operation response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileOperationResponse {
    /// Operation success status
    pub success: bool,
    /// Error message if operation failed
    pub error: Option<String>,
    /// Updated file information (for successful operations)
    pub file_info: Option<FileInfo>,
}

/// File search request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileSearchRequest {
    /// Search query (filename pattern)
    pub query: String,
    /// Directory to search in
    pub search_path: String,
    /// Whether to search recursively
    pub recursive: bool,
    /// File type filter
    pub file_type_filter: Option<FileType>,
    /// Maximum number of results
    pub limit: Option<usize>,
}

/// File search response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileSearchResponse {
    /// Search results
    pub results: Vec<FileInfo>,
    /// Total number of matches found
    pub total_matches: usize,
    /// Whether search was truncated due to limit
    pub truncated: bool,
}

/// File browser view mode
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ViewMode {
    /// List view with details
    List,
    /// Grid view with icons
    Grid,
    /// Tree view for directories
    Tree,
}

/// File browser sort options
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum SortBy {
    /// Sort by name
    Name,
    /// Sort by size
    Size,
    /// Sort by modification date
    Modified,
    /// Sort by file type
    Type,
}

/// Sort direction
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum SortDirection {
    /// Ascending order
    Ascending,
    /// Descending order
    Descending,
}

/// File browser state
#[derive(Debug, Clone)]
pub struct FileBrowserState {
    /// Current directory path
    pub current_path: String,
    /// Directory listing
    pub listing: Option<DirectoryListing>,
    /// Selected files
    pub selected_files: Vec<String>,
    /// View mode
    pub view_mode: ViewMode,
    /// Sort options
    pub sort_by: SortBy,
    /// Sort direction
    pub sort_direction: SortDirection,
    /// Whether to show hidden files
    pub show_hidden: bool,
    /// Loading state
    pub loading: bool,
    /// Error message
    pub error: Option<String>,
}

impl Default for FileBrowserState {
    fn default() -> Self {
        Self {
            current_path: "/".to_string(),
            listing: None,
            selected_files: Vec::new(),
            view_mode: ViewMode::List,
            sort_by: SortBy::Name,
            sort_direction: SortDirection::Ascending,
            show_hidden: false,
            loading: false,
            error: None,
        }
    }
}

/// File preview data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FilePreview {
    /// File information
    pub file_info: FileInfo,
    /// Preview content (for text files)
    pub content: Option<String>,
    /// Whether content was truncated
    pub truncated: bool,
    /// Preview type
    pub preview_type: PreviewType,
}

/// File preview types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum PreviewType {
    /// Text content preview
    Text,
    /// Image preview (base64 encoded)
    Image,
    /// Binary file (no preview)
    Binary,
    /// Directory listing
    Directory,
}
