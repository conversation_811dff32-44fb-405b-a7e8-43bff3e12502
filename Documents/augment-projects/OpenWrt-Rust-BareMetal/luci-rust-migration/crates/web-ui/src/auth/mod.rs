//! Authentication module for LuCI Web Interface
//!
//! Provides client-side authentication state management and API integration

use leptos::*;
use leptos_router::{navigate, NavigateOptions};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

/// Authentication state
#[derive(Debug, Clone, PartialEq)]
pub struct AuthState {
    pub authenticated: bool,
    pub user: Option<UserInfo>,
    pub session_id: Option<String>,
    pub expires_at: Option<DateTime<Utc>>,
    pub access_token: Option<String>,
    pub csrf_token: Option<String>,
}

impl Default for AuthState {
    fn default() -> Self {
        Self {
            authenticated: false,
            user: None,
            session_id: None,
            expires_at: None,
            access_token: None,
            csrf_token: None,
        }
    }
}

/// User information
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct UserInfo {
    pub username: String,
    pub role: String,
    pub permissions: Vec<String>,
    pub last_login: Option<DateTime<Utc>>,
}

/// Login request
#[derive(Debug, Serialize)]
pub struct LoginRequest {
    pub username: String,
    pub password: String,
}

/// Login response
#[derive(Debug, Deserialize)]
pub struct LoginResponse {
    pub access_token: String,
    pub token_type: String,
    pub expires_in: i64,
    pub expires_at: DateTime<Utc>,
    pub session_id: String,
    pub user: UserInfo,
}

/// API response wrapper
#[derive(Debug, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
}

/// Authentication status response
#[derive(Debug, Deserialize)]
pub struct AuthStatusResponse {
    pub authenticated: bool,
    pub user: Option<UserInfo>,
    pub session_id: Option<String>,
    pub expires_at: Option<DateTime<Utc>>,
}

/// CSRF token response
#[derive(Debug, Deserialize)]
pub struct CsrfTokenResponse {
    pub csrf_token: String,
}

/// Authentication context
#[derive(Debug, Clone)]
pub struct AuthContext {
    pub state: ReadSignal<AuthState>,
    pub set_state: WriteSignal<AuthState>,
}

impl AuthContext {
    /// Create new authentication context
    pub fn new() -> Self {
        let (state, set_state) = create_signal(AuthState::default());
        Self { state, set_state }
    }

    /// Login with username and password
    pub async fn login(&self, username: String, password: String) -> Result<(), String> {
        let request = LoginRequest { username, password };

        let client = reqwest::Client::new();
        let response = client
            .post("/api/auth/login")
            .json(&request)
            .send()
            .await
            .map_err(|e| format!("Network error: {}", e))?;

        if response.status().is_success() {
            let api_response: ApiResponse<LoginResponse> = response
                .json()
                .await
                .map_err(|e| format!("Failed to parse response: {}", e))?;

            if api_response.success {
                if let Some(login_data) = api_response.data {
                    // Store token in localStorage
                    if let Some(window) = web_sys::window() {
                        if let Some(storage) = window.local_storage().ok().flatten() {
                            let _ = storage.set_item("auth_token", &login_data.access_token);
                            let _ = storage.set_item("session_id", &login_data.session_id);
                        }
                    }

                    // Fetch CSRF token after successful login
                    let csrf_token = self.fetch_csrf_token(&login_data.access_token).await.ok();

                    // Update auth state
                    let new_state = AuthState {
                        authenticated: true,
                        user: Some(login_data.user),
                        session_id: Some(login_data.session_id),
                        expires_at: Some(login_data.expires_at),
                        access_token: Some(login_data.access_token),
                        csrf_token,
                    };
                    self.set_state.set(new_state);
                    Ok(())
                } else {
                    Err("No login data received".to_string())
                }
            } else {
                Err(api_response.error.unwrap_or("Login failed".to_string()))
            }
        } else {
            let error_text = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
            Err(format!("Login failed: {}", error_text))
        }
    }

    /// Logout current user
    pub async fn logout(&self) -> Result<(), String> {
        let current_state = self.state.get();

        if let Some(token) = &current_state.access_token {
            let client = reqwest::Client::new();
            let response = client
                .post("/api/auth/logout")
                .header("Authorization", format!("Bearer {}", token))
                .send()
                .await
                .map_err(|e| format!("Network error: {}", e))?;

            // Clear localStorage regardless of API response
            if let Some(window) = web_sys::window() {
                if let Some(storage) = window.local_storage().ok().flatten() {
                    let _ = storage.remove_item("auth_token");
                    let _ = storage.remove_item("session_id");
                }
            }

            // Update auth state
            self.set_state.set(AuthState::default());

            if response.status().is_success() {
                Ok(())
            } else {
                // Still return success since we cleared local state
                tracing::warn!("Logout API call failed, but local state cleared");
                Ok(())
            }
        } else {
            // No token, just clear state
            self.set_state.set(AuthState::default());
            Ok(())
        }
    }

    /// Check authentication status
    pub async fn check_auth_status(&self) -> Result<(), String> {
        // Try to get token from localStorage
        let token = if let Some(window) = web_sys::window() {
            if let Some(storage) = window.local_storage().ok().flatten() {
                storage.get_item("auth_token").ok().flatten()
            } else {
                None
            }
        } else {
            None
        };

        if let Some(token) = token {
            let client = reqwest::Client::new();
            let response = client
                .get("/api/auth/status")
                .header("Authorization", format!("Bearer {}", token))
                .send()
                .await
                .map_err(|e| format!("Network error: {}", e))?;

            if response.status().is_success() {
                let api_response: ApiResponse<AuthStatusResponse> = response
                    .json()
                    .await
                    .map_err(|e| format!("Failed to parse response: {}", e))?;

                if api_response.success {
                    if let Some(status) = api_response.data {
                        if status.authenticated {
                            // Fetch CSRF token for authenticated session
                            let csrf_token = self.fetch_csrf_token(&token).await.ok();

                            let new_state = AuthState {
                                authenticated: true,
                                user: status.user,
                                session_id: status.session_id,
                                expires_at: status.expires_at,
                                access_token: Some(token),
                                csrf_token,
                            };
                            self.set_state.set(new_state);
                        } else {
                            // Token is invalid, clear it
                            self.clear_auth().await;
                        }
                    }
                }
            } else {
                // Token is invalid, clear it
                self.clear_auth().await;
            }
        }

        Ok(())
    }

    /// Clear authentication state and localStorage
    async fn clear_auth(&self) {
        if let Some(window) = web_sys::window() {
            if let Some(storage) = window.local_storage().ok().flatten() {
                let _ = storage.remove_item("auth_token");
                let _ = storage.remove_item("session_id");
            }
        }
        self.set_state.set(AuthState::default());
    }

    /// Check if user has permission
    pub fn has_permission(&self, permission: &str) -> bool {
        let state = self.state.get();
        if let Some(user) = &state.user {
            user.permissions.contains(&permission.to_string())
        } else {
            false
        }
    }

    /// Check if user has any of the specified permissions
    pub fn has_any_permission(&self, permissions: &[&str]) -> bool {
        let state = self.state.get();
        if let Some(user) = &state.user {
            permissions.iter().any(|perm| user.permissions.contains(&perm.to_string()))
        } else {
            false
        }
    }

    /// Check if user has all of the specified permissions
    pub fn has_all_permissions(&self, permissions: &[&str]) -> bool {
        let state = self.state.get();
        if let Some(user) = &state.user {
            permissions.iter().all(|perm| user.permissions.contains(&perm.to_string()))
        } else {
            false
        }
    }

    /// Check if user has specific role
    pub fn has_role(&self, role: &str) -> bool {
        let state = self.state.get();
        if let Some(user) = &state.user {
            user.role.eq_ignore_ascii_case(role)
        } else {
            false
        }
    }

    /// Check if user is admin
    pub fn is_admin(&self) -> bool {
        self.has_role("admin")
    }

    /// Check if user is regular user (not guest)
    pub fn is_user(&self) -> bool {
        self.has_role("user") || self.has_role("admin")
    }

    /// Check if user is guest
    pub fn is_guest(&self) -> bool {
        self.has_role("guest")
    }

    /// Get current user role
    pub fn get_user_role(&self) -> Option<String> {
        self.state.get().user.map(|user| user.role)
    }

    /// Get current user permissions
    pub fn get_user_permissions(&self) -> Vec<String> {
        self.state.get().user.map(|user| user.permissions).unwrap_or_default()
    }

    /// Get current user info
    pub fn get_user(&self) -> Option<UserInfo> {
        self.state.get().user
    }

    /// Check if user is authenticated
    pub fn is_authenticated(&self) -> bool {
        self.state.get().authenticated
    }

    /// Get CSRF token for API calls
    pub fn get_csrf_token(&self) -> Option<String> {
        self.state.get().csrf_token
    }

    /// Fetch CSRF token from server
    async fn fetch_csrf_token(&self, access_token: &str) -> Result<String, String> {
        let client = reqwest::Client::new();
        let response = client
            .get("/api/auth/csrf-token")
            .header("Authorization", format!("Bearer {}", access_token))
            .send()
            .await
            .map_err(|e| format!("Network error: {}", e))?;

        if response.status().is_success() {
            let api_response: ApiResponse<CsrfTokenResponse> = response
                .json()
                .await
                .map_err(|e| format!("Failed to parse response: {}", e))?;

            if api_response.success {
                if let Some(csrf_data) = api_response.data {
                    Ok(csrf_data.csrf_token)
                } else {
                    Err("No CSRF token received".to_string())
                }
            } else {
                Err(api_response.error.unwrap_or("Failed to fetch CSRF token".to_string()))
            }
        } else {
            Err(format!("HTTP error: {}", response.status()))
        }
    }

    /// Make authenticated API call with CSRF protection
    pub async fn make_api_call(
        &self,
        method: &str,
        url: &str,
        body: Option<serde_json::Value>,
    ) -> Result<String, String> {
        let state = self.state.get();

        if !state.authenticated {
            return Err("Not authenticated".to_string());
        }

        let access_token = state.access_token
            .ok_or("No access token available")?;

        let client = reqwest::Client::new();
        let mut request_builder = match method.to_uppercase().as_str() {
            "GET" => client.get(url),
            "POST" => client.post(url),
            "PUT" => client.put(url),
            "DELETE" => client.delete(url),
            "PATCH" => client.patch(url),
            _ => return Err(format!("Unsupported HTTP method: {}", method)),
        };

        // Add authorization header
        request_builder = request_builder.header("Authorization", format!("Bearer {}", access_token));

        // Add CSRF token for state-changing operations
        if !matches!(method.to_uppercase().as_str(), "GET" | "HEAD" | "OPTIONS") {
            if let Some(csrf_token) = &state.csrf_token {
                request_builder = request_builder.header("X-CSRF-Token", csrf_token);
            } else {
                return Err("CSRF token not available for state-changing operation".to_string());
            }
        }

        // Add JSON body if provided
        if let Some(json_body) = body {
            request_builder = request_builder.json(&json_body);
        }

        request_builder
            .send()
            .await
            .map_err(|e| format!("Network error: {}", e))
    }
}

/// Authentication context provider
#[component]
pub fn AuthProvider(children: Children) -> impl IntoView {
    let auth_context = AuthContext::new();

    // Check authentication status on mount
    let auth_context_clone = auth_context.clone();
    create_effect(move |_| {
        let auth_clone = auth_context_clone.clone();
        spawn_local(async move {
            let _ = auth_clone.check_auth_status().await;
        });
    });

    provide_context(auth_context);
    children()
}

/// Hook to use authentication context
pub fn use_auth_context() -> AuthContext {
    use_context::<AuthContext>()
        .expect("AuthContext must be provided")
}

/// Protected route component
#[component]
pub fn ProtectedRoute(
    children: Children,
    #[prop(optional)] fallback: Option<View>,
    #[prop(optional)] required_permission: Option<String>,
    #[prop(optional)] required_role: Option<String>,
    #[prop(optional)] required_permissions: Option<Vec<String>>,
    #[prop(optional)] require_all_permissions: Option<bool>,
) -> impl IntoView {
    let auth = use_auth_context();
    let auth_state = auth.state;

    view! {
        {
            let state = auth_state.get();
            if state.authenticated {
                let has_access = if let Some(role) = required_role.as_ref() {
                    auth.has_role(role)
                } else if let Some(permission) = required_permission.as_ref() {
                    auth.has_permission(permission)
                } else if let Some(permissions) = required_permissions.as_ref() {
                    if require_all_permissions.unwrap_or(false) {
                        let perms: Vec<&str> = permissions.iter().map(|s| s.as_str()).collect();
                        auth.has_all_permissions(&perms)
                    } else {
                        let perms: Vec<&str> = permissions.iter().map(|s| s.as_str()).collect();
                        auth.has_any_permission(&perms)
                    }
                } else {
                    true // No specific requirements, just need to be authenticated
                };

                if has_access {
                    children().into_view()
                } else if let Some(fallback_view) = fallback.as_ref() {
                    fallback_view.clone()
                } else {
                    view! {
                        <div class="min-h-screen flex items-center justify-center bg-gray-50">
                            <div class="text-center">
                                <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-red-100">
                                    <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                </div>
                                <h1 class="mt-6 text-2xl font-bold text-gray-900 mb-4">
                                    "Access Denied"
                                </h1>
                                <p class="text-gray-600 mb-6">
                                    "You don't have permission to access this page."
                                </p>
                                <a href="/" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                    "Return to Dashboard"
                                </a>
                            </div>
                        </div>
                    }.into_view()
                }
            } else {
                view! {
                    <div class="min-h-screen flex items-center justify-center bg-gray-50">
                        <div class="text-center">
                            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                            <p class="mt-4 text-gray-600">"Checking authentication..."</p>
                        </div>
                    </div>
                }.into_view()
            }
        }
    }
}

/// Component that renders children only if user has required permission
#[component]
pub fn RequirePermission(
    children: Children,
    permission: String,
    #[prop(optional)] fallback: Option<View>,
) -> impl IntoView {
    let auth = use_auth_context();

    view! {
        {
            if auth.has_permission(&permission) {
                children().into_view()
            } else if let Some(fallback_view) = fallback {
                fallback_view
            } else {
                view! { <div></div> }.into_view()
            }
        }
    }
}

/// Component that renders children only if user has required role
#[component]
pub fn RequireRole(
    children: Children,
    role: String,
    #[prop(optional)] fallback: Option<View>,
) -> impl IntoView {
    let auth = use_auth_context();

    view! {
        {
            if auth.has_role(&role) {
                children().into_view()
            } else if let Some(fallback_view) = fallback {
                fallback_view
            } else {
                view! { <div></div> }.into_view()
            }
        }
    }
}

/// Component that renders children only if user has any of the specified permissions
#[component]
pub fn RequireAnyPermission(
    children: Children,
    permissions: Vec<String>,
    #[prop(optional)] fallback: Option<View>,
) -> impl IntoView {
    let auth = use_auth_context();

    view! {
        {
            let perms: Vec<&str> = permissions.iter().map(|s| s.as_str()).collect();
            if auth.has_any_permission(&perms) {
                children().into_view()
            } else if let Some(fallback_view) = fallback {
                fallback_view
            } else {
                view! { <div></div> }.into_view()
            }
        }
    }
}

/// Component that renders children only if user has all of the specified permissions
#[component]
pub fn RequireAllPermissions(
    children: Children,
    permissions: Vec<String>,
    #[prop(optional)] fallback: Option<View>,
) -> impl IntoView {
    let auth = use_auth_context();

    view! {
        {
            let perms: Vec<&str> = permissions.iter().map(|s| s.as_str()).collect();
            if auth.has_all_permissions(&perms) {
                children().into_view()
            } else if let Some(fallback_view) = fallback {
                fallback_view
            } else {
                view! { <div></div> }.into_view()
            }
        }
    }
}

/// Component that renders children only for admin users
#[component]
pub fn AdminOnly(
    children: Children,
    #[prop(optional)] fallback: Option<View>,
) -> impl IntoView {
    let auth = use_auth_context();

    view! {
        {
            if auth.is_admin() {
                children().into_view()
            } else if let Some(fallback_view) = fallback {
                fallback_view
            } else {
                view! { <div></div> }.into_view()
            }
        }
    }
}

/// Component that renders children only for authenticated users (not guests)
#[component]
pub fn UserOnly(
    children: Children,
    #[prop(optional)] fallback: Option<View>,
) -> impl IntoView {
    let auth = use_auth_context();

    view! {
        {
            if auth.is_user() {
                children().into_view()
            } else if let Some(fallback_view) = fallback {
                fallback_view
            } else {
                view! { <div></div> }.into_view()
            }
        }
    }
}