use leptos::*;
use leptos_meta::*;
use leptos_router::*;
use luci_web_ui::*;

fn main() {
    // Initialize logging
    env_logger::init();
    
    log::info!("Starting LuCI Web UI server...");
    
    mount_to_body(|| {
        view! {
            <Router>
                <main>
                    <Routes>
                        <Route path="" view=App/>
                    </Routes>
                </main>
            </Router>
        }
    })
}
