use leptos::prelude::*;

#[component]
fn App() -> impl IntoView {
    view! {
        <div class="min-h-screen bg-gray-100">
            <h1 class="text-3xl font-bold text-center py-8">
                "OpenWrt LuCI - Rust Migration"
            </h1>
            <div class="container mx-auto px-4">
                <p class="text-center text-gray-600">
                    "Welcome to the OpenWrt LuCI interface built with Rust and Leptos!"
                </p>
                <div class="mt-8 text-center">
                    <p class="text-sm text-gray-500">
                        "🎉 WASM compilation successful! The UI is now running."
                    </p>
                </div>
            </div>
        </div>
    }
}

fn main() {
    // Initialize logging
    env_logger::init();

    log::info!("Starting LuCI Web UI server...");

    mount_to_body(App)
}
