//! Loading components for OpenWrt LuCI interface
//!
//! This module provides reusable loading indicators and skeleton components
//! for better user experience during data loading.

use leptos::*;

/// Loading spinner component
#[component]
pub fn LoadingSpinner(
    /// Size of the spinner (small, medium, large)
    #[prop(default = "medium".to_string())]
    size: String,
    /// Additional CSS classes
    #[prop(optional)]
    class: Option<String>,
    /// Loading message to display
    #[prop(optional)]
    message: Option<String>,
) -> impl IntoView {
    let size_class = match size.as_str() {
        "small" => "h-4 w-4",
        "large" => "h-8 w-8",
        _ => "h-6 w-6", // medium
    };
    
    let additional_classes = class.unwrap_or_default();
    let spinner_classes = format!("loading-spinner {} {}", size_class, additional_classes);

    view! {
        <div class="flex flex-col items-center justify-center">
            <div class={spinner_classes}></div>
            {message.map(|msg| view! {
                <p class="mt-2 text-sm text-gray-600">{msg}</p>
            })}
        </div>
    }
}

/// Full page loading overlay
#[component]
pub fn LoadingOverlay(
    /// Whether the overlay is visible
    visible: ReadSignal<bool>,
    /// Loading message
    #[prop(default = "Loading...".to_string())]
    message: String,
    /// Additional CSS classes
    #[prop(optional)]
    class: Option<String>,
) -> impl IntoView {
    let additional_classes = class.unwrap_or_default();
    let overlay_classes = format!(
        "fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 {}",
        additional_classes
    );

    view! {
        <div class={move || if visible.get() { overlay_classes.clone() } else { "hidden".to_string() }}>
            <div class="bg-white rounded-lg p-6 shadow-lg">
                <div class="flex flex-col items-center">
                    <div class="loading-spinner h-8 w-8 mb-4"></div>
                    <p class="text-lg font-medium text-gray-900">{message}</p>
                </div>
            </div>
        </div>
    }
}

/// Skeleton loader for text content
#[component]
pub fn SkeletonText(
    /// Number of lines to show
    #[prop(default = 3)]
    lines: u32,
    /// Additional CSS classes
    #[prop(optional)]
    class: Option<String>,
) -> impl IntoView {
    let additional_classes = class.unwrap_or_default();
    let skeleton_classes = format!("loading-pulse h-4 mb-2 {}", additional_classes);

    let skeleton_lines = (0..lines).map(|i| {
        let width = match i % 3 {
            0 => "w-full",
            1 => "w-3/4",
            _ => "w-1/2",
        };
        view! {
            <div class={format!("{} {}", skeleton_classes, width)}></div>
        }
    }).collect_view();

    view! {
        <div class="animate-pulse">
            {skeleton_lines}
        </div>
    }
}

/// Skeleton loader for card content
#[component]
pub fn SkeletonCard(
    /// Whether to show header
    #[prop(default = true)]
    show_header: bool,
    /// Number of content lines
    #[prop(default = 4)]
    content_lines: u32,
    /// Additional CSS classes
    #[prop(optional)]
    class: Option<String>,
) -> impl IntoView {
    let additional_classes = class.unwrap_or_default();
    let card_classes = format!("card animate-pulse {}", additional_classes);

    view! {
        <div class={card_classes}>
            {if show_header {
                view! {
                    <div class="card-header">
                        <div class="loading-pulse h-6 w-1/3"></div>
                    </div>
                }
            } else {
                view! { <div></div> }
            }}
            <div class="card-body">
                <SkeletonText lines={content_lines} />
            </div>
        </div>
    }
}

/// Skeleton loader for table content
#[component]
pub fn SkeletonTable(
    /// Number of rows to show
    #[prop(default = 5)]
    rows: u32,
    /// Number of columns to show
    #[prop(default = 4)]
    columns: u32,
    /// Additional CSS classes
    #[prop(optional)]
    class: Option<String>,
) -> impl IntoView {
    let additional_classes = class.unwrap_or_default();
    let table_classes = format!("table {}", additional_classes);

    let header_cells = (0..columns).map(|_| {
        view! {
            <th class="table-header-cell">
                <div class="loading-pulse h-4 w-20"></div>
            </th>
        }
    }).collect_view();

    let body_rows = (0..rows).map(|_| {
        let cells = (0..columns).map(|_| {
            view! {
                <td class="table-cell">
                    <div class="loading-pulse h-4 w-16"></div>
                </td>
            }
        }).collect_view();

        view! {
            <tr class="table-row">
                {cells}
            </tr>
        }
    }).collect_view();

    view! {
        <div class="animate-pulse">
            <table class={table_classes}>
                <thead class="table-header">
                    <tr>
                        {header_cells}
                    </tr>
                </thead>
                <tbody class="table-body">
                    {body_rows}
                </tbody>
            </table>
        </div>
    }
}

/// Progress bar component
#[component]
pub fn ProgressBar(
    /// Current progress value (0-100)
    value: ReadSignal<f32>,
    /// Maximum value (default 100)
    #[prop(default = 100.0)]
    max: f32,
    /// Whether to show percentage text
    #[prop(default = true)]
    show_percentage: bool,
    /// Progress bar color variant
    #[prop(default = "primary".to_string())]
    variant: String,
    /// Additional CSS classes
    #[prop(optional)]
    class: Option<String>,
) -> impl IntoView {
    let additional_classes = class.unwrap_or_default();
    let container_classes = format!("progress-bar {}", additional_classes);
    
    let fill_color = match variant.as_str() {
        "success" => "bg-status-success",
        "warning" => "bg-status-warning",
        "error" => "bg-status-error",
        _ => "bg-openwrt-blue", // primary
    };

    view! {
        <div class="space-y-2">
            {if show_percentage {
                view! {
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">"Progress"</span>
                        <span class="text-gray-900">{move || format!("{:.1}%", (value.get() / max) * 100.0)}</span>
                    </div>
                }
            } else {
                view! { <div></div> }
            }}
            <div class={container_classes}>
                <div 
                    class={format!("progress-fill {}", fill_color)}
                    style={move || format!("width: {}%", (value.get() / max) * 100.0)}
                ></div>
            </div>
        </div>
    }
}

/// Loading button state component
#[component]
pub fn LoadingButton(
    /// Button text when not loading
    children: Children,
    /// Whether the button is in loading state
    loading: ReadSignal<bool>,
    /// Loading text to show
    #[prop(default = "Loading...".to_string())]
    loading_text: String,
    /// Button variant
    #[prop(default = "btn-primary".to_string())]
    variant: String,
    /// Click handler
    #[prop(optional)]
    on_click: Option<Box<dyn Fn() + 'static>>,
    /// Additional CSS classes
    #[prop(optional)]
    class: Option<String>,
) -> impl IntoView {
    let additional_classes = class.unwrap_or_default();
    let button_classes = format!("btn {} {}", variant, additional_classes);

    view! {
        <button
            type="button"
            class={button_classes}
            disabled={move || loading.get()}
            on:click=move |_| {
                if !loading.get() {
                    if let Some(handler) = &on_click {
                        handler();
                    }
                }
            }
        >
            {if loading.get() {
                view! {
                    <div class="flex items-center">
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-current" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        {loading_text}
                    </div>
                }.into_view()
            } else {
                children().into_view()
            }}
        </button>
    }
}
