use leptos::*;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// System monitoring application types
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum MonitoringAppType {
    SystemMetrics,
    NetworkMonitoring,
    ProcessMonitoring,
    ResourceMonitoring,
    LogAnalyzer,
    PerformanceProfiler,
    AlertManager,
    HealthChecker,
    UptimeMonitor,
    BandwidthMonitor,
    SecurityMonitor,
    ServiceMonitor,
}

impl MonitoringAppType {
    pub fn display_name(&self) -> &'static str {
        match self {
            MonitoringAppType::SystemMetrics => "System Metrics",
            MonitoringAppType::NetworkMonitoring => "Network Monitoring",
            MonitoringAppType::ProcessMonitoring => "Process Monitoring",
            MonitoringAppType::ResourceMonitoring => "Resource Monitoring",
            MonitoringAppType::LogAnalyzer => "Log Analyzer",
            MonitoringAppType::PerformanceProfiler => "Performance Profiler",
            MonitoringAppType::AlertManager => "Alert Manager",
            MonitoringAppType::HealthChecker => "Health Checker",
            MonitoringAppType::UptimeMonitor => "Uptime Monitor",
            MonitoringAppType::BandwidthMonitor => "Bandwidth Monitor",
            MonitoringAppType::SecurityMonitor => "Security Monitor",
            MonitoringAppType::ServiceMonitor => "Service Monitor",
        }
    }
    
    pub fn icon(&self) -> &'static str {
        match self {
            MonitoringAppType::SystemMetrics => "📊",
            MonitoringAppType::NetworkMonitoring => "🌐",
            MonitoringAppType::ProcessMonitoring => "⚙️",
            MonitoringAppType::ResourceMonitoring => "💾",
            MonitoringAppType::LogAnalyzer => "📋",
            MonitoringAppType::PerformanceProfiler => "🚀",
            MonitoringAppType::AlertManager => "🚨",
            MonitoringAppType::HealthChecker => "❤️",
            MonitoringAppType::UptimeMonitor => "⏰",
            MonitoringAppType::BandwidthMonitor => "📈",
            MonitoringAppType::SecurityMonitor => "🔒",
            MonitoringAppType::ServiceMonitor => "🔧",
        }
    }
    
    pub fn description(&self) -> &'static str {
        match self {
            MonitoringAppType::SystemMetrics => "Real-time system performance metrics and statistics",
            MonitoringAppType::NetworkMonitoring => "Network traffic analysis and connection monitoring",
            MonitoringAppType::ProcessMonitoring => "Process lifecycle and resource usage monitoring",
            MonitoringAppType::ResourceMonitoring => "CPU, memory, disk, and system resource tracking",
            MonitoringAppType::LogAnalyzer => "System log analysis and pattern detection",
            MonitoringAppType::PerformanceProfiler => "Application and system performance profiling",
            MonitoringAppType::AlertManager => "Automated alerting and notification system",
            MonitoringAppType::HealthChecker => "System health checks and diagnostics",
            MonitoringAppType::UptimeMonitor => "Service uptime and availability monitoring",
            MonitoringAppType::BandwidthMonitor => "Network bandwidth usage and traffic analysis",
            MonitoringAppType::SecurityMonitor => "Security event monitoring and threat detection",
            MonitoringAppType::ServiceMonitor => "Service status and dependency monitoring",
        }
    }
    
    pub fn category(&self) -> &'static str {
        match self {
            MonitoringAppType::SystemMetrics | MonitoringAppType::ResourceMonitoring | MonitoringAppType::PerformanceProfiler => "System",
            MonitoringAppType::NetworkMonitoring | MonitoringAppType::BandwidthMonitor => "Network",
            MonitoringAppType::ProcessMonitoring | MonitoringAppType::ServiceMonitor => "Services",
            MonitoringAppType::LogAnalyzer | MonitoringAppType::SecurityMonitor => "Security",
            MonitoringAppType::AlertManager | MonitoringAppType::HealthChecker | MonitoringAppType::UptimeMonitor => "Alerting",
        }
    }
}

/// Alert severity levels
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum AlertSeverity {
    Critical,
    Warning,
    Info,
    Debug,
}

impl AlertSeverity {
    pub fn display_name(&self) -> &'static str {
        match self {
            AlertSeverity::Critical => "Critical",
            AlertSeverity::Warning => "Warning",
            AlertSeverity::Info => "Info",
            AlertSeverity::Debug => "Debug",
        }
    }
    
    pub fn css_class(&self) -> &'static str {
        match self {
            AlertSeverity::Critical => "text-red-600 bg-red-100",
            AlertSeverity::Warning => "text-yellow-600 bg-yellow-100",
            AlertSeverity::Info => "text-blue-600 bg-blue-100",
            AlertSeverity::Debug => "text-gray-600 bg-gray-100",
        }
    }
    
    pub fn icon(&self) -> &'static str {
        match self {
            AlertSeverity::Critical => "🔴",
            AlertSeverity::Warning => "🟡",
            AlertSeverity::Info => "🔵",
            AlertSeverity::Debug => "⚪",
        }
    }
}

/// System alert
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemAlert {
    pub id: String,
    pub title: String,
    pub message: String,
    pub severity: AlertSeverity,
    pub source: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub acknowledged: bool,
    pub resolved: bool,
    pub metadata: HashMap<String, String>,
}

/// Monitoring metric
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitoringMetric {
    pub name: String,
    pub value: f64,
    pub unit: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub threshold_warning: Option<f64>,
    pub threshold_critical: Option<f64>,
}

impl MonitoringMetric {
    pub fn status(&self) -> AlertSeverity {
        if let Some(critical) = self.threshold_critical {
            if self.value >= critical {
                return AlertSeverity::Critical;
            }
        }
        if let Some(warning) = self.threshold_warning {
            if self.value >= warning {
                return AlertSeverity::Warning;
            }
        }
        AlertSeverity::Info
    }
}

/// System monitoring application
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitoringApp {
    pub id: String,
    pub name: String,
    pub app_type: MonitoringAppType,
    pub enabled: bool,
    pub running: bool,
    pub config: HashMap<String, String>,
    pub metrics: Vec<MonitoringMetric>,
    pub alerts: Vec<SystemAlert>,
    pub check_interval: u32, // seconds
    pub retention_days: u32,
    pub last_check: Option<chrono::DateTime<chrono::Utc>>,
    pub uptime: Option<std::time::Duration>,
    pub data_points_collected: u64,
    pub alerts_generated: u64,
}

impl MonitoringApp {
    pub fn new(app_type: MonitoringAppType) -> Self {
        Self {
            id: format!("{:?}", app_type).to_lowercase().replace("_", "-"),
            name: app_type.display_name().to_string(),
            app_type: app_type.clone(),
            enabled: false,
            running: false,
            config: HashMap::new(),
            metrics: Vec::new(),
            alerts: Vec::new(),
            check_interval: match app_type {
                MonitoringAppType::SystemMetrics => 30,
                MonitoringAppType::NetworkMonitoring => 60,
                MonitoringAppType::ProcessMonitoring => 15,
                MonitoringAppType::ResourceMonitoring => 30,
                MonitoringAppType::LogAnalyzer => 300,
                MonitoringAppType::PerformanceProfiler => 60,
                MonitoringAppType::AlertManager => 10,
                MonitoringAppType::HealthChecker => 120,
                MonitoringAppType::UptimeMonitor => 60,
                MonitoringAppType::BandwidthMonitor => 30,
                MonitoringAppType::SecurityMonitor => 60,
                MonitoringAppType::ServiceMonitor => 30,
            },
            retention_days: 30,
            last_check: None,
            uptime: None,
            data_points_collected: 0,
            alerts_generated: 0,
        }
    }
    
    pub fn active_alerts(&self) -> usize {
        self.alerts.iter().filter(|alert| !alert.resolved).count()
    }
    
    pub fn critical_alerts(&self) -> usize {
        self.alerts.iter().filter(|alert| !alert.resolved && alert.severity == AlertSeverity::Critical).count()
    }
}

/// System monitoring statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitoringStats {
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub total_monitors: u32,
    pub active_monitors: u32,
    pub total_alerts: u32,
    pub active_alerts: u32,
    pub critical_alerts: u32,
    pub data_points_per_minute: f64,
    pub system_health_score: f64,
    pub top_alerts: Vec<(String, u32)>,
    pub monitor_distribution: HashMap<String, u32>,
}

/// System monitoring applications manager component
#[component]
pub fn SystemMonitoringAppsManager(
    #[prop(optional)] class: Option<String>,
) -> impl IntoView {
    let (apps, set_apps) = create_signal(get_default_monitoring_apps());
    let (stats, set_stats) = create_signal(get_mock_monitoring_stats());
    let (selected_app, set_selected_app) = create_signal(None::<String>);
    let (loading, set_loading) = create_signal(false);
    let (error, set_error) = create_signal(None::<String>);
    
    // Load monitoring apps on mount
    create_effect(move |_| {
        spawn_local(async move {
            set_loading.set(true);
            set_error.set(None);
            
            match load_monitoring_apps().await {
                Ok(loaded_apps) => {
                    set_apps.set(loaded_apps);
                }
                Err(e) => {
                    set_error.set(Some(e));
                }
            }
            
            set_loading.set(false);
        });
    });
    
    // Auto-refresh stats every 15 seconds
    create_effect(move |_| {
        let interval = gloo_timers::callback::Interval::new(15000, move || {
            spawn_local(async move {
                if let Ok(new_stats) = load_monitoring_stats().await {
                    set_stats.set(new_stats);
                }
            });
        });
        
        // Keep interval alive
        std::mem::forget(interval);
    });
    
    view! {
        <div class=format!("system-monitoring-apps-manager {}", class.unwrap_or_default())>
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-lg font-semibold text-gray-900">System Monitoring Applications</h2>
                            <p class="mt-1 text-sm text-gray-600">Advanced monitoring with alerting and performance analysis</p>
                        </div>
                        <div class="flex space-x-2">
                            <button 
                                class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
                                on:click=move |_| {
                                    spawn_local(async move {
                                        if let Ok(refreshed) = load_monitoring_apps().await {
                                            set_apps.set(refreshed);
                                        }
                                    });
                                }
                            >
                                🔄 Refresh
                            </button>
                            <button class="bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700 transition-colors">
                                ➕ Add Monitor
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="p-6">
                    {move || {
                        if loading.get() {
                            view! {
                                <div class="flex items-center justify-center py-8">
                                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                    <span class="ml-3 text-gray-600">Loading monitoring applications...</span>
                                </div>
                            }.into_view()
                        } else if let Some(err) = error.get() {
                            view! {
                                <div class="bg-red-50 border border-red-200 rounded-md p-4">
                                    <div class="flex">
                                        <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                                        </svg>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-red-800">Monitoring Apps Loading Error</h3>
                                            <p class="mt-1 text-sm text-red-700">{err}</p>
                                        </div>
                                    </div>
                                </div>
                            }.into_view()
                        } else {
                            view! {
                                <div class="space-y-6">
                                    // Monitoring overview
                                    <MonitoringOverview apps=apps stats=stats />
                                    
                                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                        // Monitoring apps list
                                        <div>
                                            <MonitoringAppsList 
                                                apps=apps
                                                selected_app=selected_app
                                                on_select=move |id: String| set_selected_app.set(Some(id))
                                                on_app_action=move |action: MonitoringAppAction| {
                                                    spawn_local(async move {
                                                        if let Ok(updated) = handle_monitoring_app_action(action).await {
                                                            set_apps.set(updated);
                                                        }
                                                    });
                                                }
                                            />
                                        </div>
                                        
                                        // System alerts
                                        <div>
                                            <SystemAlertsList apps=apps selected_app=selected_app />
                                        </div>
                                    </div>
                                </div>
                            }.into_view()
                        }
                    }}
                </div>
            </div>
        </div>
    }
}

/// Monitoring app action types
#[derive(Debug, Clone)]
pub enum MonitoringAppAction {
    Enable(String),
    Disable(String),
    Start(String),
    Stop(String),
    Restart(String),
    Configure(String, HashMap<String, String>),
    AcknowledgeAlert(String, String),
    ResolveAlert(String, String),
    ClearAlerts(String),
    UpdateThresholds(String, Vec<MonitoringMetric>),
}

/// Monitoring overview component
#[component]
fn MonitoringOverview(
    apps: ReadSignal<Vec<MonitoringApp>>,
    stats: ReadSignal<MonitoringStats>,
) -> impl IntoView {
    view! {
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {move || {
                let app_list = apps.get();
                let current_stats = stats.get();

                let active_monitors = app_list.iter().filter(|app| app.running).count();
                let total_alerts = app_list.iter().map(|app| app.alerts.len()).sum::<usize>();
                let critical_alerts = app_list.iter().map(|app| app.critical_alerts()).sum::<usize>();
                let health_score = current_stats.system_health_score;

                view! {
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-blue-600">Active Monitors</p>
                                <p class="text-2xl font-semibold text-blue-900">{active_monitors}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-yellow-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-yellow-600">Total Alerts</p>
                                <p class="text-2xl font-semibold text-yellow-900">{total_alerts}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-red-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-red-600">Critical Alerts</p>
                                <p class="text-2xl font-semibold text-red-900">{critical_alerts}</p>
                            </div>
                        </div>
                    </div>

                    <div class=format!(
                        "p-4 rounded-lg {}",
                        if health_score >= 90.0 {
                            "bg-green-50"
                        } else if health_score >= 70.0 {
                            "bg-yellow-50"
                        } else {
                            "bg-red-50"
                        }
                    )>
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class=format!(
                                    "h-8 w-8 {}",
                                    if health_score >= 90.0 {
                                        "text-green-600"
                                    } else if health_score >= 70.0 {
                                        "text-yellow-600"
                                    } else {
                                        "text-red-600"
                                    }
                                ) fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class=format!(
                                    "text-sm font-medium {}",
                                    if health_score >= 90.0 {
                                        "text-green-600"
                                    } else if health_score >= 70.0 {
                                        "text-yellow-600"
                                    } else {
                                        "text-red-600"
                                    }
                                )>System Health</p>
                                <p class=format!(
                                    "text-2xl font-semibold {}",
                                    if health_score >= 90.0 {
                                        "text-green-900"
                                    } else if health_score >= 70.0 {
                                        "text-yellow-900"
                                    } else {
                                        "text-red-900"
                                    }
                                )>{format!("{:.1}%", health_score)}</p>
                            </div>
                        </div>
                    </div>
                }
            }}
        </div>
    }
}

/// Monitoring apps list component
#[component]
fn MonitoringAppsList(
    apps: ReadSignal<Vec<MonitoringApp>>,
    selected_app: ReadSignal<Option<String>>,
    on_select: impl Fn(String) + 'static + Copy,
    on_app_action: impl Fn(MonitoringAppAction) + 'static + Copy,
) -> impl IntoView {
    view! {
        <div class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900">Monitoring Applications</h3>

            <div class="space-y-2">
                {move || {
                    let app_list = apps.get();

                    if app_list.is_empty() {
                        view! {
                            <div class="text-center py-8 text-gray-500">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                                </svg>
                                <p class="mt-2">No monitoring applications configured</p>
                            </div>
                        }.into_view()
                    } else {
                        app_list.into_iter().map(|app| {
                            let app_id = app.id.clone();
                            let is_selected = selected_app.get().as_ref() == Some(&app.id);

                            view! {
                                <MonitoringAppCard
                                    app=app
                                    is_selected=is_selected
                                    on_select=move || on_select(app_id.clone())
                                    on_action=on_app_action
                                />
                            }
                        }).collect::<Vec<_>>()
                    }
                }}
            </div>
        </div>
    }
}

/// Monitoring app card component
#[component]
fn MonitoringAppCard(
    app: MonitoringApp,
    is_selected: bool,
    on_select: impl Fn() + 'static + Copy,
    on_action: impl Fn(MonitoringAppAction) + 'static + Copy,
) -> impl IntoView {
    let app_id = app.id.clone();

    view! {
        <div
            class=move || format!(
                "border rounded-lg p-4 cursor-pointer transition-colors {}",
                if is_selected {
                    "border-blue-500 bg-blue-50"
                } else {
                    "border-gray-200 hover:border-gray-300 bg-white"
                }
            )
            on:click=move |_| on_select()
        >
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <span class="text-2xl">{app.app_type.icon()}</span>
                    <div>
                        <h4 class="font-medium text-gray-900">{app.name.clone()}</h4>
                        <p class="text-sm text-gray-500">{app.app_type.category()} • Every {app.check_interval}s</p>
                    </div>
                </div>

                <div class="flex items-center space-x-2">
                    <div class="flex items-center space-x-1">
                        {if app.enabled {
                            view! {
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-green-600 bg-green-100">
                                    ✓ Enabled
                                </span>
                            }.into_view()
                        } else {
                            view! {
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-gray-600 bg-gray-100">
                                    ○ Disabled
                                </span>
                            }.into_view()
                        }}

                        {if app.running {
                            view! {
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-blue-600 bg-blue-100">
                                    ▶️ Running
                                </span>
                            }.into_view()
                        } else {
                            view! {
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-gray-600 bg-gray-100">
                                    ⏹️ Stopped
                                </span>
                            }.into_view()
                        }}

                        {if app.active_alerts() > 0 {
                            view! {
                                <span class=format!(
                                    "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {}",
                                    if app.critical_alerts() > 0 {
                                        "text-red-600 bg-red-100"
                                    } else {
                                        "text-yellow-600 bg-yellow-100"
                                    }
                                )>
                                    🚨 {app.active_alerts()}
                                </span>
                            }.into_view()
                        } else {
                            view! { <span></span> }.into_view()
                        }}
                    </div>

                    <div class="flex space-x-1">
                        <button
                            class="p-1 text-blue-600 hover:bg-blue-100 rounded"
                            title="Restart monitor"
                            on:click=move |e| {
                                e.stop_propagation();
                                on_action(MonitoringAppAction::Restart(app_id.clone()));
                            }
                        >
                            🔄
                        </button>

                        {if app.active_alerts() > 0 {
                            view! {
                                <button
                                    class="p-1 text-orange-600 hover:bg-orange-100 rounded"
                                    title="Clear alerts"
                                    on:click=move |e| {
                                        e.stop_propagation();
                                        on_action(MonitoringAppAction::ClearAlerts(app_id.clone()));
                                    }
                                >
                                    🧹
                                </button>
                            }.into_view()
                        } else {
                            view! { <div></div> }.into_view()
                        }}
                    </div>
                </div>
            </div>

            <div class="mt-3 grid grid-cols-3 gap-4 text-sm text-gray-500">
                <div>
                    <span class="font-medium">Metrics:</span> {app.metrics.len()}
                </div>
                <div>
                    <span class="font-medium">Data Points:</span> {format_number(app.data_points_collected)}
                </div>
                <div>
                    <span class="font-medium">Alerts:</span> {app.alerts_generated}
                </div>
            </div>

            {if let Some(last_check) = app.last_check {
                let time_ago = chrono::Utc::now() - last_check;
                view! {
                    <div class="mt-2 text-xs text-gray-400">
                        Last check: {format_time_ago(time_ago)}
                    </div>
                }.into_view()
            } else {
                view! {
                    <div class="mt-2 text-xs text-gray-400">
                        Never checked
                    </div>
                }.into_view()
            }}
        </div>
    }
}

/// System alerts list component
#[component]
fn SystemAlertsList(
    apps: ReadSignal<Vec<MonitoringApp>>,
    selected_app: ReadSignal<Option<String>>,
) -> impl IntoView {
    view! {
        <div class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900">System Alerts</h3>

            <div class="space-y-2 max-h-96 overflow-y-auto">
                {move || {
                    let app_list = apps.get();
                    let selected_id = selected_app.get();

                    let alerts: Vec<(String, SystemAlert)> = if let Some(app_id) = selected_id {
                        app_list.iter()
                            .find(|app| app.id == app_id)
                            .map(|app| app.alerts.iter().map(|alert| (app.name.clone(), alert.clone())).collect())
                            .unwrap_or_default()
                    } else {
                        app_list.iter()
                            .flat_map(|app| app.alerts.iter().map(|alert| (app.name.clone(), alert.clone())))
                            .collect()
                    };

                    // Sort alerts by severity and timestamp
                    let mut sorted_alerts = alerts;
                    sorted_alerts.sort_by(|a, b| {
                        let severity_order = |s: &AlertSeverity| match s {
                            AlertSeverity::Critical => 0,
                            AlertSeverity::Warning => 1,
                            AlertSeverity::Info => 2,
                            AlertSeverity::Debug => 3,
                        };

                        severity_order(&a.1.severity).cmp(&severity_order(&b.1.severity))
                            .then(b.1.timestamp.cmp(&a.1.timestamp))
                    });

                    if sorted_alerts.is_empty() {
                        view! {
                            <div class="text-center py-8 text-gray-500">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <p class="mt-2">No system alerts</p>
                            </div>
                        }.into_view()
                    } else {
                        sorted_alerts.into_iter().take(10).map(|(source, alert)| {
                            view! {
                                <SystemAlertCard source=source alert=alert />
                            }
                        }).collect::<Vec<_>>()
                    }
                }}
            </div>
        </div>
    }
}

/// System alert card component
#[component]
fn SystemAlertCard(
    source: String,
    alert: SystemAlert,
) -> impl IntoView {
    view! {
        <div class=format!(
            "border rounded-lg p-3 transition-colors {}",
            if alert.resolved {
                "bg-gray-50 border-gray-200"
            } else {
                "bg-white border-gray-200 hover:bg-gray-50"
            }
        )>
            <div class="flex items-start justify-between">
                <div class="flex items-start space-x-3">
                    <span class="text-lg">{alert.severity.icon()}</span>
                    <div class="flex-1">
                        <div class="flex items-center space-x-2">
                            <h4 class=format!(
                                "font-medium {}",
                                if alert.resolved { "text-gray-500" } else { "text-gray-900" }
                            )>{alert.title.clone()}</h4>
                            <span class=format!(
                                "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {}",
                                alert.severity.css_class()
                            )>
                                {alert.severity.display_name()}
                            </span>
                            <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                                {source}
                            </span>
                        </div>
                        <p class=format!(
                            "text-sm mt-1 {}",
                            if alert.resolved { "text-gray-400" } else { "text-gray-600" }
                        )>{alert.message.clone()}</p>
                        <div class="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                            <span>{format_time_ago(chrono::Utc::now() - alert.timestamp)}</span>
                            {if alert.acknowledged {
                                view! {
                                    <span class="text-blue-600">✓ Acknowledged</span>
                                }.into_view()
                            } else {
                                view! { <span></span> }.into_view()
                            }}
                            {if alert.resolved {
                                view! {
                                    <span class="text-green-600">✅ Resolved</span>
                                }.into_view()
                            } else {
                                view! { <span></span> }.into_view()
                            }}
                        </div>
                    </div>
                </div>

                {if !alert.resolved {
                    view! {
                        <div class="flex space-x-1">
                            {if !alert.acknowledged {
                                view! {
                                    <button class="p-1 text-blue-600 hover:bg-blue-100 rounded" title="Acknowledge">
                                        ✓
                                    </button>
                                }.into_view()
                            } else {
                                view! { <div></div> }.into_view()
                            }}
                            <button class="p-1 text-green-600 hover:bg-green-100 rounded" title="Resolve">
                                ✅
                            </button>
                        </div>
                    }.into_view()
                } else {
                    view! { <div></div> }.into_view()
                }}
            </div>
        </div>
    }
}

/// Utility function to format numbers
fn format_number(num: u64) -> String {
    if num >= 1_000_000 {
        format!("{:.1}M", num as f64 / 1_000_000.0)
    } else if num >= 1_000 {
        format!("{:.1}K", num as f64 / 1_000.0)
    } else {
        num.to_string()
    }
}

/// Utility function to format time ago
fn format_time_ago(duration: chrono::Duration) -> String {
    let total_seconds = duration.num_seconds();

    if total_seconds < 60 {
        format!("{}s ago", total_seconds)
    } else if total_seconds < 3600 {
        format!("{}m ago", total_seconds / 60)
    } else if total_seconds < 86400 {
        format!("{}h ago", total_seconds / 3600)
    } else {
        format!("{}d ago", total_seconds / 86400)
    }
}

/// Get default monitoring applications
fn get_default_monitoring_apps() -> Vec<MonitoringApp> {
    let mut apps = vec![
        MonitoringApp::new(MonitoringAppType::SystemMetrics),
        MonitoringApp::new(MonitoringAppType::NetworkMonitoring),
        MonitoringApp::new(MonitoringAppType::ProcessMonitoring),
        MonitoringApp::new(MonitoringAppType::ResourceMonitoring),
        MonitoringApp::new(MonitoringAppType::LogAnalyzer),
        MonitoringApp::new(MonitoringAppType::AlertManager),
        MonitoringApp::new(MonitoringAppType::HealthChecker),
        MonitoringApp::new(MonitoringAppType::UptimeMonitor),
    ];

    // Configure System Metrics with realistic settings
    if let Some(system_metrics) = apps.iter_mut().find(|app| app.app_type == MonitoringAppType::SystemMetrics) {
        system_metrics.enabled = true;
        system_metrics.running = true;
        system_metrics.data_points_collected = 125_000;
        system_metrics.alerts_generated = 15;
        system_metrics.last_check = Some(chrono::Utc::now() - chrono::Duration::seconds(25));
        system_metrics.uptime = Some(std::time::Duration::from_secs(86400 * 5)); // 5 days

        system_metrics.metrics = vec![
            MonitoringMetric {
                name: "CPU Usage".to_string(),
                value: 45.2,
                unit: "%".to_string(),
                timestamp: chrono::Utc::now(),
                threshold_warning: Some(70.0),
                threshold_critical: Some(90.0),
            },
            MonitoringMetric {
                name: "Memory Usage".to_string(),
                value: 68.7,
                unit: "%".to_string(),
                timestamp: chrono::Utc::now(),
                threshold_warning: Some(80.0),
                threshold_critical: Some(95.0),
            },
            MonitoringMetric {
                name: "Disk Usage".to_string(),
                value: 23.4,
                unit: "%".to_string(),
                timestamp: chrono::Utc::now(),
                threshold_warning: Some(85.0),
                threshold_critical: Some(95.0),
            },
        ];

        system_metrics.alerts = vec![
            SystemAlert {
                id: "alert-cpu-high".to_string(),
                title: "High CPU Usage".to_string(),
                message: "CPU usage has been above 80% for the last 5 minutes".to_string(),
                severity: AlertSeverity::Warning,
                source: "System Metrics".to_string(),
                timestamp: chrono::Utc::now() - chrono::Duration::minutes(12),
                acknowledged: false,
                resolved: false,
                metadata: HashMap::from([
                    ("cpu_usage".to_string(), "85.3".to_string()),
                    ("duration".to_string(), "5m".to_string()),
                ]),
            },
        ];
    }

    // Configure Network Monitoring
    if let Some(network_mon) = apps.iter_mut().find(|app| app.app_type == MonitoringAppType::NetworkMonitoring) {
        network_mon.enabled = true;
        network_mon.running = true;
        network_mon.data_points_collected = 89_000;
        network_mon.alerts_generated = 8;
        network_mon.last_check = Some(chrono::Utc::now() - chrono::Duration::seconds(45));

        network_mon.metrics = vec![
            MonitoringMetric {
                name: "Network Throughput".to_string(),
                value: 125.6,
                unit: "Mbps".to_string(),
                timestamp: chrono::Utc::now(),
                threshold_warning: Some(800.0),
                threshold_critical: Some(950.0),
            },
            MonitoringMetric {
                name: "Packet Loss".to_string(),
                value: 0.2,
                unit: "%".to_string(),
                timestamp: chrono::Utc::now(),
                threshold_warning: Some(1.0),
                threshold_critical: Some(5.0),
            },
        ];
    }

    // Configure Alert Manager
    if let Some(alert_mgr) = apps.iter_mut().find(|app| app.app_type == MonitoringAppType::AlertManager) {
        alert_mgr.enabled = true;
        alert_mgr.running = true;
        alert_mgr.data_points_collected = 45_000;
        alert_mgr.alerts_generated = 234;
        alert_mgr.last_check = Some(chrono::Utc::now() - chrono::Duration::seconds(8));

        alert_mgr.alerts = vec![
            SystemAlert {
                id: "alert-disk-space".to_string(),
                title: "Low Disk Space".to_string(),
                message: "Root filesystem is 92% full".to_string(),
                severity: AlertSeverity::Critical,
                source: "Alert Manager".to_string(),
                timestamp: chrono::Utc::now() - chrono::Duration::minutes(3),
                acknowledged: true,
                resolved: false,
                metadata: HashMap::from([
                    ("filesystem".to_string(), "/".to_string()),
                    ("usage_percent".to_string(), "92".to_string()),
                ]),
            },
            SystemAlert {
                id: "alert-service-down".to_string(),
                title: "Service Unavailable".to_string(),
                message: "SSH service is not responding".to_string(),
                severity: AlertSeverity::Critical,
                source: "Alert Manager".to_string(),
                timestamp: chrono::Utc::now() - chrono::Duration::minutes(8),
                acknowledged: false,
                resolved: false,
                metadata: HashMap::from([
                    ("service".to_string(), "ssh".to_string()),
                    ("port".to_string(), "22".to_string()),
                ]),
            },
        ];
    }

    apps
}

/// Get mock monitoring statistics
fn get_mock_monitoring_stats() -> MonitoringStats {
    MonitoringStats {
        timestamp: chrono::Utc::now(),
        total_monitors: 8,
        active_monitors: 6,
        total_alerts: 23,
        active_alerts: 5,
        critical_alerts: 2,
        data_points_per_minute: 45.7,
        system_health_score: 78.5,
        top_alerts: vec![
            ("High CPU Usage".to_string(), 8),
            ("Low Disk Space".to_string(), 5),
            ("Service Unavailable".to_string(), 4),
            ("Network Timeout".to_string(), 3),
            ("Memory Leak".to_string(), 3),
        ],
        monitor_distribution: HashMap::from([
            ("System".to_string(), 3),
            ("Network".to_string(), 2),
            ("Services".to_string(), 2),
            ("Alerting".to_string(), 1),
        ]),
    }
}

/// Load monitoring applications from system (mock implementation)
async fn load_monitoring_apps() -> Result<Vec<MonitoringApp>, String> {
    // Simulate network delay
    gloo_timers::future::TimeoutFuture::new(900).await;

    // In a real implementation, this would query the monitoring system
    // For now, return mock data
    Ok(get_default_monitoring_apps())
}

/// Load monitoring statistics (mock implementation)
async fn load_monitoring_stats() -> Result<MonitoringStats, String> {
    // Simulate network delay
    gloo_timers::future::TimeoutFuture::new(600).await;

    // In a real implementation, this would query monitoring statistics
    // For now, return mock data with some variation
    let mut stats = get_mock_monitoring_stats();

    // Add some random variation to make it look live
    use js_sys::Math;
    stats.data_points_per_minute = 40.0 + (Math::random() * 20.0);
    stats.system_health_score = 70.0 + (Math::random() * 25.0);
    stats.active_alerts = 3 + (Math::random() * 5.0) as u32;
    stats.critical_alerts = (Math::random() * 3.0) as u32;

    Ok(stats)
}

/// Handle monitoring app actions (mock implementation)
async fn handle_monitoring_app_action(action: MonitoringAppAction) -> Result<Vec<MonitoringApp>, String> {
    // Simulate processing delay
    gloo_timers::future::TimeoutFuture::new(1000).await;

    match action {
        MonitoringAppAction::Enable(app_id) => {
            log::info!("Enabling monitoring app: {}", app_id);
            // In real implementation, would enable the monitoring service
        }
        MonitoringAppAction::Disable(app_id) => {
            log::info!("Disabling monitoring app: {}", app_id);
            // In real implementation, would disable the monitoring service
        }
        MonitoringAppAction::Start(app_id) => {
            log::info!("Starting monitoring app: {}", app_id);
            // In real implementation, would start the monitoring service
        }
        MonitoringAppAction::Stop(app_id) => {
            log::info!("Stopping monitoring app: {}", app_id);
            // In real implementation, would stop the monitoring service
        }
        MonitoringAppAction::Restart(app_id) => {
            log::info!("Restarting monitoring app: {}", app_id);
            // In real implementation, would restart the monitoring service
        }
        MonitoringAppAction::Configure(app_id, config) => {
            log::info!("Configuring monitoring app: {} with {:?}", app_id, config);
            // In real implementation, would update monitoring app configuration
        }
        MonitoringAppAction::AcknowledgeAlert(app_id, alert_id) => {
            log::info!("Acknowledging alert: {} in app: {}", alert_id, app_id);
            // In real implementation, would acknowledge the alert
        }
        MonitoringAppAction::ResolveAlert(app_id, alert_id) => {
            log::info!("Resolving alert: {} in app: {}", alert_id, app_id);
            // In real implementation, would resolve the alert
        }
        MonitoringAppAction::ClearAlerts(app_id) => {
            log::info!("Clearing all alerts for app: {}", app_id);
            // In real implementation, would clear all alerts for the app
        }
        MonitoringAppAction::UpdateThresholds(app_id, metrics) => {
            log::info!("Updating thresholds for app: {} with {} metrics", app_id, metrics.len());
            // In real implementation, would update monitoring thresholds
        }
    }

    // Return updated apps list
    load_monitoring_apps().await
}
