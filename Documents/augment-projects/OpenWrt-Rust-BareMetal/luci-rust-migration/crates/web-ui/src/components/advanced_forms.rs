//! Advanced Form Components
//! 
//! Provides sophisticated form components with:
//! - Complex validation rules and real-time validation
//! - Conditional field display based on other field values
//! - Dynamic form generation from configuration
//! - Multi-step form wizards
//! - Form state management and persistence
//! - Advanced input types and custom validators

use leptos::*;
use leptos::ev::SubmitEvent;
use web_sys::{HtmlInputElement, HtmlSelectElement, HtmlTextAreaElement};
use wasm_bindgen::JsCast;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::rc::Rc;

use crate::components::buttons::{Button, ButtonVariant, ButtonSize};
use crate::components::alerts::{Alert, AlertVariant};
use crate::components::loading::LoadingSpinner;

/// Field validation result
pub type ValidationResult = Result<(), String>;

/// Field validator function type
pub type FieldValidator = Rc<dyn Fn(&str) -> ValidationResult>;

/// Conditional display rule
pub type ConditionalRule = Rc<dyn Fn(&HashMap<String, String>) -> bool>;

/// Field value change handler
pub type FieldChangeHandler = Rc<dyn Fn(&str, &str)>; // (field_name, new_value)

/// Advanced form field configuration
#[derive(Clone)]
pub struct FormFieldConfig {
    pub name: String,
    pub label: String,
    pub field_type: FormFieldType,
    pub required: bool,
    pub disabled: bool,
    pub placeholder: Option<String>,
    pub help_text: Option<String>,
    pub default_value: Option<String>,
    pub validators: Vec<FieldValidator>,
    pub conditional_rule: Option<ConditionalRule>,
    pub options: Vec<SelectOption>, // For select/radio fields
    pub attributes: HashMap<String, String>, // Custom HTML attributes
}

/// Form field types
#[derive(Debug, Clone, PartialEq)]
pub enum FormFieldType {
    Text,
    Password,
    Email,
    Number,
    Tel,
    Url,
    Search,
    Textarea,
    Select,
    MultiSelect,
    Radio,
    Checkbox,
    File,
    Date,
    Time,
    DateTime,
    Range,
    Color,
    Hidden,
    Custom(String), // Custom field type identifier
}

/// Select option for dropdowns and radio groups
#[derive(Debug, Clone, PartialEq)]
pub struct SelectOption {
    pub value: String,
    pub label: String,
    pub disabled: bool,
    pub group: Option<String>, // For option groups
}

/// Form validation state
#[derive(Debug, Clone, PartialEq)]
pub struct FormValidationState {
    pub is_valid: bool,
    pub field_errors: HashMap<String, String>,
    pub form_errors: Vec<String>,
    pub is_validating: bool,
}

impl Default for FormValidationState {
    fn default() -> Self {
        Self {
            is_valid: true,
            field_errors: HashMap::new(),
            form_errors: Vec::new(),
            is_validating: false,
        }
    }
}

/// Form configuration for dynamic form generation
#[derive(Clone)]
pub struct DynamicFormConfig {
    pub title: String,
    pub description: Option<String>,
    pub fields: Vec<FormFieldConfig>,
    pub submit_text: String,
    pub cancel_text: Option<String>,
    pub validation_mode: ValidationMode,
    pub layout: FormLayout,
    pub on_submit: Option<Rc<dyn Fn(HashMap<String, String>) -> ValidationResult>>,
    pub on_cancel: Option<Rc<dyn Fn()>>,
    pub on_field_change: Option<FieldChangeHandler>,
}

/// Form validation modes
#[derive(Debug, Clone, PartialEq)]
pub enum ValidationMode {
    OnSubmit,      // Validate only when form is submitted
    OnBlur,        // Validate when field loses focus
    OnChange,      // Validate on every field change
    Hybrid,        // OnBlur for first validation, then OnChange
}

/// Form layout options
#[derive(Debug, Clone, PartialEq)]
pub enum FormLayout {
    Vertical,      // Stack fields vertically
    Horizontal,    // Label and input side by side
    Grid(u32),     // Grid layout with specified columns
    Custom,        // Custom layout handled by parent
}

/// Multi-step form wizard configuration
#[derive(Clone)]
pub struct FormWizardConfig {
    pub title: String,
    pub steps: Vec<FormWizardStep>,
    pub allow_skip: bool,
    pub show_progress: bool,
    pub on_complete: Option<Rc<dyn Fn(HashMap<String, String>) -> ValidationResult>>,
    pub on_cancel: Option<Rc<dyn Fn()>>,
}

/// Individual wizard step
#[derive(Clone)]
pub struct FormWizardStep {
    pub title: String,
    pub description: Option<String>,
    pub fields: Vec<FormFieldConfig>,
    pub optional: bool,
    pub validation_required: bool,
}

/// Advanced form field component with validation and conditional display
#[component]
pub fn AdvancedFormField(
    config: FormFieldConfig,
    value: ReadSignal<String>,
    on_change: WriteSignal<String>,
    form_values: ReadSignal<HashMap<String, String>>,
    validation_state: ReadSignal<FormValidationState>,
    validation_mode: ValidationMode,
    #[prop(optional)] on_field_change: Option<FieldChangeHandler>,
) -> impl IntoView {
    let field_name = config.name.clone();
    let field_error = create_memo(move |_| {
        validation_state.get().field_errors.get(&field_name).cloned()
    });
    
    // Check if field should be displayed based on conditional rule
    let should_display = create_memo(move |_| {
        if let Some(rule) = &config.conditional_rule {
            rule(&form_values.get())
        } else {
            true
        }
    });
    
    // Validate field value
    let validate_field = {
        let validators = config.validators.clone();
        let field_name = config.name.clone();
        move |value: &str| -> ValidationResult {
            for validator in &validators {
                if let Err(error) = validator(value) {
                    return Err(error);
                }
            }
            Ok(())
        }
    };
    
    // Handle field value change
    let handle_change = {
        let field_name = config.name.clone();
        let validate_field = validate_field.clone();
        let validation_mode = validation_mode.clone();
        move |new_value: String| {
            on_change.set(new_value.clone());

            // Trigger field change handler if provided
            if let Some(handler) = &on_field_change {
                handler(&field_name, &new_value);
            }

            // Validate based on validation mode
            match validation_mode {
                ValidationMode::OnChange => {
                    let _ = validate_field(&new_value);
                }
                ValidationMode::Hybrid => {
                    // Implement hybrid validation logic
                    let _ = validate_field(&new_value);
                }
                _ => {} // No immediate validation
            }
        }
    };
    
    // Handle field blur for validation
    let handle_blur = {
        let validate_field = validate_field.clone();
        let validation_mode = validation_mode.clone();
        move || {
            if matches!(validation_mode, ValidationMode::OnBlur | ValidationMode::Hybrid) {
                let _ = validate_field(&value.get());
            }
        }
    };
    
    view! {
        <div class="form-field-container" style:display=move || if should_display.get() { "block" } else { "none" }>
            {match config.field_type {
                FormFieldType::Text | FormFieldType::Password | FormFieldType::Email | 
                FormFieldType::Number | FormFieldType::Tel | FormFieldType::Url | 
                FormFieldType::Search => {
                    let input_type = match config.field_type {
                        FormFieldType::Text => "text",
                        FormFieldType::Password => "password",
                        FormFieldType::Email => "email",
                        FormFieldType::Number => "number",
                        FormFieldType::Tel => "tel",
                        FormFieldType::Url => "url",
                        FormFieldType::Search => "search",
                        _ => "text",
                    };
                    
                    view! {
                        <div class="space-y-1">
                            <label class="block text-sm font-medium text-gray-700">
                                {config.label.clone()}
                                {if config.required { 
                                    view! { <span class="text-red-500 ml-1">"*"</span> } 
                                } else { 
                                    view! { <span></span> } 
                                }}
                            </label>
                            <input
                                type={input_type}
                                class=move || format!("w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 {}",
                                    if field_error.get().is_some() { "border-red-300 focus:border-red-500 focus:ring-red-500" } else { "border-gray-300" }
                                )
                                placeholder={config.placeholder.clone().unwrap_or_default()}
                                prop:value=move || value.get()
                                prop:required=config.required
                                prop:disabled=config.disabled
                                on:input=move |ev| {
                                    let target = ev.target().unwrap();
                                    let input = target.dyn_into::<HtmlInputElement>().unwrap();
                                    handle_change(input.value());
                                }
                                on:blur=move |_| handle_blur()
                            />
                            {move || field_error.get().map(|error| {
                                view! {
                                    <p class="text-sm text-red-600 mt-1 flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                                        </svg>
                                        {error}
                                    </p>
                                }
                            })}
                            {config.help_text.clone().map(|help| {
                                view! {
                                    <p class="text-sm text-gray-500 mt-1">{help}</p>
                                }
                            })}
                        </div>
                    }.into_view()
                }
                
                FormFieldType::Textarea => {
                    view! {
                        <div class="space-y-1">
                            <label class="block text-sm font-medium text-gray-700">
                                {config.label.clone()}
                                {if config.required { 
                                    view! { <span class="text-red-500 ml-1">"*"</span> } 
                                } else { 
                                    view! { <span></span> } 
                                }}
                            </label>
                            <textarea
                                class=move || format!("w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-vertical {}",
                                    if field_error.get().is_some() { "border-red-300 focus:border-red-500 focus:ring-red-500" } else { "border-gray-300" }
                                )
                                placeholder={config.placeholder.clone().unwrap_or_default()}
                                rows="4"
                                prop:required=config.required
                                prop:disabled=config.disabled
                                on:input=move |ev| {
                                    let target = ev.target().unwrap();
                                    let textarea = target.dyn_into::<HtmlTextAreaElement>().unwrap();
                                    handle_change(textarea.value());
                                }
                                on:blur=move |_| handle_blur()
                            >
                                {move || value.get()}
                            </textarea>
                            {move || field_error.get().map(|error| {
                                view! {
                                    <p class="text-sm text-red-600 mt-1 flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                                        </svg>
                                        {error}
                                    </p>
                                }
                            })}
                            {config.help_text.clone().map(|help| {
                                view! {
                                    <p class="text-sm text-gray-500 mt-1">{help}</p>
                                }
                            })}
                        </div>
                    }.into_view()
                }
                
                FormFieldType::Select => {
                    view! {
                        <div class="space-y-1">
                            <label class="block text-sm font-medium text-gray-700">
                                {config.label.clone()}
                                {if config.required { 
                                    view! { <span class="text-red-500 ml-1">"*"</span> } 
                                } else { 
                                    view! { <span></span> } 
                                }}
                            </label>
                            <select
                                class=move || format!("w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 {}",
                                    if field_error.get().is_some() { "border-red-300 focus:border-red-500 focus:ring-red-500" } else { "border-gray-300" }
                                )
                                prop:required=config.required
                                prop:disabled=config.disabled
                                on:change=move |ev| {
                                    let target = ev.target().unwrap();
                                    let select = target.dyn_into::<HtmlSelectElement>().unwrap();
                                    handle_change(select.value());
                                }
                                on:blur=move |_| handle_blur()
                            >
                                <option value="" disabled=config.required selected=move || value.get().is_empty()>
                                    {config.placeholder.clone().unwrap_or_else(|| "Select an option...".to_string())}
                                </option>
                                {config.options.iter().map(|option| {
                                    let option_value = option.value.clone();
                                    view! {
                                        <option 
                                            value={option.value.clone()}
                                            disabled={option.disabled}
                                            selected=move || value.get() == option_value
                                        >
                                            {option.label.clone()}
                                        </option>
                                    }
                                }).collect::<Vec<_>>()}
                            </select>
                            {move || field_error.get().map(|error| {
                                view! {
                                    <p class="text-sm text-red-600 mt-1 flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                                        </svg>
                                        {error}
                                    </p>
                                }
                            })}
                            {config.help_text.clone().map(|help| {
                                view! {
                                    <p class="text-sm text-gray-500 mt-1">{help}</p>
                                }
                            })}
                        </div>
                    }.into_view()
                }
                
                _ => {
                    view! {
                        <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                            <p class="text-sm text-yellow-800">
                                "Field type not yet implemented: " {format!("{:?}", config.field_type)}
                            </p>
                        </div>
                    }.into_view()
                }
            }}
        </div>
    }
}

/// Dynamic form component that generates forms from configuration
#[component]
pub fn DynamicForm(
    config: DynamicFormConfig,
    #[prop(optional)] initial_values: Option<HashMap<String, String>>,
    #[prop(optional)] on_submit_success: Option<Rc<dyn Fn(HashMap<String, String>)>>,
    #[prop(optional)] on_submit_error: Option<Rc<dyn Fn(String)>>,
) -> impl IntoView {
    // Form state
    let (form_values, set_form_values) = create_signal(initial_values.unwrap_or_default());
    let (validation_state, set_validation_state) = create_signal(FormValidationState::default());
    let (is_submitting, set_is_submitting) = create_signal(false);
    let config_fields = config.fields.clone();

    // Initialize default values
    create_effect(move |_| {
        let mut values = form_values.get();
        for field in &config_fields {
            if !values.contains_key(&field.name) {
                if let Some(default) = &field.default_value {
                    values.insert(field.name.clone(), default.clone());
                } else {
                    values.insert(field.name.clone(), String::new());
                }
            }
        }
        set_form_values.set(values);
    });

    // Field change handler
    let field_change_handler: FieldChangeHandler = {
        let config_handler = config.on_field_change.clone();
        Rc::new(move |field_name: &str, new_value: &str| {
            // Update form values
            set_form_values.update(|values| {
                values.insert(field_name.to_string(), new_value.to_string());
            });

            // Call external handler if provided
            if let Some(handler) = &config_handler {
                handler(field_name, new_value);
            }
        })
    };

    // Form validation
    let validate_form = {
        let fields = config.fields.clone();
        move || -> FormValidationState {
            let values = form_values.get();
            let mut field_errors = HashMap::new();
            let mut form_errors = Vec::new();

            for field in &fields {
                let default_value = String::new();
                let value = values.get(&field.name).unwrap_or(&default_value);

                // Skip validation for hidden fields or fields not displayed
                if let Some(rule) = &field.conditional_rule {
                    if !rule(&values) {
                        continue;
                    }
                }

                // Run field validators
                for validator in &field.validators {
                    if let Err(error) = validator(value) {
                        field_errors.insert(field.name.clone(), error);
                        break;
                    }
                }
            }

            FormValidationState {
                is_valid: field_errors.is_empty() && form_errors.is_empty(),
                field_errors,
                form_errors,
                is_validating: false,
            }
        }
    };

    // Handle form submission
    let handle_submit = {
        let config_submit = config.on_submit.clone();
        let validate_form = validate_form.clone();
        move |ev: SubmitEvent| {
            ev.prevent_default();

            set_is_submitting.set(true);
            let validation = validate_form();
            set_validation_state.set(validation.clone());

            if !validation.is_valid {
                set_is_submitting.set(false);
                return;
            }

            let values = form_values.get();

            // Call submit handler if provided
            if let Some(handler) = &config_submit {
                match handler(values.clone()) {
                    Ok(()) => {
                        if let Some(success_handler) = &on_submit_success {
                            success_handler(values);
                        }
                    }
                    Err(error) => {
                        set_validation_state.update(|state| {
                            state.form_errors.push(error.clone());
                            state.is_valid = false;
                        });
                        if let Some(error_handler) = &on_submit_error {
                            error_handler(error);
                        }
                    }
                }
            }

            set_is_submitting.set(false);
        }
    };

    // Handle cancel
    let handle_cancel = {
        let config_cancel = config.on_cancel.clone();
        move || {
            if let Some(handler) = &config_cancel {
                handler();
            }
        }
    };

    view! {
        <div class="dynamic-form">
            <div class="mb-6">
                <h3 class="text-lg font-semibold text-gray-900">{config.title.clone()}</h3>
                {config.description.clone().map(|desc| {
                    view! {
                        <p class="text-sm text-gray-600 mt-1">{desc}</p>
                    }
                })}
            </div>

            // Display form-level errors
            {move || {
                let errors = validation_state.get().form_errors;
                if !errors.is_empty() {
                    view! {
                        <div class="mb-4">
                            {errors.into_iter().map(|error| {
                                view! {
                                    <Alert variant=AlertVariant::Error>
                                        {error}
                                    </Alert>
                                }
                            }).collect::<Vec<_>>()}
                        </div>
                    }.into_view()
                } else {
                    view! { <div></div> }.into_view()
                }
            }}

            <form on:submit=handle_submit>
                <div class=move || match config.layout {
                    FormLayout::Vertical => "space-y-4".to_string(),
                    FormLayout::Horizontal => "space-y-4".to_string(), // TODO: Implement horizontal layout
                    FormLayout::Grid(cols) => format!("grid grid-cols-{} gap-4", cols),
                    FormLayout::Custom => "".to_string(),
                }>
                    {config.fields.iter().map(|field| {
                        let field_name = field.name.clone();
                        let field_config = field.clone();

                        // Create signals for this field
                        let initial_value = form_values.get().get(&field_name).cloned().unwrap_or_default();
                        let (field_value, field_setter) = create_signal(initial_value);

                        view! {
                            <AdvancedFormField
                                config=field_config
                                value=field_value
                                on_change=field_setter
                                form_values=form_values
                                validation_state=validation_state
                                validation_mode=config.validation_mode.clone()
                                on_field_change=field_change_handler.clone()
                            />
                        }
                    }).collect::<Vec<_>>()}
                </div>

                <div class="flex justify-between items-center pt-6 mt-6 border-t border-gray-200">
                    {config.cancel_text.clone().map(|cancel_text| {
                        view! {
                            <Button
                                variant=ButtonVariant::Secondary
                                size=ButtonSize::Medium
                                on_click=Box::new(handle_cancel)
                                disabled=is_submitting.get()
                            >
                                {cancel_text}
                            </Button>
                        }.into_view()
                    }).unwrap_or_else(|| view! { <div></div> }.into_view())}

                    <Button
                        variant=ButtonVariant::Primary
                        size=ButtonSize::Medium
                        button_type="submit".to_string()
                        disabled=is_submitting.get()
                    >
                        {move || if is_submitting.get() {
                            view! {
                                <div class="flex items-center space-x-2">
                                    <LoadingSpinner size="small".to_string() />
                                    <span>"Submitting..."</span>
                                </div>
                            }.into_view()
                        } else {
                            view! { <span>{config.submit_text.clone()}</span> }.into_view()
                        }}
                    </Button>
                </div>
            </form>
        </div>
    }
}

/// Multi-step form wizard component
#[component]
pub fn FormWizard(
    config: FormWizardConfig,
    #[prop(optional)] initial_values: Option<HashMap<String, String>>,
    #[prop(optional)] on_complete_success: Option<Rc<dyn Fn(HashMap<String, String>)>>,
    #[prop(optional)] on_complete_error: Option<Rc<dyn Fn(String)>>,
) -> impl IntoView {
    let (current_step, set_current_step) = create_signal(0usize);
    let (form_values, set_form_values) = create_signal(initial_values.unwrap_or_default());
    let (step_validation_states, set_step_validation_states) = create_signal(
        vec![FormValidationState::default(); config.steps.len()]
    );
    let (is_completing, set_is_completing) = create_signal(false);

    let total_steps = config.steps.len();

    // Validate current step
    let validate_current_step = {
        let steps = config.steps.clone();
        move || -> FormValidationState {
            let current = current_step.get();
            if current >= steps.len() {
                return FormValidationState::default();
            }

            let step = &steps[current];
            let values = form_values.get();
            let mut field_errors = HashMap::new();

            for field in &step.fields {
                let default_value = String::new();
                let value = values.get(&field.name).unwrap_or(&default_value);

                // Skip validation for conditional fields not displayed
                if let Some(rule) = &field.conditional_rule {
                    if !rule(&values) {
                        continue;
                    }
                }

                // Run field validators
                for validator in &field.validators {
                    if let Err(error) = validator(value) {
                        field_errors.insert(field.name.clone(), error);
                        break;
                    }
                }
            }

            FormValidationState {
                is_valid: field_errors.is_empty(),
                field_errors,
                form_errors: Vec::new(),
                is_validating: false,
            }
        }
    };

    // Handle next step
    let handle_next = {
        let validate_current_step = validate_current_step.clone();
        let config_steps = config.steps.clone();
        move || {
            let validation = validate_current_step();

            set_step_validation_states.update(|states| {
                if let Some(state) = states.get_mut(current_step.get()) {
                    *state = validation.clone();
                }
            });

            if validation.is_valid || !config_steps[current_step.get()].validation_required {
                if current_step.get() < total_steps - 1 {
                    set_current_step.update(|step| *step += 1);
                }
            }
        }
    };

    // Handle previous step
    let handle_previous = move || {
        if current_step.get() > 0 {
            set_current_step.update(|step| *step -= 1);
        }
    };

    // Handle wizard completion
    let handle_complete = {
        let config_complete = config.on_complete.clone();
        let validate_current_step = validate_current_step.clone();
        move || {
            set_is_completing.set(true);

            // Validate final step
            let validation = validate_current_step();
            set_step_validation_states.update(|states| {
                if let Some(state) = states.get_mut(current_step.get()) {
                    *state = validation.clone();
                }
            });

            if !validation.is_valid {
                set_is_completing.set(false);
                return;
            }

            let values = form_values.get();

            if let Some(handler) = &config_complete {
                match handler(values.clone()) {
                    Ok(()) => {
                        if let Some(success_handler) = &on_complete_success {
                            success_handler(values);
                        }
                    }
                    Err(error) => {
                        if let Some(error_handler) = &on_complete_error {
                            error_handler(error);
                        }
                    }
                }
            }

            set_is_completing.set(false);
        }
    };

    // Handle cancel
    let handle_cancel = {
        let config_cancel = config.on_cancel.clone();
        move || {
            if let Some(handler) = &config_cancel {
                handler();
            }
        }
    };

    let config_steps_for_view = config.steps.clone();

    view! {
        <div class="form-wizard">
            <div class="mb-8">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">{config.title.clone()}</h2>

                // Progress indicator
                {if config.show_progress {
                    view! {
                        <div class="mb-6">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-gray-700">
                                    "Step " {move || current_step.get() + 1} " of " {total_steps}
                                </span>
                                <span class="text-sm text-gray-500">
                                    {move || format!("{}%", ((current_step.get() + 1) * 100) / total_steps)}
                                </span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div
                                    class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                    style:width=move || format!("{}%", ((current_step.get() + 1) * 100) / total_steps)
                                ></div>
                            </div>
                        </div>
                    }.into_view()
                } else {
                    view! { <div></div> }.into_view()
                }}

                // Step navigation breadcrumbs
                <div class="flex items-center space-x-2 mb-6">
                    {config_steps_for_view.iter().enumerate().map(|(index, step)| {
                        let step_title = step.title.clone();
                        let is_current = move || current_step.get() == index;
                        let is_completed = move || current_step.get() > index;
                        let is_valid = create_memo(move |_| {
                            step_validation_states.get().get(index).map(|state| state.is_valid).unwrap_or(true)
                        });

                        view! {
                            <div class="flex items-center">
                                <div class=move || format!("flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium {}",
                                    if is_current() {
                                        "bg-blue-600 text-white"
                                    } else if is_completed() {
                                        if is_valid.get() { "bg-green-600 text-white" } else { "bg-red-600 text-white" }
                                    } else {
                                        "bg-gray-300 text-gray-600"
                                    }
                                )>
                                    {if is_completed() && is_valid.get() {
                                        view! {
                                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                            </svg>
                                        }.into_view()
                                    } else if is_completed() && !is_valid.get() {
                                        view! {
                                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                            </svg>
                                        }.into_view()
                                    } else {
                                        view! { <span>{index + 1}</span> }.into_view()
                                    }}
                                </div>
                                <span class=move || format!("ml-2 text-sm font-medium {}",
                                    if is_current() { "text-blue-600" } else { "text-gray-500" }
                                )>
                                    {step_title.clone()}
                                </span>
                                {if index < config.steps.len() - 1 {
                                    view! {
                                        <svg class="ml-2 w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                                        </svg>
                                    }.into_view()
                                } else {
                                    view! { <span></span> }.into_view()
                                }}
                            </div>
                        }
                    }).collect::<Vec<_>>()}
                </div>
            </div>

            // Current step content
            {move || {
                let current = current_step.get();
                if current >= config.steps.len() {
                    return view! { <div>"Invalid step"</div> }.into_view();
                }

                let step = &config.steps[current];
                let step_validation = step_validation_states.get().get(current).cloned().unwrap_or_default();

                view! {
                    <div class="step-content">
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-900">{step.title.clone()}</h3>
                            {step.description.clone().map(|desc| {
                                view! {
                                    <p class="text-sm text-gray-600 mt-1">{desc}</p>
                                }
                            })}
                        </div>

                        // Step form fields
                        <div class="space-y-4">
                            {step.fields.iter().map(|field| {
                                let field_name = field.name.clone();
                                let field_config = field.clone();

                                let initial_value = form_values.get().get(&field_name).cloned().unwrap_or_default();
                                let (field_value, field_setter) = create_signal(initial_value);

                                view! {
                                    <AdvancedFormField
                                        config=field_config
                                        value=field_value
                                        on_change=field_setter
                                        form_values=form_values
                                        validation_state=create_signal(step_validation.clone()).0
                                        validation_mode=ValidationMode::OnBlur
                                        on_field_change=Rc::new(|_, _| {})
                                    />
                                }
                            }).collect::<Vec<_>>()}
                        </div>
                    </div>
                }.into_view()
            }}

            // Navigation buttons
            <div class="flex justify-between items-center pt-6 mt-8 border-t border-gray-200">
                <div class="flex space-x-2">
                    {if current_step.get() > 0 {
                        view! {
                            <Button
                                variant=ButtonVariant::Secondary
                                size=ButtonSize::Medium
                                on_click=Box::new(handle_previous)
                                disabled=is_completing.get()
                            >
                                "Previous"
                            </Button>
                        }.into_view()
                    } else {
                        view! { <div></div> }.into_view()
                    }}

                    <Button
                        variant=ButtonVariant::Secondary
                        size=ButtonSize::Medium
                        on_click=Box::new(handle_cancel)
                        disabled=is_completing.get()
                    >
                        "Cancel"
                    </Button>
                </div>

                <div>
                    {if current_step.get() < total_steps - 1 {
                        view! {
                            <Button
                                variant=ButtonVariant::Primary
                                size=ButtonSize::Medium
                                on_click=Box::new(handle_next)
                                disabled=is_completing.get()
                            >
                                "Next"
                            </Button>
                        }.into_view()
                    } else {
                        view! {
                            <Button
                                variant=ButtonVariant::Primary
                                size=ButtonSize::Medium
                                on_click=Box::new(handle_complete)
                                disabled=is_completing.get()
                            >
                                {move || if is_completing.get() {
                                    view! {
                                        <div class="flex items-center space-x-2">
                                            <LoadingSpinner size="small".to_string() />
                                            <span>"Completing..."</span>
                                        </div>
                                    }.into_view()
                                } else {
                                    view! { <span>"Complete"</span> }.into_view()
                                }}
                            </Button>
                        }.into_view()
                    }}
                </div>
            </div>
        </div>
    }
}

/// Advanced form validators
pub mod validators {
    use super::*;
    use regex::Regex;

    /// Required field validator
    pub fn required(field_name: &str) -> FieldValidator {
        let field_name = field_name.to_string();
        Rc::new(move |value: &str| {
            if value.trim().is_empty() {
                Err(format!("{} is required", field_name))
            } else {
                Ok(())
            }
        })
    }

    /// Minimum length validator
    pub fn min_length(field_name: &str, min_len: usize) -> FieldValidator {
        let field_name = field_name.to_string();
        Rc::new(move |value: &str| {
            if value.len() < min_len {
                Err(format!("{} must be at least {} characters long", field_name, min_len))
            } else {
                Ok(())
            }
        })
    }

    /// Maximum length validator
    pub fn max_length(field_name: &str, max_len: usize) -> FieldValidator {
        let field_name = field_name.to_string();
        Rc::new(move |value: &str| {
            if value.len() > max_len {
                Err(format!("{} must be no more than {} characters long", field_name, max_len))
            } else {
                Ok(())
            }
        })
    }

    /// Email format validator
    pub fn email(field_name: &str) -> FieldValidator {
        let field_name = field_name.to_string();
        Rc::new(move |value: &str| {
            if value.is_empty() {
                return Ok(()); // Allow empty for optional fields
            }

            let email_regex = Regex::new(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$").unwrap();
            if email_regex.is_match(value) {
                Ok(())
            } else {
                Err(format!("{} must be a valid email address", field_name))
            }
        })
    }

    /// IP address validator (IPv4)
    pub fn ipv4_address(field_name: &str) -> FieldValidator {
        let field_name = field_name.to_string();
        Rc::new(move |value: &str| {
            if value.is_empty() {
                return Ok(());
            }

            let parts: Vec<&str> = value.split('.').collect();
            if parts.len() == 4 && parts.iter().all(|part| {
                part.parse::<u8>().is_ok()
            }) {
                Ok(())
            } else {
                Err(format!("{} must be a valid IPv4 address", field_name))
            }
        })
    }

    /// IPv6 address validator
    pub fn ipv6_address(field_name: &str) -> FieldValidator {
        let field_name = field_name.to_string();
        Rc::new(move |value: &str| {
            if value.is_empty() {
                return Ok(());
            }

            // Simplified IPv6 validation
            let ipv6_regex = Regex::new(r"^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$").unwrap();
            if ipv6_regex.is_match(value) || value.contains("::") {
                Ok(())
            } else {
                Err(format!("{} must be a valid IPv6 address", field_name))
            }
        })
    }

    /// MAC address validator
    pub fn mac_address(field_name: &str) -> FieldValidator {
        let field_name = field_name.to_string();
        Rc::new(move |value: &str| {
            if value.is_empty() {
                return Ok(());
            }

            let mac_regex = Regex::new(r"^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$").unwrap();
            if mac_regex.is_match(value) {
                Ok(())
            } else {
                Err(format!("{} must be a valid MAC address (e.g., 00:11:22:33:44:55)", field_name))
            }
        })
    }

    /// Numeric range validator
    pub fn numeric_range(field_name: &str, min: f64, max: f64) -> FieldValidator {
        let field_name = field_name.to_string();
        Rc::new(move |value: &str| {
            if value.is_empty() {
                return Ok(());
            }

            match value.parse::<f64>() {
                Ok(num) if num >= min && num <= max => Ok(()),
                Ok(_) => Err(format!("{} must be between {} and {}", field_name, min, max)),
                Err(_) => Err(format!("{} must be a valid number", field_name)),
            }
        })
    }

    /// Integer range validator
    pub fn integer_range(field_name: &str, min: i64, max: i64) -> FieldValidator {
        let field_name = field_name.to_string();
        Rc::new(move |value: &str| {
            if value.is_empty() {
                return Ok(());
            }

            match value.parse::<i64>() {
                Ok(num) if num >= min && num <= max => Ok(()),
                Ok(_) => Err(format!("{} must be between {} and {}", field_name, min, max)),
                Err(_) => Err(format!("{} must be a valid integer", field_name)),
            }
        })
    }

    /// Port number validator
    pub fn port_number(field_name: &str) -> FieldValidator {
        integer_range(field_name, 1, 65535)
    }

    /// URL validator
    pub fn url(field_name: &str) -> FieldValidator {
        let field_name = field_name.to_string();
        Rc::new(move |value: &str| {
            if value.is_empty() {
                return Ok(());
            }

            let url_regex = Regex::new(r"^https?://[^\s/$.?#].[^\s]*$").unwrap();
            if url_regex.is_match(value) {
                Ok(())
            } else {
                Err(format!("{} must be a valid URL", field_name))
            }
        })
    }

    /// Domain name validator
    pub fn domain_name(field_name: &str) -> FieldValidator {
        let field_name = field_name.to_string();
        Rc::new(move |value: &str| {
            if value.is_empty() {
                return Ok(());
            }

            let domain_regex = Regex::new(r"^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$").unwrap();
            if domain_regex.is_match(value) && value.len() <= 253 {
                Ok(())
            } else {
                Err(format!("{} must be a valid domain name", field_name))
            }
        })
    }

    /// Custom regex validator
    pub fn regex_pattern(field_name: &str, pattern: &str, error_message: Option<&str>) -> FieldValidator {
        let field_name = field_name.to_string();
        let pattern = pattern.to_string();
        let error_message = error_message.map(|s| s.to_string()).unwrap_or_else(|| {
            format!("{} format is invalid", field_name)
        });

        Rc::new(move |value: &str| {
            if value.is_empty() {
                return Ok(());
            }

            match Regex::new(&pattern) {
                Ok(regex) => {
                    if regex.is_match(value) {
                        Ok(())
                    } else {
                        Err(error_message.clone())
                    }
                }
                Err(_) => Err("Invalid validation pattern".to_string()),
            }
        })
    }

    /// Combine multiple validators (all must pass)
    pub fn combine(validators: Vec<FieldValidator>) -> FieldValidator {
        Rc::new(move |value: &str| {
            for validator in &validators {
                if let Err(error) = validator(value) {
                    return Err(error);
                }
            }
            Ok(())
        })
    }

    /// Custom validator from closure
    pub fn custom<F>(validator_fn: F) -> FieldValidator
    where
        F: Fn(&str) -> ValidationResult + 'static,
    {
        Rc::new(validator_fn)
    }
}

/// Conditional display rules
pub mod conditions {
    use super::*;

    /// Field equals specific value
    pub fn field_equals(field_name: &str, expected_value: &str) -> ConditionalRule {
        let field_name = field_name.to_string();
        let expected_value = expected_value.to_string();
        Rc::new(move |values: &HashMap<String, String>| {
            values.get(&field_name).map(|v| v == &expected_value).unwrap_or(false)
        })
    }

    /// Field is not empty
    pub fn field_not_empty(field_name: &str) -> ConditionalRule {
        let field_name = field_name.to_string();
        Rc::new(move |values: &HashMap<String, String>| {
            values.get(&field_name).map(|v| !v.trim().is_empty()).unwrap_or(false)
        })
    }

    /// Field is empty
    pub fn field_empty(field_name: &str) -> ConditionalRule {
        let field_name = field_name.to_string();
        Rc::new(move |values: &HashMap<String, String>| {
            values.get(&field_name).map(|v| v.trim().is_empty()).unwrap_or(true)
        })
    }

    /// Field contains specific value
    pub fn field_contains(field_name: &str, substring: &str) -> ConditionalRule {
        let field_name = field_name.to_string();
        let substring = substring.to_string();
        Rc::new(move |values: &HashMap<String, String>| {
            values.get(&field_name).map(|v| v.contains(&substring)).unwrap_or(false)
        })
    }

    /// Multiple fields all equal specific values
    pub fn all_fields_equal(field_conditions: Vec<(String, String)>) -> ConditionalRule {
        Rc::new(move |values: &HashMap<String, String>| {
            field_conditions.iter().all(|(field, expected)| {
                values.get(field).map(|v| v == expected).unwrap_or(false)
            })
        })
    }

    /// Any of multiple fields equal specific values
    pub fn any_field_equals(field_conditions: Vec<(String, String)>) -> ConditionalRule {
        Rc::new(move |values: &HashMap<String, String>| {
            field_conditions.iter().any(|(field, expected)| {
                values.get(field).map(|v| v == expected).unwrap_or(false)
            })
        })
    }

    /// Custom condition from closure
    pub fn custom<F>(condition_fn: F) -> ConditionalRule
    where
        F: Fn(&HashMap<String, String>) -> bool + 'static,
    {
        Rc::new(condition_fn)
    }
}

/// Form builder utilities
pub mod builder {
    use super::*;

    /// Form field builder for easier configuration
    pub struct FormFieldBuilder {
        config: FormFieldConfig,
    }

    impl FormFieldBuilder {
        pub fn new(name: &str, label: &str, field_type: FormFieldType) -> Self {
            Self {
                config: FormFieldConfig {
                    name: name.to_string(),
                    label: label.to_string(),
                    field_type,
                    required: false,
                    disabled: false,
                    placeholder: None,
                    help_text: None,
                    default_value: None,
                    validators: Vec::new(),
                    conditional_rule: None,
                    options: Vec::new(),
                    attributes: HashMap::new(),
                },
            }
        }

        pub fn required(mut self) -> Self {
            self.config.required = true;
            self
        }

        pub fn disabled(mut self) -> Self {
            self.config.disabled = true;
            self
        }

        pub fn placeholder(mut self, placeholder: &str) -> Self {
            self.config.placeholder = Some(placeholder.to_string());
            self
        }

        pub fn help_text(mut self, help: &str) -> Self {
            self.config.help_text = Some(help.to_string());
            self
        }

        pub fn default_value(mut self, value: &str) -> Self {
            self.config.default_value = Some(value.to_string());
            self
        }

        pub fn validator(mut self, validator: FieldValidator) -> Self {
            self.config.validators.push(validator);
            self
        }

        pub fn condition(mut self, rule: ConditionalRule) -> Self {
            self.config.conditional_rule = Some(rule);
            self
        }

        pub fn options(mut self, options: Vec<SelectOption>) -> Self {
            self.config.options = options;
            self
        }

        pub fn attribute(mut self, name: &str, value: &str) -> Self {
            self.config.attributes.insert(name.to_string(), value.to_string());
            self
        }

        pub fn build(self) -> FormFieldConfig {
            self.config
        }
    }

    /// Quick field creation functions
    pub fn text_field(name: &str, label: &str) -> FormFieldBuilder {
        FormFieldBuilder::new(name, label, FormFieldType::Text)
    }

    pub fn password_field(name: &str, label: &str) -> FormFieldBuilder {
        FormFieldBuilder::new(name, label, FormFieldType::Password)
    }

    pub fn email_field(name: &str, label: &str) -> FormFieldBuilder {
        FormFieldBuilder::new(name, label, FormFieldType::Email)
    }

    pub fn number_field(name: &str, label: &str) -> FormFieldBuilder {
        FormFieldBuilder::new(name, label, FormFieldType::Number)
    }

    pub fn select_field(name: &str, label: &str, options: Vec<SelectOption>) -> FormFieldBuilder {
        FormFieldBuilder::new(name, label, FormFieldType::Select).options(options)
    }

    pub fn textarea_field(name: &str, label: &str) -> FormFieldBuilder {
        FormFieldBuilder::new(name, label, FormFieldType::Textarea)
    }

    pub fn checkbox_field(name: &str, label: &str) -> FormFieldBuilder {
        FormFieldBuilder::new(name, label, FormFieldType::Checkbox)
    }
}
