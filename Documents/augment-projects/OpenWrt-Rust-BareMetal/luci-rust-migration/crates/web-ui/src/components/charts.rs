use leptos::*;
use std::collections::HashMap;
use wasm_bindgen::prelude::*;
use web_sys::{CanvasRenderingContext2d, HtmlCanvasElement};
use js_sys::Array;

/// Chart data point
#[derive(Debug, Clone)]
pub struct ChartDataPoint {
    pub label: String,
    pub value: f64,
    pub color: Option<String>,
    pub timestamp: Option<u64>,
}

/// Time series data point
#[derive(Debug, Clone)]
pub struct TimeSeriesPoint {
    pub timestamp: u64,
    pub value: f64,
    pub label: Option<String>,
}

/// Chart configuration
#[derive(Debug, Clone)]
pub struct ChartConfig {
    pub title: String,
    pub width: u32,
    pub height: u32,
    pub show_legend: bool,
    pub show_grid: bool,
    pub show_labels: bool,
    pub colors: Vec<String>,
    pub background_color: String,
    pub text_color: String,
    pub grid_color: String,
    pub animation_duration: u32,
}

impl Default for ChartConfig {
    fn default() -> Self {
        Self {
            title: String::new(),
            width: 400,
            height: 300,
            show_legend: true,
            show_grid: true,
            show_labels: true,
            colors: vec![
                "#3B82F6".to_string(), // blue
                "#EF4444".to_string(), // red
                "#10B981".to_string(), // green
                "#F59E0B".to_string(), // yellow
                "#8B5CF6".to_string(), // purple
                "#EC4899".to_string(), // pink
                "#06B6D4".to_string(), // cyan
                "#84CC16".to_string(), // lime
            ],
            background_color: "#FFFFFF".to_string(),
            text_color: "#374151".to_string(),
            grid_color: "#E5E7EB".to_string(),
            animation_duration: 300,
        }
    }
}

/// Chart types
#[derive(Debug, Clone, PartialEq)]
pub enum ChartType {
    Line,
    Bar,
    Pie,
    Doughnut,
    Area,
    Scatter,
}

/// Line chart component
#[component]
pub fn LineChart(
    data: ReadSignal<Vec<TimeSeriesPoint>>,
    #[prop(optional)] config: Option<ChartConfig>,
    #[prop(optional)] class: Option<String>,
) -> impl IntoView {
    let config = config.unwrap_or_default();
    let canvas_ref = create_node_ref::<html::Canvas>();

    // Draw chart when data changes
    create_effect(move |_| {
        let data_points = data.get();
        if let Some(canvas) = canvas_ref.get() {
            draw_line_chart(&canvas, &data_points, &config);
        }
    });

    view! {
        <div class=format!("chart-container {}", class.unwrap_or_default())>
            {if !config.title.is_empty() {
                view! {
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 text-center">
                        {config.title.clone()}
                    </h3>
                }.into_view()
            } else {
                view! { <div></div> }.into_view()
            }}

            <canvas
                node_ref=canvas_ref
                width=config.width
                height=config.height
                class="border border-gray-200 rounded-lg shadow-sm"
            ></canvas>
        </div>
    }
}

/// Bar chart component
#[component]
pub fn BarChart(
    data: ReadSignal<Vec<ChartDataPoint>>,
    #[prop(optional)] config: Option<ChartConfig>,
    #[prop(optional)] class: Option<String>,
) -> impl IntoView {
    let config = config.unwrap_or_default();
    let canvas_ref = create_node_ref::<html::Canvas>();

    // Draw chart when data changes
    create_effect(move |_| {
        let data_points = data.get();
        if let Some(canvas) = canvas_ref.get() {
            draw_bar_chart(&canvas, &data_points, &config);
        }
    });

    view! {
        <div class=format!("chart-container {}", class.unwrap_or_default())>
            {if !config.title.is_empty() {
                view! {
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 text-center">
                        {config.title.clone()}
                    </h3>
                }.into_view()
            } else {
                view! { <div></div> }.into_view()
            }}

            <canvas
                node_ref=canvas_ref
                width=config.width
                height=config.height
                class="border border-gray-200 rounded-lg shadow-sm"
            ></canvas>

            {if config.show_legend {
                view! {
                    <div class="mt-4 flex flex-wrap justify-center gap-4">
                        {data.get().iter().enumerate().map(|(i, point)| {
                            let color = point.color.clone().unwrap_or_else(|| {
                                config.colors.get(i % config.colors.len()).cloned().unwrap_or("#3B82F6".to_string())
                            });
                            view! {
                                <div class="flex items-center space-x-2">
                                    <div
                                        class="w-3 h-3 rounded-full"
                                        style:background-color=color
                                    ></div>
                                    <span class="text-sm text-gray-600">{point.label.clone()}</span>
                                </div>
                            }
                        }).collect::<Vec<_>>()}
                    </div>
                }.into_view()
            } else {
                view! { <div></div> }.into_view()
            }}
        </div>
    }
}

/// Pie chart component
#[component]
pub fn PieChart(
    data: ReadSignal<Vec<ChartDataPoint>>,
    #[prop(optional)] config: Option<ChartConfig>,
    #[prop(optional)] class: Option<String>,
) -> impl IntoView {
    let config = config.unwrap_or_default();
    let canvas_ref = create_node_ref::<html::Canvas>();

    // Draw chart when data changes
    create_effect(move |_| {
        let data_points = data.get();
        if let Some(canvas) = canvas_ref.get() {
            draw_pie_chart(&canvas, &data_points, &config);
        }
    });

    view! {
        <div class=format!("chart-container {}", class.unwrap_or_default())>
            {if !config.title.is_empty() {
                view! {
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 text-center">
                        {config.title.clone()}
                    </h3>
                }.into_view()
            } else {
                view! { <div></div> }.into_view()
            }}

            <div class="flex items-center justify-center space-x-8">
                <canvas
                    node_ref=canvas_ref
                    width=config.width
                    height=config.height
                    class="border border-gray-200 rounded-lg shadow-sm"
                ></canvas>

                {if config.show_legend {
                    view! {
                        <div class="space-y-2">
                            {data.get().iter().enumerate().map(|(i, point)| {
                                let color = point.color.clone().unwrap_or_else(|| {
                                    config.colors.get(i % config.colors.len()).cloned().unwrap_or("#3B82F6".to_string())
                                });
                                let total: f64 = data.get().iter().map(|p| p.value).sum();
                                let percentage = if total > 0.0 { (point.value / total * 100.0).round() } else { 0.0 };

                                view! {
                                    <div class="flex items-center space-x-3">
                                        <div
                                            class="w-4 h-4 rounded-full"
                                            style:background-color=color
                                        ></div>
                                        <div class="flex-1">
                                            <div class="text-sm font-medium text-gray-900">{point.label.clone()}</div>
                                            <div class="text-xs text-gray-500">
                                                {format!("{:.1} ({:.0}%)", point.value, percentage)}
                                            </div>
                                        </div>
                                    </div>
                                }
                            }).collect::<Vec<_>>()}
                        </div>
                    }.into_view()
                } else {
                    view! { <div></div> }.into_view()
                }}
            </div>
        </div>
    }
}

/// System monitoring chart component
#[component]
pub fn SystemMonitorChart(
    cpu_data: ReadSignal<Vec<TimeSeriesPoint>>,
    memory_data: ReadSignal<Vec<TimeSeriesPoint>>,
    #[prop(optional)] class: Option<String>,
) -> impl IntoView {
    let config = ChartConfig {
        title: "System Performance".to_string(),
        width: 600,
        height: 300,
        colors: vec!["#3B82F6".to_string(), "#EF4444".to_string()],
        ..Default::default()
    };

    view! {
        <div class=format!("system-monitor-chart {}", class.unwrap_or_default())>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">System Performance</h3>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <LineChart
                            data=cpu_data
                            config=ChartConfig {
                                title: "CPU Usage (%)".to_string(),
                                colors: vec!["#3B82F6".to_string()],
                                ..config.clone()
                            }
                        />
                    </div>

                    <div>
                        <LineChart
                            data=memory_data
                            config=ChartConfig {
                                title: "Memory Usage (%)".to_string(),
                                colors: vec!["#EF4444".to_string()],
                                ..config.clone()
                            }
                        />
                    </div>
                </div>
            </div>
        </div>
    }
}

/// Network statistics chart component
#[component]
pub fn NetworkStatsChart(
    rx_data: ReadSignal<Vec<TimeSeriesPoint>>,
    tx_data: ReadSignal<Vec<TimeSeriesPoint>>,
    #[prop(optional)] class: Option<String>,
) -> impl IntoView {
    view! {
        <div class=format!("network-stats-chart {}", class.unwrap_or_default())>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Network Traffic</h3>

                <div class="space-y-6">
                    <div>
                        <LineChart
                            data=rx_data
                            config=ChartConfig {
                                title: "Download (MB/s)".to_string(),
                                colors: vec!["#10B981".to_string()],
                                width: 600,
                                height: 200,
                                ..Default::default()
                            }
                        />
                    </div>

                    <div>
                        <LineChart
                            data=tx_data
                            config=ChartConfig {
                                title: "Upload (MB/s)".to_string(),
                                colors: vec!["#F59E0B".to_string()],
                                width: 600,
                                height: 200,
                                ..Default::default()
                            }
                        />
                    </div>
                </div>
            </div>
        </div>
    }
}

/// Storage usage chart component
#[component]
pub fn StorageChart(
    data: ReadSignal<Vec<ChartDataPoint>>,
    #[prop(optional)] class: Option<String>,
) -> impl IntoView {
    view! {
        <div class=format!("storage-chart {}", class.unwrap_or_default())>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <PieChart
                    data=data
                    config=ChartConfig {
                        title: "Storage Usage".to_string(),
                        width: 300,
                        height: 300,
                        ..Default::default()
                    }
                />
            </div>
        </div>
    }
}

/// Performance metrics dashboard
#[component]
pub fn PerformanceDashboard(
    cpu_data: ReadSignal<Vec<TimeSeriesPoint>>,
    memory_data: ReadSignal<Vec<TimeSeriesPoint>>,
    network_rx_data: ReadSignal<Vec<TimeSeriesPoint>>,
    network_tx_data: ReadSignal<Vec<TimeSeriesPoint>>,
    storage_data: ReadSignal<Vec<ChartDataPoint>>,
    #[prop(optional)] class: Option<String>,
) -> impl IntoView {
    view! {
        <div class=format!("performance-dashboard {}", class.unwrap_or_default())>
            <div class="space-y-6">
                <div class="grid grid-cols-1 xl:grid-cols-2 gap-6">
                    <SystemMonitorChart
                        cpu_data=cpu_data
                        memory_data=memory_data
                    />

                    <StorageChart data=storage_data />
                </div>

                <NetworkStatsChart
                    rx_data=network_rx_data
                    tx_data=network_tx_data
                />
            </div>
        </div>
    }
}

/// Drawing functions for charts
fn draw_line_chart(canvas: &HtmlCanvasElement, data: &[TimeSeriesPoint], config: &ChartConfig) {
    let context = canvas
        .get_context("2d")
        .unwrap()
        .unwrap()
        .dyn_into::<CanvasRenderingContext2d>()
        .unwrap();

    // Clear canvas
    context.clear_rect(0.0, 0.0, canvas.width() as f64, canvas.height() as f64);

    if data.is_empty() {
        return;
    }

    let padding = 40.0;
    let chart_width = canvas.width() as f64 - 2.0 * padding;
    let chart_height = canvas.height() as f64 - 2.0 * padding;

    // Find min/max values
    let min_value = data.iter().map(|p| p.value).fold(f64::INFINITY, f64::min);
    let max_value = data.iter().map(|p| p.value).fold(f64::NEG_INFINITY, f64::max);
    let value_range = max_value - min_value;

    let min_time = data.iter().map(|p| p.timestamp).min().unwrap_or(0);
    let max_time = data.iter().map(|p| p.timestamp).max().unwrap_or(0);
    let time_range = max_time - min_time;

    // Draw grid
    if config.show_grid {
        context.set_stroke_style(&config.grid_color.clone().into());
        context.set_line_width(1.0);

        // Horizontal grid lines
        for i in 0..=5 {
            let y = padding + (i as f64 / 5.0) * chart_height;
            context.begin_path();
            context.move_to(padding, y);
            context.line_to(padding + chart_width, y);
            context.stroke();
        }

        // Vertical grid lines
        for i in 0..=10 {
            let x = padding + (i as f64 / 10.0) * chart_width;
            context.begin_path();
            context.move_to(x, padding);
            context.line_to(x, padding + chart_height);
            context.stroke();
        }
    }

    // Draw line
    if data.len() > 1 {
        context.set_stroke_style(&config.colors[0].clone().into());
        context.set_line_width(2.0);
        context.begin_path();

        for (i, point) in data.iter().enumerate() {
            let x = padding + if time_range > 0 {
                ((point.timestamp - min_time) as f64 / time_range as f64) * chart_width
            } else {
                (i as f64 / (data.len() - 1) as f64) * chart_width
            };

            let y = padding + chart_height - if value_range > 0.0 {
                ((point.value - min_value) / value_range) * chart_height
            } else {
                chart_height / 2.0
            };

            if i == 0 {
                context.move_to(x, y);
            } else {
                context.line_to(x, y);
            }
        }

        context.stroke();
    }

    // Draw data points
    context.set_fill_style(&config.colors[0].clone().into());
    for (i, point) in data.iter().enumerate() {
        let x = padding + if time_range > 0 {
            ((point.timestamp - min_time) as f64 / time_range as f64) * chart_width
        } else {
            (i as f64 / (data.len() - 1) as f64) * chart_width
        };

        let y = padding + chart_height - if value_range > 0.0 {
            ((point.value - min_value) / value_range) * chart_height
        } else {
            chart_height / 2.0
        };

        context.begin_path();
        context.arc(x, y, 3.0, 0.0, 2.0 * std::f64::consts::PI).unwrap();
        context.fill();
    }
}

fn draw_bar_chart(canvas: &HtmlCanvasElement, data: &[ChartDataPoint], config: &ChartConfig) {
    let context = canvas
        .get_context("2d")
        .unwrap()
        .unwrap()
        .dyn_into::<CanvasRenderingContext2d>()
        .unwrap();

    // Clear canvas
    context.clear_rect(0.0, 0.0, canvas.width() as f64, canvas.height() as f64);

    if data.is_empty() {
        return;
    }

    let padding = 40.0;
    let chart_width = canvas.width() as f64 - 2.0 * padding;
    let chart_height = canvas.height() as f64 - 2.0 * padding;

    let max_value = data.iter().map(|p| p.value).fold(f64::NEG_INFINITY, f64::max);
    let bar_width = chart_width / data.len() as f64 * 0.8;
    let bar_spacing = chart_width / data.len() as f64 * 0.2;

    // Draw grid
    if config.show_grid {
        context.set_stroke_style(&config.grid_color.clone().into());
        context.set_line_width(1.0);

        for i in 0..=5 {
            let y = padding + (i as f64 / 5.0) * chart_height;
            context.begin_path();
            context.move_to(padding, y);
            context.line_to(padding + chart_width, y);
            context.stroke();
        }
    }

    // Draw bars
    for (i, point) in data.iter().enumerate() {
        let color = point.color.clone().unwrap_or_else(|| {
            config.colors.get(i % config.colors.len()).cloned().unwrap_or("#3B82F6".to_string())
        });

        let x = padding + (i as f64 * (bar_width + bar_spacing)) + bar_spacing / 2.0;
        let bar_height = if max_value > 0.0 {
            (point.value / max_value) * chart_height
        } else {
            0.0
        };
        let y = padding + chart_height - bar_height;

        context.set_fill_style(&color.into());
        context.fill_rect(x, y, bar_width, bar_height);

        // Draw labels
        if config.show_labels {
            context.set_fill_style(&config.text_color.clone().into());
            context.set_font("12px Arial");
            context.set_text_align("center");

            // Value label
            let _ = context.fill_text(&format!("{:.1}", point.value), x + bar_width / 2.0, y - 5.0);

            // Category label
            let _ = context.fill_text(&point.label, x + bar_width / 2.0, padding + chart_height + 20.0);
        }
    }
}

fn draw_pie_chart(canvas: &HtmlCanvasElement, data: &[ChartDataPoint], config: &ChartConfig) {
    let context = canvas
        .get_context("2d")
        .unwrap()
        .unwrap()
        .dyn_into::<CanvasRenderingContext2d>()
        .unwrap();

    // Clear canvas
    context.clear_rect(0.0, 0.0, canvas.width() as f64, canvas.height() as f64);

    if data.is_empty() {
        return;
    }

    let center_x = canvas.width() as f64 / 2.0;
    let center_y = canvas.height() as f64 / 2.0;
    let radius = (canvas.width().min(canvas.height()) as f64 / 2.0) - 20.0;

    let total: f64 = data.iter().map(|p| p.value).sum();
    if total <= 0.0 {
        return;
    }

    let mut current_angle = -std::f64::consts::PI / 2.0; // Start at top

    for (i, point) in data.iter().enumerate() {
        let color = point.color.clone().unwrap_or_else(|| {
            config.colors.get(i % config.colors.len()).cloned().unwrap_or("#3B82F6".to_string())
        });

        let slice_angle = (point.value / total) * 2.0 * std::f64::consts::PI;

        context.set_fill_style(&color.into());
        context.begin_path();
        context.move_to(center_x, center_y);
        context.arc(center_x, center_y, radius, current_angle, current_angle + slice_angle).unwrap();
        context.close_path();
        context.fill();

        // Draw slice border
        context.set_stroke_style(&"#FFFFFF".into());
        context.set_line_width(2.0);
        context.stroke();

        current_angle += slice_angle;
    }
}
