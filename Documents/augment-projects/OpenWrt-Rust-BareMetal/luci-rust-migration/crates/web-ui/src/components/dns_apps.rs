use leptos::*;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// DNS application types
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum DnsAppType {
    DnsServer,
    DnsFiltering,
    DnsAnalytics,
    DnsForwarder,
    DnsCache,
    DnsBlocklist,
    DnsResolver,
    DnsLoadBalancer,
    DnsSecValidator,
    DnsMonitoring,
}

impl DnsAppType {
    pub fn display_name(&self) -> &'static str {
        match self {
            DnsAppType::DnsServer => "DNS Server",
            DnsAppType::DnsFiltering => "DNS Filtering",
            DnsAppType::DnsAnalytics => "DNS Analytics",
            DnsAppType::DnsForwarder => "DNS Forwarder",
            DnsAppType::DnsCache => "DNS Cache",
            DnsAppType::DnsBlocklist => "DNS Blocklist",
            DnsAppType::DnsResolver => "DNS Resolver",
            DnsAppType::DnsLoadBalancer => "DNS Load Balancer",
            DnsAppType::DnsSecValidator => "DNSSEC Validator",
            DnsAppType::DnsMonitoring => "DNS Monitoring",
        }
    }
    
    pub fn icon(&self) -> &'static str {
        match self {
            DnsAppType::DnsServer => "🌐",
            DnsAppType::DnsFiltering => "🚫",
            DnsAppType::DnsAnalytics => "📊",
            DnsAppType::DnsForwarder => "↗️",
            DnsAppType::DnsCache => "💾",
            DnsAppType::DnsBlocklist => "🛡️",
            DnsAppType::DnsResolver => "🔍",
            DnsAppType::DnsLoadBalancer => "⚖️",
            DnsAppType::DnsSecValidator => "🔐",
            DnsAppType::DnsMonitoring => "📈",
        }
    }
    
    pub fn description(&self) -> &'static str {
        match self {
            DnsAppType::DnsServer => "Authoritative DNS server for local domains",
            DnsAppType::DnsFiltering => "Filter DNS queries based on rules and policies",
            DnsAppType::DnsAnalytics => "Analyze DNS query patterns and statistics",
            DnsAppType::DnsForwarder => "Forward DNS queries to upstream servers",
            DnsAppType::DnsCache => "Cache DNS responses for improved performance",
            DnsAppType::DnsBlocklist => "Block malicious or unwanted domains",
            DnsAppType::DnsResolver => "Recursive DNS resolver for client queries",
            DnsAppType::DnsLoadBalancer => "Load balance DNS queries across servers",
            DnsAppType::DnsSecValidator => "Validate DNSSEC signatures",
            DnsAppType::DnsMonitoring => "Monitor DNS server performance and health",
        }
    }
}

/// DNS record type
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum DnsRecordType {
    A,
    AAAA,
    CNAME,
    MX,
    TXT,
    NS,
    PTR,
    SRV,
    SOA,
}

impl DnsRecordType {
    pub fn display_name(&self) -> &'static str {
        match self {
            DnsRecordType::A => "A (IPv4)",
            DnsRecordType::AAAA => "AAAA (IPv6)",
            DnsRecordType::CNAME => "CNAME (Canonical Name)",
            DnsRecordType::MX => "MX (Mail Exchange)",
            DnsRecordType::TXT => "TXT (Text)",
            DnsRecordType::NS => "NS (Name Server)",
            DnsRecordType::PTR => "PTR (Pointer)",
            DnsRecordType::SRV => "SRV (Service)",
            DnsRecordType::SOA => "SOA (Start of Authority)",
        }
    }
}

/// DNS record
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DnsRecord {
    pub id: String,
    pub name: String,
    pub record_type: DnsRecordType,
    pub value: String,
    pub ttl: u32,
    pub priority: Option<u16>,
    pub weight: Option<u16>,
    pub port: Option<u16>,
    pub enabled: bool,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

/// DNS zone
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DnsZone {
    pub id: String,
    pub name: String,
    pub zone_type: String, // "master", "slave", "forward"
    pub records: Vec<DnsRecord>,
    pub enabled: bool,
    pub serial: u32,
    pub refresh: u32,
    pub retry: u32,
    pub expire: u32,
    pub minimum: u32,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

/// DNS application
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DnsApp {
    pub id: String,
    pub name: String,
    pub app_type: DnsAppType,
    pub enabled: bool,
    pub running: bool,
    pub config: HashMap<String, String>,
    pub zones: Vec<DnsZone>,
    pub queries_per_second: f64,
    pub cache_hit_rate: f64,
    pub blocked_queries: u64,
    pub total_queries: u64,
    pub uptime: Option<std::time::Duration>,
    pub last_restart: Option<chrono::DateTime<chrono::Utc>>,
}

impl DnsApp {
    pub fn new(app_type: DnsAppType) -> Self {
        Self {
            id: format!("{:?}", app_type).to_lowercase().replace("_", "-"),
            name: app_type.display_name().to_string(),
            app_type,
            enabled: false,
            running: false,
            config: HashMap::new(),
            zones: Vec::new(),
            queries_per_second: 0.0,
            cache_hit_rate: 0.0,
            blocked_queries: 0,
            total_queries: 0,
            uptime: None,
            last_restart: None,
        }
    }
}

/// DNS query statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DnsQueryStats {
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub queries_per_second: f64,
    pub cache_hits: u64,
    pub cache_misses: u64,
    pub blocked_queries: u64,
    pub response_time_ms: f64,
    pub top_domains: Vec<(String, u64)>,
    pub query_types: HashMap<String, u64>,
}

/// DNS applications manager component
#[component]
pub fn DnsAppsManager(
    #[prop(optional)] class: Option<String>,
) -> impl IntoView {
    let (apps, set_apps) = create_signal(get_default_dns_apps());
    let (stats, set_stats) = create_signal(get_mock_dns_stats());
    let (selected_app, set_selected_app) = create_signal(None::<String>);
    let (loading, set_loading) = create_signal(false);
    let (error, set_error) = create_signal(None::<String>);
    
    // Load DNS apps on mount
    create_effect(move |_| {
        spawn_local(async move {
            set_loading.set(true);
            set_error.set(None);
            
            match load_dns_apps().await {
                Ok(loaded_apps) => {
                    set_apps.set(loaded_apps);
                }
                Err(e) => {
                    set_error.set(Some(e));
                }
            }
            
            set_loading.set(false);
        });
    });
    
    // Auto-refresh stats every 30 seconds
    create_effect(move |_| {
        let interval = gloo_timers::callback::Interval::new(30000, move || {
            spawn_local(async move {
                if let Ok(new_stats) = load_dns_stats().await {
                    set_stats.set(new_stats);
                }
            });
        });
        
        // Keep interval alive
        std::mem::forget(interval);
    });
    
    view! {
        <div class=format!("dns-apps-manager {}", class.unwrap_or_default())>
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-lg font-semibold text-gray-900">DNS Applications</h2>
                            <p class="mt-1 text-sm text-gray-600">Manage DNS server configuration, filtering, and analytics</p>
                        </div>
                        <div class="flex space-x-2">
                            <button 
                                class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
                                on:click=move |_| {
                                    spawn_local(async move {
                                        if let Ok(refreshed) = load_dns_apps().await {
                                            set_apps.set(refreshed);
                                        }
                                    });
                                }
                            >
                                🔄 Refresh
                            </button>
                            <button class="bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700 transition-colors">
                                "➕ Add Zone"
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="p-6">
                    {move || {
                        if loading.get() {
                            view! {
                                <div class="flex items-center justify-center py-8">
                                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                    <span class="ml-3 text-gray-600">Loading DNS applications...</span>
                                </div>
                            }.into_view()
                        } else if let Some(err) = error.get() {
                            view! {
                                <div class="bg-red-50 border border-red-200 rounded-md p-4">
                                    <div class="flex">
                                        <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                                        </svg>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-red-800">DNS Apps Loading Error</h3>
                                            <p class="mt-1 text-sm text-red-700">{err}</p>
                                        </div>
                                    </div>
                                </div>
                            }.into_view()
                        } else {
                            view! {
                                <div class="space-y-6">
                                    // DNS overview
                                    <DnsOverview apps=apps stats=stats />
                                    
                                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                        // DNS apps list
                                        <div>
                                            <DnsAppsList 
                                                apps=apps
                                                selected_app=selected_app
                                                on_select=move |id: String| set_selected_app.set(Some(id))
                                                on_app_action=move |action: DnsAppAction| {
                                                    spawn_local(async move {
                                                        if let Ok(updated) = handle_dns_app_action(action).await {
                                                            set_apps.set(updated);
                                                        }
                                                    });
                                                }
                                            />
                                        </div>
                                        
                                        // DNS analytics
                                        <div>
                                            <DnsAnalytics stats=stats />
                                        </div>
                                    </div>
                                </div>
                            }.into_view()
                        }
                    }}
                </div>
            </div>
        </div>
    }
}

/// DNS app action types
#[derive(Debug, Clone)]
pub enum DnsAppAction {
    Enable(String),
    Disable(String),
    Start(String),
    Stop(String),
    Restart(String),
    Configure(String, HashMap<String, String>),
    AddZone(String, DnsZone),
    RemoveZone(String, String),
    FlushCache(String),
}

/// DNS overview component
#[component]
fn DnsOverview(
    apps: ReadSignal<Vec<DnsApp>>,
    stats: ReadSignal<DnsQueryStats>,
) -> impl IntoView {
    view! {
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {move || {
                let app_list = apps.get();
                let current_stats = stats.get();

                let active_apps = app_list.iter().filter(|app| app.running).count();
                let total_zones = app_list.iter().map(|app| app.zones.len()).sum::<usize>();
                let total_queries = app_list.iter().map(|app| app.total_queries).sum::<u64>();
                let avg_cache_hit_rate = if !app_list.is_empty() {
                    app_list.iter().map(|app| app.cache_hit_rate).sum::<f64>() / app_list.len() as f64
                } else {
                    0.0
                };

                view! {
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h6l2 2h6a2 2 0 012 2v4a2 2 0 01-2 2H5z"/>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-blue-600">Active Apps</p>
                                <p class="text-2xl font-semibold text-blue-900">{active_apps}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-green-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-green-600">DNS Zones</p>
                                <p class="text-2xl font-semibold text-green-900">{total_zones}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-purple-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-purple-600">Queries/sec</p>
                                <p class="text-2xl font-semibold text-purple-900">{format!("{:.1}", current_stats.queries_per_second)}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-yellow-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-yellow-600">Cache Hit Rate</p>
                                <p class="text-2xl font-semibold text-yellow-900">{format!("{:.1}%", avg_cache_hit_rate * 100.0)}</p>
                            </div>
                        </div>
                    </div>
                }
            }}
        </div>
    }
}

/// DNS apps list component
#[component]
fn DnsAppsList(
    apps: ReadSignal<Vec<DnsApp>>,
    selected_app: ReadSignal<Option<String>>,
    on_select: impl Fn(String) + 'static + Copy,
    on_app_action: impl Fn(DnsAppAction) + 'static + Copy,
) -> impl IntoView {
    view! {
        <div class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900">DNS Applications</h3>

            <div class="space-y-2">
                {move || {
                    let app_list = apps.get();

                    if app_list.is_empty() {
                        view! {
                            <div class="text-center py-8 text-gray-500">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h6l2 2h6a2 2 0 012 2v4a2 2 0 01-2 2H5z"/>
                                </svg>
                                <p class="mt-2">No DNS applications configured</p>
                            </div>
                        }.into_view()
                    } else {
                        app_list.into_iter().map(|app| {
                            let app_id = app.id.clone();
                            let is_selected = selected_app.get().as_ref() == Some(&app.id);

                            view! {
                                <DnsAppCard
                                    app=app
                                    is_selected=is_selected
                                    on_select=move || on_select(app_id.clone())
                                    on_action=on_app_action
                                />
                            }
                        }).collect_view()
                    }
                }}
            </div>
        </div>
    }
}

/// DNS app card component
#[component]
fn DnsAppCard(
    app: DnsApp,
    is_selected: bool,
    on_select: impl Fn() + 'static + Copy,
    on_action: impl Fn(DnsAppAction) + 'static + Copy,
) -> impl IntoView {
    let app_id = app.id.clone();

    view! {
        <div
            class=move || format!(
                "border rounded-lg p-4 cursor-pointer transition-colors {}",
                if is_selected {
                    "border-blue-500 bg-blue-50"
                } else {
                    "border-gray-200 hover:border-gray-300 bg-white"
                }
            )
            on:click=move |_| on_select()
        >
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <span class="text-2xl">{app.app_type.icon()}</span>
                    <div>
                        <h4 class="font-medium text-gray-900">{app.name.clone()}</h4>
                        <p class="text-sm text-gray-500">{app.app_type.description()}</p>
                    </div>
                </div>

                <div class="flex items-center space-x-2">
                    <div class="flex items-center space-x-1">
                        {if app.enabled {
                            view! {
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-green-600 bg-green-100">
                                    "✓ Enabled"
                                </span>
                            }.into_view()
                        } else {
                            view! {
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-gray-600 bg-gray-100">
                                    "○ Disabled"
                                </span>
                            }.into_view()
                        }}

                        {if app.running {
                            view! {
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-blue-600 bg-blue-100">
                                    ▶️ Running
                                </span>
                            }.into_view()
                        } else {
                            view! {
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-gray-600 bg-gray-100">
                                    ⏹️ Stopped
                                </span>
                            }.into_view()
                        }}
                    </div>

                    <div class="flex space-x-1">
                        <button
                            class="p-1 text-blue-600 hover:bg-blue-100 rounded"
                            title="Restart DNS app"
                            on:click=move |e| {
                                e.stop_propagation();
                                on_action(DnsAppAction::Restart(app_id.clone()));
                            }
                        >
                            🔄
                        </button>

                        <button
                            class="p-1 text-purple-600 hover:bg-purple-100 rounded"
                            title="Flush DNS cache"
                            on:click=move |e| {
                                e.stop_propagation();
                                on_action(DnsAppAction::FlushCache(app_id.clone()));
                            }
                        >
                            🗑️
                        </button>
                    </div>
                </div>
            </div>

            <div class="mt-3 grid grid-cols-3 gap-4 text-sm text-gray-500">
                <div>
                    <span class="font-medium">Zones:</span> {app.zones.len()}
                </div>
                <div>
                    <span class="font-medium">QPS:</span> {format!("{:.1}", app.queries_per_second)}
                </div>
                <div>
                    <span class="font-medium">Cache:</span> {format!("{:.1}%", app.cache_hit_rate * 100.0)}
                </div>
            </div>

            {if let Some(uptime) = app.uptime {
                view! {
                    <div class="mt-2 text-xs text-gray-400">
                        Uptime: {format_duration(uptime)}
                    </div>
                }.into_view()
            } else {
                view! {
                    <div class="mt-2 text-xs text-gray-400">
                        Not running
                    </div>
                }.into_view()
            }}
        </div>
    }
}

/// DNS analytics component
#[component]
fn DnsAnalytics(
    stats: ReadSignal<DnsQueryStats>,
) -> impl IntoView {
    view! {
        <div class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900">DNS Analytics</h3>

            <div class="bg-gray-50 rounded-lg p-4 space-y-4">
                {move || {
                    let current_stats = stats.get();

                    view! {
                        <div class="grid grid-cols-2 gap-4">
                            <div class="bg-white rounded p-3">
                                <h4 class="text-sm font-medium text-gray-700 mb-2">Query Performance</h4>
                                <div class="space-y-2">
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">Queries/sec:</span>
                                        <span class="text-sm font-medium">{format!("{:.1}", current_stats.queries_per_second)}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">Response time:</span>
                                        <span class="text-sm font-medium">{format!("{:.1}ms", current_stats.response_time_ms)}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">Cache hits:</span>
                                        <span class="text-sm font-medium">{current_stats.cache_hits}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">Cache misses:</span>
                                        <span class="text-sm font-medium">{current_stats.cache_misses}</span>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded p-3">
                                <h4 class="text-sm font-medium text-gray-700 mb-2">Security</h4>
                                <div class="space-y-2">
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">Blocked queries:</span>
                                        <span class="text-sm font-medium text-red-600">{current_stats.blocked_queries}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">Block rate:</span>
                                        <span class="text-sm font-medium text-red-600">
                                            {format!("{:.1}%",
                                                if current_stats.cache_hits + current_stats.cache_misses > 0 {
                                                    (current_stats.blocked_queries as f64 /
                                                     (current_stats.cache_hits + current_stats.cache_misses) as f64) * 100.0
                                                } else {
                                                    0.0
                                                }
                                            )}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded p-3">
                            <h4 class="text-sm font-medium text-gray-700 mb-2">Top Domains</h4>
                            <div class="space-y-1">
                                {current_stats.top_domains.iter().take(5).map(|(domain, count)| {
                                    view! {
                                        <div class="flex justify-between text-sm">
                                            <span class="text-gray-600 truncate">{domain.clone()}</span>
                                            <span class="font-medium">{count}</span>
                                        </div>
                                    }
                                }).collect_view()}
                            </div>
                        </div>

                        <div class="bg-white rounded p-3">
                            <h4 class="text-sm font-medium text-gray-700 mb-2">Query Types</h4>
                            <div class="space-y-1">
                                {current_stats.query_types.iter().map(|(query_type, count)| {
                                    view! {
                                        <div class="flex justify-between text-sm">
                                            <span class="text-gray-600">{query_type.clone()}</span>
                                            <span class="font-medium">{count}</span>
                                        </div>
                                    }
                                }).collect_view()}
                            </div>
                        </div>
                    }
                }}
            </div>
        </div>
    }
}

/// Utility function to format duration
fn format_duration(duration: std::time::Duration) -> String {
    let total_seconds = duration.as_secs();
    let days = total_seconds / 86400;
    let hours = (total_seconds % 86400) / 3600;
    let minutes = (total_seconds % 3600) / 60;

    if days > 0 {
        format!("{}d {}h {}m", days, hours, minutes)
    } else if hours > 0 {
        format!("{}h {}m", hours, minutes)
    } else {
        format!("{}m", minutes)
    }
}

/// Get default DNS applications
fn get_default_dns_apps() -> Vec<DnsApp> {
    let mut apps = vec![
        DnsApp::new(DnsAppType::DnsServer),
        DnsApp::new(DnsAppType::DnsFiltering),
        DnsApp::new(DnsAppType::DnsAnalytics),
        DnsApp::new(DnsAppType::DnsForwarder),
        DnsApp::new(DnsAppType::DnsCache),
        DnsApp::new(DnsAppType::DnsBlocklist),
        DnsApp::new(DnsAppType::DnsResolver),
        DnsApp::new(DnsAppType::DnsMonitoring),
    ];

    // Configure some apps with realistic settings
    if let Some(server) = apps.iter_mut().find(|app| app.app_type == DnsAppType::DnsServer) {
        server.enabled = true;
        server.running = true;
        server.queries_per_second = 45.2;
        server.cache_hit_rate = 0.78;
        server.total_queries = 125678;
        server.uptime = Some(std::time::Duration::from_secs(86400 * 3)); // 3 days
        server.last_restart = Some(chrono::Utc::now() - chrono::Duration::days(3));
        server.config.insert("listen_port".to_string(), "53".to_string());
        server.config.insert("interface".to_string(), "0.0.0.0".to_string());

        // Add sample zones
        server.zones.push(DnsZone {
            id: "zone-local".to_string(),
            name: "local".to_string(),
            zone_type: "master".to_string(),
            records: vec![
                DnsRecord {
                    id: "rec-001".to_string(),
                    name: "router.local".to_string(),
                    record_type: DnsRecordType::A,
                    value: "192.168.1.1".to_string(),
                    ttl: 300,
                    priority: None,
                    weight: None,
                    port: None,
                    enabled: true,
                    created_at: chrono::Utc::now() - chrono::Duration::days(1),
                    updated_at: chrono::Utc::now() - chrono::Duration::days(1),
                },
            ],
            enabled: true,
            serial: 2024010101,
            refresh: 3600,
            retry: 1800,
            expire: 604800,
            minimum: 300,
            created_at: chrono::Utc::now() - chrono::Duration::days(1),
            updated_at: chrono::Utc::now() - chrono::Duration::days(1),
        });
    }

    if let Some(filtering) = apps.iter_mut().find(|app| app.app_type == DnsAppType::DnsFiltering) {
        filtering.enabled = true;
        filtering.running = true;
        filtering.queries_per_second = 12.8;
        filtering.blocked_queries = 1234;
        filtering.total_queries = 45678;
        filtering.uptime = Some(std::time::Duration::from_secs(86400 * 2)); // 2 days
        filtering.config.insert("blocklist_url".to_string(), "https://someonewhocares.org/hosts/zero/hosts".to_string());
        filtering.config.insert("update_interval".to_string(), "24".to_string());
    }

    apps
}

/// Get mock DNS statistics
fn get_mock_dns_stats() -> DnsQueryStats {
    DnsQueryStats {
        timestamp: chrono::Utc::now(),
        queries_per_second: 58.3,
        cache_hits: 45678,
        cache_misses: 12345,
        blocked_queries: 1234,
        response_time_ms: 12.5,
        top_domains: vec![
            ("google.com".to_string(), 1234),
            ("facebook.com".to_string(), 987),
            ("youtube.com".to_string(), 765),
            ("github.com".to_string(), 543),
            ("stackoverflow.com".to_string(), 321),
        ],
        query_types: HashMap::from([
            ("A".to_string(), 35678),
            ("AAAA".to_string(), 12345),
            ("CNAME".to_string(), 5678),
            ("MX".to_string(), 1234),
            ("TXT".to_string(), 987),
            ("NS".to_string(), 543),
        ]),
    }
}

/// Load DNS applications from system (mock implementation)
async fn load_dns_apps() -> Result<Vec<DnsApp>, String> {
    // Simulate network delay
    gloo_timers::future::TimeoutFuture::new(600).await;

    // In a real implementation, this would query the DNS system
    // For now, return mock data
    Ok(get_default_dns_apps())
}

/// Load DNS statistics (mock implementation)
async fn load_dns_stats() -> Result<DnsQueryStats, String> {
    // Simulate network delay
    gloo_timers::future::TimeoutFuture::new(300).await;

    // In a real implementation, this would query DNS statistics
    // For now, return mock data with some variation
    let mut stats = get_mock_dns_stats();

    // Add some random variation to make it look live
    use js_sys::Math;
    stats.queries_per_second = 50.0 + (Math::random() * 20.0);
    stats.response_time_ms = 10.0 + (Math::random() * 10.0);
    stats.cache_hits += (Math::random() * 100.0) as u64;
    stats.cache_misses += (Math::random() * 50.0) as u64;

    Ok(stats)
}

/// Handle DNS app actions (mock implementation)
async fn handle_dns_app_action(action: DnsAppAction) -> Result<Vec<DnsApp>, String> {
    // Simulate processing delay
    gloo_timers::future::TimeoutFuture::new(1000).await;

    match action {
        DnsAppAction::Enable(app_id) => {
            log::info!("Enabling DNS app: {}", app_id);
            // In real implementation, would enable the DNS application
        }
        DnsAppAction::Disable(app_id) => {
            log::info!("Disabling DNS app: {}", app_id);
            // In real implementation, would disable the DNS application
        }
        DnsAppAction::Start(app_id) => {
            log::info!("Starting DNS app: {}", app_id);
            // In real implementation, would start the DNS service
        }
        DnsAppAction::Stop(app_id) => {
            log::info!("Stopping DNS app: {}", app_id);
            // In real implementation, would stop the DNS service
        }
        DnsAppAction::Restart(app_id) => {
            log::info!("Restarting DNS app: {}", app_id);
            // In real implementation, would restart the DNS service
        }
        DnsAppAction::Configure(app_id, config) => {
            log::info!("Configuring DNS app: {} with {:?}", app_id, config);
            // In real implementation, would update DNS app configuration
        }
        DnsAppAction::AddZone(app_id, zone) => {
            log::info!("Adding DNS zone: {} to app: {}", zone.name, app_id);
            // In real implementation, would add DNS zone
        }
        DnsAppAction::RemoveZone(app_id, zone_id) => {
            log::info!("Removing DNS zone: {} from app: {}", zone_id, app_id);
            // In real implementation, would remove DNS zone
        }
        DnsAppAction::FlushCache(app_id) => {
            log::info!("Flushing DNS cache for app: {}", app_id);
            // In real implementation, would flush DNS cache
        }
    }

    // Return updated apps list
    load_dns_apps().await
}
