//! Table components for OpenWrt LuCI interface
//!
//! This module provides reusable table components for displaying data
//! in a structured format following OpenWrt design patterns.

use leptos::*;
use std::collections::HashMap;
use std::rc::Rc;
use serde::{Deserialize, Serialize};
use web_sys::HtmlInputElement;

/// Sort direction for table columns
#[derive(Clone, PartialEq, Debug)]
pub enum SortDirection {
    Ascending,
    Descending,
}

/// Filter type for table columns
#[derive(Clone, PartialEq, Debug)]
pub enum FilterType {
    Text,
    Number,
    Date,
    Select(Vec<String>),
}

/// Column data type for proper sorting
#[derive(Clone, PartialEq, Debug)]
pub enum ColumnDataType {
    Text,
    Number,
    Date,
    Boolean,
}

/// Enhanced table column definition
#[derive(Clone)]
pub struct AdvancedTableColumn {
    pub key: String,
    pub label: String,
    pub sortable: bool,
    pub filterable: bool,
    pub filter_type: FilterType,
    pub data_type: ColumnDataType,
    pub width: Option<String>,
    pub align: TableAlign,
    pub hidden: bool,
}

impl AdvancedTableColumn {
    pub fn new(key: &str, label: &str) -> Self {
        Self {
            key: key.to_string(),
            label: label.to_string(),
            sortable: true,
            filterable: true,
            filter_type: FilterType::Text,
            data_type: ColumnDataType::Text,
            width: None,
            align: TableAlign::Left,
            hidden: false,
        }
    }

    pub fn sortable(mut self, sortable: bool) -> Self {
        self.sortable = sortable;
        self
    }

    pub fn filterable(mut self, filterable: bool) -> Self {
        self.filterable = filterable;
        self
    }

    pub fn filter_type(mut self, filter_type: FilterType) -> Self {
        self.filter_type = filter_type;
        self
    }

    pub fn data_type(mut self, data_type: ColumnDataType) -> Self {
        self.data_type = data_type;
        self
    }

    pub fn width(mut self, width: &str) -> Self {
        self.width = Some(width.to_string());
        self
    }

    pub fn align(mut self, align: TableAlign) -> Self {
        self.align = align;
        self
    }

    pub fn hidden(mut self, hidden: bool) -> Self {
        self.hidden = hidden;
        self
    }
}

/// Basic table column definition (for backward compatibility)
#[derive(Clone)]
pub struct TableColumn {
    pub key: String,
    pub label: String,
    pub sortable: bool,
    pub width: Option<String>,
    pub align: TableAlign,
}

/// Table cell alignment
#[derive(Clone, PartialEq)]
pub enum TableAlign {
    Left,
    Center,
    Right,
}

impl TableAlign {
    fn to_class(&self) -> &'static str {
        match self {
            TableAlign::Left => "text-left",
            TableAlign::Center => "text-center",
            TableAlign::Right => "text-right",
        }
    }
}

/// Enhanced table row data
#[derive(Clone, PartialEq)]
pub struct AdvancedTableRow {
    pub id: String,
    pub cells: HashMap<String, AdvancedTableCell>,
    pub actions: Vec<TableAction>,
    pub selectable: bool,
    pub expandable: bool,
    pub expanded_content: Option<View>,
}

impl AdvancedTableRow {
    pub fn new(id: &str) -> Self {
        Self {
            id: id.to_string(),
            cells: HashMap::new(),
            actions: Vec::new(),
            selectable: true,
            expandable: false,
            expanded_content: None,
        }
    }

    pub fn cell(mut self, key: &str, content: View, raw_value: &str) -> Self {
        self.cells.insert(key.to_string(), AdvancedTableCell {
            key: key.to_string(),
            content,
            raw_value: raw_value.to_string(),
        });
        self
    }

    pub fn action(mut self, action: TableAction) -> Self {
        self.actions.push(action);
        self
    }

    pub fn selectable(mut self, selectable: bool) -> Self {
        self.selectable = selectable;
        self
    }

    pub fn expandable(mut self, expandable: bool, content: Option<View>) -> Self {
        self.expandable = expandable;
        self.expanded_content = content;
        self
    }
}

/// Enhanced table cell data
#[derive(Clone, PartialEq)]
pub struct AdvancedTableCell {
    pub key: String,
    pub content: View,
    pub raw_value: String, // For sorting and filtering
}

/// Bulk action definition
#[derive(Clone)]
pub struct BulkAction {
    pub id: String,
    pub label: String,
    pub icon: Option<View>,
    pub variant: super::navigation::ButtonVariant,
    pub requires_confirmation: bool,
    pub confirmation_message: Option<String>,
}

/// Table row data (for backward compatibility)
pub struct TableRow {
    pub id: String,
    pub cells: Vec<TableCell>,
    pub actions: Vec<TableAction>,
}

/// Table cell data (for backward compatibility)
#[derive(Clone)]
pub struct TableCell {
    pub key: String,
    pub content: View,
    pub raw_value: String, // For sorting
}

/// Table action button
pub struct TableAction {
    pub label: String,
    pub icon: Option<View>,
    pub variant: super::navigation::ButtonVariant,
    pub on_click: Option<Box<dyn Fn(String) + 'static>>, // Receives row ID
}

impl Clone for TableAction {
    fn clone(&self) -> Self {
        Self {
            label: self.label.clone(),
            icon: self.icon.clone(),
            variant: self.variant.clone(),
            on_click: None, // Can't clone closures, so we set to None
        }
    }
}

impl PartialEq for TableAction {
    fn eq(&self, other: &Self) -> bool {
        self.label == other.label && self.variant == other.variant
        // We ignore icon and on_click for comparison
    }
}

/// Comprehensive data table component
#[component]
pub fn DataTable(
    /// Table columns configuration
    columns: Vec<TableColumn>,
    /// Table row data
    rows: Vec<TableRow>,
    /// Whether the table is loading
    #[prop(default = false)]
    loading: bool,
    /// Empty state message
    #[prop(default = "No data available".to_string())]
    empty_message: String,
    /// Whether to show row actions
    #[prop(default = true)]
    show_actions: bool,
    /// Additional CSS classes for table
    #[prop(optional)]
    class: Option<String>,
) -> impl IntoView {
    let table_classes = format!("table {}", class.unwrap_or_default());

    view! {
        <div class="overflow-x-auto">
            <table class={table_classes}>
                <thead class="table-header">
                    <tr>
                        {columns.iter().map(|col| {
                            let header_class = format!("table-header-cell {}", col.align.to_class());
                            let style = col.width.as_ref().map(|w| format!("width: {}", w));

                            view! {
                                <th class={header_class} style={style.unwrap_or_default()}>
                                    <div class="flex items-center space-x-1">
                                        <span>{col.label.clone()}</span>
                                        {col.sortable.then(|| view! {
                                            <button class="text-gray-400 hover:text-gray-600">
                                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M5 12l5-5 5 5H5z"/>
                                                </svg>
                                            </button>
                                        })}
                                    </div>
                                </th>
                            }
                        }).collect::<Vec<_>>()}
                        {show_actions.then(|| view! {
                            <th class="table-header-cell text-right">
                                "Actions"
                            </th>
                        })}
                    </tr>
                </thead>
                <tbody class="table-body">
                    {if loading {
                        vec![view! {
                            <tr>
                                <td colspan={columns.len() + if show_actions { 1 } else { 0 }} class="table-cell text-center py-8">
                                    <div class="flex items-center justify-center space-x-2">
                                        <div class="loading-spinner"></div>
                                        <span class="text-gray-500">"Loading..."</span>
                                    </div>
                                </td>
                            </tr>
                        }]
                    } else if rows.is_empty() {
                        vec![view! {
                            <tr>
                                <td colspan={columns.len() + if show_actions { 1 } else { 0 }} class="table-cell text-center py-8 text-gray-500">
                                    {empty_message}
                                </td>
                            </tr>
                        }]
                    } else {
                        rows.into_iter().map(|row| {
                            view! {
                                <tr class="table-row">
                                    {columns.iter().map(|col| {
                                        let cell = row.cells.iter().find(|c| c.key == col.key);
                                        let cell_class = format!("table-cell {}", col.align.to_class());

                                        view! {
                                            <td class={cell_class}>
                                                {cell.map(|cell_data| cell_data.content.clone()).unwrap_or_else(|| {
                                                    view! { <span class="text-gray-400">"-"</span> }.into_view()
                                                })}
                                            </td>
                                        }
                                    }).collect::<Vec<_>>()}
                                    {if show_actions && !row.actions.is_empty() {
                                        view! {
                                            <td class="table-cell text-right">
                                                <div class="flex items-center justify-end space-x-2">
                                                    {row.actions.into_iter().map(|action| {
                                                        let row_id = row.id.clone();
                                                        view! {
                                                            <button
                                                                class={action.variant.to_class()}
                                                                title={action.label.clone()}
                                                                on:click=move |_| {
                                                                    if let Some(handler) = &action.on_click {
                                                                        handler(row_id.clone());
                                                                    }
                                                                }
                                                            >
                                                                {action.icon.unwrap_or_else(|| {
                                                                    view! { <span>{action.label}</span> }.into_view()
                                                                })}
                                                            </button>
                                                        }
                                                    }).collect::<Vec<_>>()}
                                                </div>
                                            </td>
                                        }.into_view()
                                    } else if show_actions {
                                        view! { <td class="table-cell"></td> }.into_view()
                                    } else {
                                        view! { <td></td> }.into_view()
                                    }}
                                </tr>
                            }
                        }).collect::<Vec<_>>()
                    }}
                </tbody>
            </table>
        </div>
    }
}

/// Advanced data table component with sorting, filtering, pagination, and bulk operations
#[component]
pub fn AdvancedDataTable(
    /// Table columns configuration
    columns: Vec<AdvancedTableColumn>,
    /// Table row data
    rows: Vec<AdvancedTableRow>,
    /// Whether the table is loading
    #[prop(default = false)]
    loading: bool,
    /// Empty state message
    #[prop(default = "No data available".to_string())]
    empty_message: String,
    /// Whether to show row actions
    #[prop(default = true)]
    show_actions: bool,
    /// Whether to enable row selection
    #[prop(default = false)]
    selectable: bool,
    /// Bulk actions available when rows are selected
    #[prop(default = Vec::new())]
    bulk_actions: Vec<BulkAction>,
    /// Whether to show search bar
    #[prop(default = true)]
    searchable: bool,
    /// Whether to enable pagination
    #[prop(default = true)]
    paginated: bool,
    /// Page size options
    #[prop(default = vec![10, 25, 50, 100])]
    page_size_options: Vec<usize>,
    /// Default page size
    #[prop(default = 25)]
    default_page_size: usize,
    /// Whether to show export options
    #[prop(default = false)]
    exportable: bool,
    /// Whether to show column filters
    #[prop(default = true)]
    show_column_filters: bool,
    /// Whether to show column visibility controls
    #[prop(default = false)]
    show_column_controls: bool,
    /// Whether to enable multi-column sorting
    #[prop(default = false)]
    multi_sort: bool,
    /// Whether to enable virtual scrolling for large datasets
    #[prop(default = false)]
    virtual_scrolling: bool,
    /// Virtual scrolling item height (pixels)
    #[prop(default = 48)]
    virtual_item_height: usize,
    /// Additional CSS classes for table
    #[prop(optional)]
    class: Option<String>,
    /// Callback when rows are selected
    #[prop(optional)]
    on_selection_change: Option<Rc<dyn Fn(Vec<String>)>>,
    /// Callback when bulk action is triggered
    #[prop(optional)]
    on_bulk_action: Option<Rc<dyn Fn(String, Vec<String>)>>,
) -> impl IntoView {
    // State management
    let (search_query, set_search_query) = create_signal(String::new());
    let (sort_column, set_sort_column) = create_signal(None::<String>);
    let (sort_direction, set_sort_direction) = create_signal(SortDirection::Ascending);
    let (column_filters, set_column_filters) = create_signal(HashMap::<String, String>::new());
    let (current_page, set_current_page) = create_signal(1usize);
    let (page_size, set_page_size) = create_signal(default_page_size);
    let (selected_rows, set_selected_rows) = create_signal(Vec::<String>::new());
    let (expanded_rows, set_expanded_rows) = create_signal(Vec::<String>::new());
    let (hidden_columns, set_hidden_columns) = create_signal(Vec::<String>::new());
    let (sort_columns, set_sort_columns) = create_signal(Vec::<(String, SortDirection)>::new());
    let (show_filters, set_show_filters) = create_signal(false);

    // Clone columns for use in different closures
    let columns_for_memo = columns.clone();
    let columns_for_header = columns.clone();

    // Clone handlers for use in different closures
    let on_selection_change_for_header = on_selection_change.clone();
    let on_selection_change_for_rows = on_selection_change.clone();

    // Filtered and sorted data
    let processed_data = create_memo(move |_| {
        let mut data = rows.clone();
        let search = search_query.get();
        let filters = column_filters.get();
        let sort_col = sort_column.get();
        let sort_dir = sort_direction.get();

        // Apply global search
        if !search.is_empty() {
            data.retain(|row| {
                row.cells.values().any(|cell| {
                    cell.raw_value.to_lowercase().contains(&search.to_lowercase())
                })
            });
        }

        // Apply column filters
        for (col_key, filter_value) in filters.iter() {
            if !filter_value.is_empty() {
                data.retain(|row| {
                    if let Some(cell) = row.cells.get(col_key.as_str()) {
                        cell.raw_value.to_lowercase().contains(&filter_value.to_lowercase())
                    } else {
                        false
                    }
                });
            }
        }

        // Apply sorting
        if let Some(sort_key) = sort_col {
            let column = columns_for_memo.iter().find(|c| c.key == sort_key);
            if let Some(col) = column {
                data.sort_by(|a, b| {
                    let a_val = a.cells.get(sort_key.as_str()).map(|c| c.raw_value.as_str()).unwrap_or("");
                    let b_val = b.cells.get(sort_key.as_str()).map(|c| c.raw_value.as_str()).unwrap_or("");

                    let comparison = match col.data_type {
                        ColumnDataType::Number => {
                            let a_num: f64 = a_val.parse().unwrap_or(0.0);
                            let b_num: f64 = b_val.parse().unwrap_or(0.0);
                            a_num.partial_cmp(&b_num).unwrap_or(std::cmp::Ordering::Equal)
                        },
                        ColumnDataType::Date => {
                            // Simple date comparison - could be enhanced with proper date parsing
                            a_val.cmp(b_val)
                        },
                        ColumnDataType::Boolean => {
                            let a_bool = a_val.parse::<bool>().unwrap_or(false);
                            let b_bool = b_val.parse::<bool>().unwrap_or(false);
                            a_bool.cmp(&b_bool)
                        },
                        _ => a_val.cmp(b_val),
                    };

                    match sort_dir {
                        SortDirection::Ascending => comparison,
                        SortDirection::Descending => comparison.reverse(),
                    }
                });
            }
        }

        data
    });

    // Paginated data
    let paginated_data = create_memo(move |_| {
        let data = processed_data.get();
        let page = current_page.get();
        let size = page_size.get();

        if paginated {
            let start = (page - 1) * size;
            let end = std::cmp::min(start + size, data.len());
            data[start..end].to_vec()
        } else {
            data
        }
    });

    // Total pages calculation
    let total_pages = create_memo(move |_| {
        if paginated {
            let total = processed_data.get().len();
            let size = page_size.get();
            (total + size - 1) / size // Ceiling division
        } else {
            1
        }
    });

    // Handle column sort
    let handle_sort = move |column_key: String| {
        if sort_column.get() == Some(column_key.clone()) {
            // Toggle sort direction
            set_sort_direction.update(|dir| {
                *dir = match dir {
                    SortDirection::Ascending => SortDirection::Descending,
                    SortDirection::Descending => SortDirection::Ascending,
                }
            });
        } else {
            // Set new sort column
            set_sort_column.set(Some(column_key));
            set_sort_direction.set(SortDirection::Ascending);
        }
        set_current_page.set(1); // Reset to first page when sorting
    };

    view! {
        <div class="advanced-data-table">
            // Search and controls bar
            {if searchable || !bulk_actions.is_empty() || exportable {
                view! {
                    <div class="flex items-center justify-between mb-4 space-x-4">
                        // Left side - Search and bulk actions
                        <div class="flex items-center space-x-4">
                            {if searchable {
                                view! {
                                    <div class="relative">
                                        <input
                                            type="text"
                                            placeholder="Search..."
                                            class="form-input pl-10 pr-4 py-2 w-64"
                                            prop:value=search_query
                                            on:input=move |ev| {
                                                let value = event_target_value(&ev);
                                                set_search_query.set(value);
                                                set_current_page.set(1); // Reset to first page when searching
                                            }
                                        />
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                            </svg>
                                        </div>
                                    </div>
                                }.into_view()
                            } else {
                                view! { <div></div> }.into_view()
                            }}

                            // Bulk actions
                            {if selectable && !bulk_actions.is_empty() {
                                view! {
                                    <div class="flex items-center space-x-2">
                                        <Show when=move || !selected_rows.get().is_empty()>
                                            <span class="text-sm text-gray-600">
                                                {move || format!("{} selected", selected_rows.get().len())}
                                            </span>
                                            {bulk_actions.iter().map(|action| {
                                                let action_id = action.id.clone();
                                                let action_label = action.label.clone();
                                                let requires_confirmation = action.requires_confirmation;
                                                let confirmation_msg = action.confirmation_message.clone();
                                                let bulk_handler = on_bulk_action.clone();

                                                view! {
                                                    <button
                                                        class={action.variant.to_class()}
                                                        on:click=move |_| {
                                                            let selected = selected_rows.get();
                                                            if !selected.is_empty() {
                                                                if requires_confirmation {
                                                                    let msg = confirmation_msg.clone().unwrap_or_else(|| {
                                                                        format!("Are you sure you want to {} {} items?", action_label.to_lowercase(), selected.len())
                                                                    });
                                                                    if web_sys::window().unwrap().confirm_with_message(&msg).unwrap_or(false) {
                                                                        if let Some(ref handler) = bulk_handler {
                                                                            handler(action_id.clone(), selected);
                                                                        }
                                                                    }
                                                                } else {
                                                                    if let Some(ref handler) = bulk_handler {
                                                                        handler(action_id.clone(), selected);
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    >
                                                        {action.icon.clone().unwrap_or_else(|| {
                                                            view! { <span>{action.label.clone()}</span> }.into_view()
                                                        })}
                                                    </button>
                                                }
                                            }).collect::<Vec<_>>()}
                                        </Show>
                                    </div>
                                }.into_view()
                            } else {
                                view! { <div></div> }.into_view()
                            }}
                        </div>

                        // Right side - Column controls, Export and page size
                        <div class="flex items-center space-x-4">
                            {if show_column_controls {
                                let columns_for_controls = columns.clone();
                                let (show_column_menu, set_show_column_menu) = create_signal(false);

                                view! {
                                    <div class="flex items-center space-x-2">
                                        <button
                                            class="btn btn-secondary btn-sm"
                                            on:click=move |_| set_show_filters.update(|show| *show = !*show)
                                        >
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"/>
                                            </svg>
                                            "Filters"
                                        </button>

                                        <div class="relative">
                                            <button
                                                class="btn btn-secondary btn-sm"
                                                on:click=move |_| set_show_column_menu.update(|show| *show = !*show)
                                            >
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2"/>
                                                </svg>
                                                "Columns"
                                            </button>

                                            {if show_column_menu.get() {
                                                view! {
                                                    <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-50">
                                                        <div class="py-1">
                                                            <div class="px-3 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">
                                                                "Show/Hide Columns"
                                                            </div>
                                                            {columns_for_controls.iter().map(|col| {
                                                                let col_key = col.key.clone();
                                                                let col_label = col.label.clone();
                                                                let col_key_for_toggle = col.key.clone();

                                                                view! {
                                                                    <label class="flex items-center px-3 py-2 hover:bg-gray-50 cursor-pointer">
                                                                        <input
                                                                            type="checkbox"
                                                                            class="form-checkbox h-4 w-4 text-blue-600"
                                                                            prop:checked=move || !hidden_columns.get().contains(&col_key)
                                                                            on:change=move |_| {
                                                                                set_hidden_columns.update(|hidden| {
                                                                                    if hidden.contains(&col_key_for_toggle) {
                                                                                        hidden.retain(|k| k != &col_key_for_toggle);
                                                                                    } else {
                                                                                        hidden.push(col_key_for_toggle.clone());
                                                                                    }
                                                                                });
                                                                            }
                                                                        />
                                                                        <span class="ml-2 text-sm text-gray-700">{col_label}</span>
                                                                    </label>
                                                                }
                                                            }).collect::<Vec<_>>()}
                                                        </div>
                                                    </div>
                                                }.into_view()
                                            } else {
                                                view! { <div style="display: none;"></div> }.into_view()
                                            }}
                                        </div>
                                    </div>
                                }.into_view()
                            } else {
                                view! { <div></div> }.into_view()
                            }}

                            {if paginated {
                                view! {
                                    <div class="flex items-center space-x-2">
                                        <label class="text-sm text-gray-600">"Show:"</label>
                                        <select
                                            class="form-select py-1 px-2 text-sm"
                                            on:change=move |ev| {
                                                let value = event_target_value(&ev);
                                                if let Ok(size) = value.parse::<usize>() {
                                                    set_page_size.set(size);
                                                    set_current_page.set(1);
                                                }
                                            }
                                        >
                                            {page_size_options.iter().map(|&size| {
                                                view! {
                                                    <option value={size.to_string()} prop:selected=move || page_size.get() == size>
                                                        {size.to_string()}
                                                    </option>
                                                }
                                            }).collect::<Vec<_>>()}
                                        </select>
                                        <span class="text-sm text-gray-600">"entries"</span>
                                    </div>
                                }.into_view()
                            } else {
                                view! { <div></div> }.into_view()
                            }}

                            {if exportable {
                                let export_data = processed_data.clone();
                                let export_columns = columns.clone();

                                view! {
                                    <div class="flex items-center space-x-2">
                                        <button
                                            class="btn btn-secondary btn-sm"
                                            on:click=move |_| {
                                                // Export as CSV - simplified version
                                                let data = export_data.get();
                                                let cols = &export_columns;

                                                // Create CSV content
                                                let mut csv_content = String::new();

                                                // Header row
                                                let headers: Vec<String> = cols.iter()
                                                    .filter(|col| !col.hidden)
                                                    .map(|col| col.label.clone())
                                                    .collect();
                                                csv_content.push_str(&headers.join(","));
                                                csv_content.push('\n');

                                                // Data rows
                                                for row in data {
                                                    let row_data: Vec<String> = cols.iter()
                                                        .filter(|col| !col.hidden)
                                                        .map(|col| {
                                                            row.cells.get(&col.key)
                                                                .map(|cell| {
                                                                    // Escape CSV values
                                                                    let value = &cell.raw_value;
                                                                    if value.contains(',') || value.contains('"') || value.contains('\n') {
                                                                        format!("\"{}\"", value.replace("\"", "\"\""))
                                                                    } else {
                                                                        value.clone()
                                                                    }
                                                                })
                                                                .unwrap_or_else(|| String::new())
                                                        })
                                                        .collect();
                                                    csv_content.push_str(&row_data.join(","));
                                                    csv_content.push('\n');
                                                }

                                                // Log CSV content for now (can be enhanced later with proper download)
                                                leptos::logging::log!("CSV Export:\n{}", csv_content);
                                            }
                                        >
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                            </svg>
                                            "Export CSV"
                                        </button>
                                    </div>
                                }.into_view()
                            } else {
                                view! { <div></div> }.into_view()
                            }}
                        </div>
                    </div>
                }.into_view()
            } else {
                view! { <div></div> }.into_view()
            }}

            // Table
            <div class="overflow-x-auto border border-gray-200 rounded-lg">
                <table class={format!("table w-full {}", class.unwrap_or_default())}>
                    <thead class="bg-gray-50">
                        <tr>
                            // Select all checkbox
                            {if selectable {
                                let all_selectable_rows = move || {
                                    paginated_data.get().iter()
                                        .filter(|row| row.selectable)
                                        .map(|row| row.id.clone())
                                        .collect::<Vec<_>>()
                                };
                                let all_selected = move || {
                                    let selectable = all_selectable_rows();
                                    let selected = selected_rows.get();
                                    !selectable.is_empty() && selectable.iter().all(|id| selected.contains(id))
                                };

                                view! {
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
                                        <input
                                            type="checkbox"
                                            class="form-checkbox"
                                            prop:checked=all_selected
                                            on:change=move |_| {
                                                let selectable = all_selectable_rows();
                                                if all_selected() {
                                                    // Deselect all
                                                    set_selected_rows.update(|selected| {
                                                        selected.retain(|id| !selectable.contains(id));
                                                    });
                                                } else {
                                                    // Select all
                                                    set_selected_rows.update(|selected| {
                                                        for id in selectable {
                                                            if !selected.contains(&id) {
                                                                selected.push(id);
                                                            }
                                                        }
                                                    });
                                                }
                                                if let Some(handler) = on_selection_change_for_header.as_ref() {
                                                    handler(selected_rows.get());
                                                }
                                            }
                                        />
                                    </th>
                                }.into_view()
                            } else {
                                view! { <th></th> }.into_view()
                            }}

                            // Column headers
                            {columns_for_header.iter().filter(|col| !col.hidden).map(|col| {
                                let col_key = col.key.clone();
                                let col_key_for_sort = col.key.clone();
                                let col_key_for_click = col.key.clone();
                                let col_label = col.label.clone();
                                let is_sortable = col.sortable;
                                let is_sorted = move || sort_column.get() == Some(col_key.clone());
                                let sort_dir = move || sort_direction.get();

                                let header_class = format!(
                                    "px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider {}",
                                    col.align.to_class()
                                );
                                let style = col.width.as_ref().map(|w| format!("width: {}", w));

                                view! {
                                    <th class={header_class} style={style.unwrap_or_default()}>
                                        <div class="flex items-center space-x-1">
                                            {if is_sortable {
                                                view! {
                                                    <button
                                                        class="flex items-center space-x-1 hover:text-gray-700"
                                                        on:click=move |_| handle_sort(col_key_for_click.clone())
                                                    >
                                                        <span>{col_label.clone()}</span>
                                                        <div class="flex flex-col">
                                                            <svg class={format!("w-3 h-3 {}",
                                                                if sort_column.get() == Some(col_key_for_sort.clone()) && sort_dir() == SortDirection::Ascending {
                                                                    "text-blue-600"
                                                                } else {
                                                                    "text-gray-300"
                                                                }
                                                            )} fill="currentColor" viewBox="0 0 20 20">
                                                                <path d="M5 12l5-5 5 5H5z"/>
                                                            </svg>
                                                            <svg class={format!("w-3 h-3 -mt-1 {}",
                                                                if sort_column.get() == Some(col_key_for_sort.clone()) && sort_dir() == SortDirection::Descending {
                                                                    "text-blue-600"
                                                                } else {
                                                                    "text-gray-300"
                                                                }
                                                            )} fill="currentColor" viewBox="0 0 20 20">
                                                                <path d="M15 8l-5 5-5-5h10z"/>
                                                            </svg>
                                                        </div>
                                                    </button>
                                                }.into_view()
                                            } else {
                                                view! { <span>{col_label}</span> }.into_view()
                                            }}
                                        </div>
                                    </th>
                                }
                            }).collect::<Vec<_>>()}

                            // Actions column
                            {if show_actions {
                                view! {
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        "Actions"
                                    </th>
                                }.into_view()
                            } else {
                                view! { <th></th> }.into_view()
                            }}
                        </tr>

                        // Filter row
                        {if show_column_filters && show_filters.get() {
                            view! {
                                <tr class="bg-gray-100 border-t border-gray-200">
                                    // Empty cell for select column
                                    {if selectable {
                                        view! { <th class="px-6 py-2"></th> }.into_view()
                                    } else {
                                        view! { <th></th> }.into_view()
                                    }}

                                    // Filter inputs for each column
                                    {columns_for_header.iter().filter(|col| !col.hidden).map(|col| {
                                        let col_key = col.key.clone();
                                        let col_key_for_filter = col.key.clone();
                                        let is_filterable = col.filterable;
                                        let filter_type = col.filter_type.clone();

                                        view! {
                                            <th class="px-6 py-2">
                                                {if is_filterable {
                                                    match filter_type {
                                                        FilterType::Text => {
                                                            view! {
                                                                <input
                                                                    type="text"
                                                                    class="form-input w-full text-xs"
                                                                    placeholder="Filter..."
                                                                    prop:value=move || column_filters.get().get(&col_key).cloned().unwrap_or_default()
                                                                    on:input=move |ev| {
                                                                        let value = event_target_value(&ev);
                                                                        set_column_filters.update(|filters| {
                                                                            if value.is_empty() {
                                                                                filters.remove(&col_key_for_filter);
                                                                            } else {
                                                                                filters.insert(col_key_for_filter.clone(), value);
                                                                            }
                                                                        });
                                                                        set_current_page.set(1);
                                                                    }
                                                                />
                                                            }.into_view()
                                                        },
                                                        FilterType::Select(options) => {
                                                            view! {
                                                                <select
                                                                    class="form-select w-full text-xs"
                                                                    on:change=move |ev| {
                                                                        let value = event_target_value(&ev);
                                                                        set_column_filters.update(|filters| {
                                                                            if value.is_empty() {
                                                                                filters.remove(&col_key_for_filter);
                                                                            } else {
                                                                                filters.insert(col_key_for_filter.clone(), value);
                                                                            }
                                                                        });
                                                                        set_current_page.set(1);
                                                                    }
                                                                >
                                                                    <option value="">"All"</option>
                                                                    {options.iter().map(|option| {
                                                                        let option_value = option.clone();
                                                                        view! {
                                                                            <option value={option_value.clone()}>
                                                                                {option_value}
                                                                            </option>
                                                                        }
                                                                    }).collect::<Vec<_>>()}
                                                                </select>
                                                            }.into_view()
                                                        },
                                                        FilterType::Number => {
                                                            view! {
                                                                <input
                                                                    type="number"
                                                                    class="form-input w-full text-xs"
                                                                    placeholder="Filter..."
                                                                    prop:value=move || column_filters.get().get(&col_key).cloned().unwrap_or_default()
                                                                    on:input=move |ev| {
                                                                        let value = event_target_value(&ev);
                                                                        set_column_filters.update(|filters| {
                                                                            if value.is_empty() {
                                                                                filters.remove(&col_key_for_filter);
                                                                            } else {
                                                                                filters.insert(col_key_for_filter.clone(), value);
                                                                            }
                                                                        });
                                                                        set_current_page.set(1);
                                                                    }
                                                                />
                                                            }.into_view()
                                                        },
                                                        FilterType::Date => {
                                                            view! {
                                                                <input
                                                                    type="date"
                                                                    class="form-input w-full text-xs"
                                                                    prop:value=move || column_filters.get().get(&col_key).cloned().unwrap_or_default()
                                                                    on:input=move |ev| {
                                                                        let value = event_target_value(&ev);
                                                                        set_column_filters.update(|filters| {
                                                                            if value.is_empty() {
                                                                                filters.remove(&col_key_for_filter);
                                                                            } else {
                                                                                filters.insert(col_key_for_filter.clone(), value);
                                                                            }
                                                                        });
                                                                        set_current_page.set(1);
                                                                    }
                                                                />
                                                            }.into_view()
                                                        }
                                                    }
                                                } else {
                                                    view! { <div></div> }.into_view()
                                                }}
                                            </th>
                                        }
                                    }).collect::<Vec<_>>()}

                                    // Empty cell for actions column
                                    {if show_actions {
                                        view! { <th class="px-6 py-2"></th> }.into_view()
                                    } else {
                                        view! { <th></th> }.into_view()
                                    }}
                                </tr>
                            }.into_view()
                        } else {
                            view! { <tr style="display: none;"></tr> }.into_view()
                        }}
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {
                            move || {
                            if loading {
                                let colspan = columns.len() +
                                    (if selectable { 1 } else { 0 }) +
                                    (if show_actions { 1 } else { 0 });
                                vec![view! {
                                    <tr>
                                        <td colspan={colspan} class="px-6 py-8 text-center">
                                            <div class="flex items-center justify-center space-x-2">
                                                <div class="loading-spinner"></div>
                                                <span class="text-gray-500">"Loading..."</span>
                                            </div>
                                        </td>
                                    </tr>
                                }]
                            } else {
                                let data = paginated_data.get();
                                if data.is_empty() {
                                    let colspan = columns.len() +
                                        (if selectable { 1 } else { 0 }) +
                                        (if show_actions { 1 } else { 0 });
                                    vec![view! {
                                        <tr>
                                            <td colspan={colspan} class="px-6 py-8 text-center text-gray-500">
                                                {empty_message.clone()}
                                            </td>
                                        </tr>
                                    }]
                                } else {
                                    data.into_iter().map(|row| {
                                        let row_id = row.id.clone();
                                        let row_id_for_selection = row.id.clone();
                                        let row_id_for_expansion = row.id.clone();
                                        let row_id_for_checkbox = row.id.clone();
                                        let is_selected = move || selected_rows.get().contains(&row_id_for_selection);
                                        let is_expanded = move || expanded_rows.get().contains(&row_id_for_expansion);

                                        view! {
                                            <tr class="hover:bg-gray-50">
                                                // Selection checkbox
                                                {if selectable && row.selectable {
                                                    let selection_handler = on_selection_change_for_rows.clone();
                                                    view! {
                                                        <td class="px-6 py-4 whitespace-nowrap w-12">
                                                            <input
                                                                type="checkbox"
                                                                class="form-checkbox"
                                                                prop:checked=is_selected
                                                                on:change=move |_| {
                                                                    set_selected_rows.update(|selected| {
                                                                        if let Some(pos) = selected.iter().position(|id| id == &row_id_for_checkbox) {
                                                                            selected.remove(pos);
                                                                        } else {
                                                                            selected.push(row_id_for_checkbox.clone());
                                                                        }
                                                                    });
                                                                    if let Some(ref handler) = selection_handler {
                                                                        handler(selected_rows.get());
                                                                    }
                                                                }
                                                            />
                                                        </td>
                                                    }.into_view()
                                                } else if selectable {
                                                    view! { <td class="px-6 py-4 w-12"></td> }.into_view()
                                                } else {
                                                    view! { <td></td> }.into_view()
                                                }}

                                                // Data cells
                                                {columns.iter().filter(|col| !col.hidden).map(|col| {
                                                    let cell = row.cells.get(&col.key);
                                                    let cell_class = format!(
                                                        "px-6 py-4 whitespace-nowrap text-sm {}",
                                                        col.align.to_class()
                                                    );

                                                    view! {
                                                        <td class={cell_class}>
                                                            {cell.map(|cell_data| cell_data.content.clone()).unwrap_or_else(|| {
                                                                view! { <span class="text-gray-400">"-"</span> }.into_view()
                                                            })}
                                                        </td>
                                                    }
                                                }).collect::<Vec<_>>()}

                                                // Actions
                                                {if show_actions && !row.actions.is_empty() {
                                                    view! {
                                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                            <div class="flex items-center justify-end space-x-2">
                                                                {row.actions.into_iter().map(|action| {
                                                                    let action_row_id = row_id.clone();
                                                                    view! {
                                                                        <button
                                                                            class={action.variant.to_class()}
                                                                            title={action.label.clone()}
                                                                            on:click=move |_| {
                                                                                if let Some(handler) = &action.on_click {
                                                                                    handler(action_row_id.clone());
                                                                                }
                                                                            }
                                                                        >
                                                                            {action.icon.unwrap_or_else(|| {
                                                                                view! { <span>{action.label}</span> }.into_view()
                                                                            })}
                                                                        </button>
                                                                    }
                                                                }).collect::<Vec<_>>()}
                                                            </div>
                                                        </td>
                                                    }.into_view()
                                                } else if show_actions {
                                                    view! { <td class="px-6 py-4"></td> }.into_view()
                                                } else {
                                                    view! { <td></td> }.into_view()
                                                }}
                                            </tr>
                                        }
                                    }).collect::<Vec<_>>()
                                }
                            }
                        }}
                    </tbody>
                </table>
            </div>

            // Pagination
            {if paginated {
                let total_items = move || processed_data.get().len();
                let current_page_num = move || current_page.get();
                let total_pages_num = move || total_pages.get();
                let page_size_num = move || page_size.get();

                view! {
                    <div class="flex items-center justify-between mt-4">
                        <div class="text-sm text-gray-700">
                            {move || {
                                let total = total_items();
                                let page = current_page_num();
                                let size = page_size_num();
                                let start = if total == 0 { 0 } else { (page - 1) * size + 1 };
                                let end = std::cmp::min(page * size, total);
                                format!("Showing {} to {} of {} entries", start, end, total)
                            }}
                        </div>

                        <div class="flex items-center space-x-2">
                            // Previous button
                            <button
                                class="btn btn-secondary btn-sm"
                                disabled=move || current_page_num() <= 1
                                on:click=move |_| {
                                    if current_page_num() > 1 {
                                        set_current_page.update(|page| *page -= 1);
                                    }
                                }
                            >
                                "Previous"
                            </button>

                            // Page numbers
                            {move || {
                                let current = current_page_num();
                                let total = total_pages_num();
                                let mut pages = Vec::new();

                                // Show first page
                                if total > 0 {
                                    pages.push(1);
                                }

                                // Show pages around current
                                let start = std::cmp::max(2, current.saturating_sub(2));
                                let end = std::cmp::min(total, current + 2);

                                if start > 2 {
                                    pages.push(0); // Ellipsis marker
                                }

                                for page in start..=end {
                                    if page != 1 && page <= total {
                                        pages.push(page);
                                    }
                                }

                                if end < total - 1 {
                                    pages.push(0); // Ellipsis marker
                                }

                                // Show last page
                                if total > 1 && end < total {
                                    pages.push(total);
                                }

                                pages.into_iter().map(|page| {
                                    if page == 0 {
                                        view! { <span class="px-2 text-gray-400">"..."</span> }.into_view()
                                    } else {
                                        let is_current = page == current;
                                        view! {
                                            <button
                                                class={if is_current { "btn btn-primary btn-sm" } else { "btn btn-secondary btn-sm" }}
                                                on:click=move |_| set_current_page.set(page)
                                            >
                                                {page.to_string()}
                                            </button>
                                        }.into_view()
                                    }
                                }).collect::<Vec<_>>()
                            }}

                            // Next button
                            <button
                                class="btn btn-secondary btn-sm"
                                disabled=move || current_page_num() >= total_pages_num()
                                on:click=move |_| {
                                    if current_page_num() < total_pages_num() {
                                        set_current_page.update(|page| *page += 1);
                                    }
                                }
                            >
                                "Next"
                            </button>
                        </div>
                    </div>
                }.into_view()
            } else {
                view! { <div></div> }.into_view()
            }}
        </div>
    }
}
