//! Navigation and button components for OpenWrt LuCI interface
//!
//! This module provides reusable navigation and button components following
//! OpenWrt design patterns and optimized for embedded device interfaces.

use leptos::*;
use leptos_router::*;
use std::collections::HashMap;
use serde::{Deserialize, Serialize};

/// Button variant types
#[derive(Clone, PartialEq)]
pub enum ButtonVariant {
    Primary,
    Secondary,
    Success,
    Warning,
    Error,
    Danger,
    Ghost,
}

/// Button size types
#[derive(Clone, PartialEq)]
pub enum ButtonSize {
    Small,
    Medium,
    Large,
}

impl ButtonVariant {
    pub fn to_class(&self) -> &'static str {
        match self {
            ButtonVariant::Primary => "btn-primary",
            ButtonVariant::Secondary => "btn-secondary",
            ButtonVariant::Success => "btn-success",
            ButtonVariant::Warning => "btn-warning",
            ButtonVariant::Error => "btn-error",
            ButtonVariant::Danger => "btn bg-red-600 text-white hover:bg-red-700 border border-red-600",
            ButtonVariant::Ghost => "btn bg-transparent text-gray-700 hover:bg-gray-100 border border-gray-300",
        }
    }
}

impl ButtonSize {
    fn to_class(&self) -> &'static str {
        match self {
            ButtonSize::Small => "btn-sm",
            ButtonSize::Medium => "",
            ButtonSize::Large => "btn-lg",
        }
    }
}

/// Reusable button component with OpenWrt styling
#[component]
pub fn Button(
    /// Button text content
    children: Children,
    /// Button variant style
    #[prop(default = ButtonVariant::Primary)]
    variant: ButtonVariant,
    /// Button size
    #[prop(default = ButtonSize::Medium)]
    size: ButtonSize,
    /// Whether the button is disabled
    #[prop(default = false)]
    disabled: bool,
    /// Whether the button is in loading state
    #[prop(default = false)]
    loading: bool,
    /// Click handler
    #[prop(optional)]
    on_click: Option<Box<dyn Fn() + 'static>>,
    /// Button type (button, submit, reset)
    #[prop(default = "button".to_string())]
    button_type: String,
    /// Additional CSS classes
    #[prop(optional)]
    class: Option<String>,
) -> impl IntoView {
    let button_classes = format!(
        "{} {} {}",
        variant.to_class(),
        size.to_class(),
        class.unwrap_or_default()
    );

    view! {
        <button
            type={button_type}
            class={button_classes}
            disabled={disabled || loading}
            on:click=move |_| {
                if let Some(handler) = &on_click {
                    handler();
                }
            }
        >
            {loading.then(|| view! {
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-current" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            })}
            {children()}
        </button>
    }
}

/// Icon button component for actions with icons
#[component]
pub fn IconButton(
    /// Icon SVG content
    icon: View,
    /// Button variant style
    #[prop(default = ButtonVariant::Ghost)]
    variant: ButtonVariant,
    /// Button size
    #[prop(default = ButtonSize::Medium)]
    size: ButtonSize,
    /// Whether the button is disabled
    #[prop(default = false)]
    disabled: bool,
    /// Click handler
    #[prop(optional)]
    on_click: Option<Box<dyn Fn() + 'static>>,
    /// Tooltip text
    #[prop(optional)]
    tooltip: Option<String>,
    /// Additional CSS classes
    #[prop(optional)]
    class: Option<String>,
) -> impl IntoView {
    let button_classes = format!(
        "{} {} p-2 {}",
        variant.to_class(),
        size.to_class(),
        class.unwrap_or_default()
    );

    view! {
        <button
            type="button"
            class={button_classes}
            disabled={disabled}
            title={tooltip.unwrap_or_default()}
            on:click=move |_| {
                if let Some(handler) = &on_click {
                    handler();
                }
            }
        >
            {icon}
        </button>
    }
}

/// Navigation link component with active state handling
#[component]
pub fn NavLink(
    /// Link destination
    href: String,
    /// Link text content
    children: Children,
    /// Whether this link is currently active
    #[prop(default = false)]
    active: bool,
    /// Additional CSS classes
    #[prop(optional)]
    class: Option<String>,
) -> impl IntoView {
    let link_classes = if active {
        format!("nav-link-active {}", class.unwrap_or_default())
    } else {
        format!("nav-link-inactive {}", class.unwrap_or_default())
    };

    view! {
        <A href={href} class={link_classes}>
            {children()}
        </A>
    }
}

/// Navigation item configuration for hierarchical navigation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NavItem {
    pub id: String,
    pub label: String,
    pub path: String,
    pub icon: Option<String>,
    pub description: Option<String>,
    pub children: Vec<NavItem>,
    pub permissions: Vec<String>,
    pub badge: Option<String>,
    pub external: bool,
    pub disabled: bool,
}

impl NavItem {
    pub fn new(id: &str, label: &str, path: &str) -> Self {
        Self {
            id: id.to_string(),
            label: label.to_string(),
            path: path.to_string(),
            icon: None,
            description: None,
            children: Vec::new(),
            permissions: Vec::new(),
            badge: None,
            external: false,
            disabled: false,
        }
    }

    pub fn with_icon(mut self, icon: &str) -> Self {
        self.icon = Some(icon.to_string());
        self
    }

    pub fn with_description(mut self, description: &str) -> Self {
        self.description = Some(description.to_string());
        self
    }

    pub fn with_children(mut self, children: Vec<NavItem>) -> Self {
        self.children = children;
        self
    }

    pub fn with_permissions(mut self, permissions: Vec<String>) -> Self {
        self.permissions = permissions;
        self
    }

    pub fn with_badge(mut self, badge: &str) -> Self {
        self.badge = Some(badge.to_string());
        self
    }

    pub fn external(mut self) -> Self {
        self.external = true;
        self
    }

    pub fn disabled(mut self) -> Self {
        self.disabled = true;
        self
    }
}

/// Breadcrumb item
#[derive(Debug, Clone)]
pub struct BreadcrumbItem {
    pub label: String,
    pub path: String,
    pub active: bool,
    pub icon: Option<String>,
    pub description: Option<String>,
    pub quick_actions: Vec<QuickAction>,
}

/// Quick action for breadcrumb items
#[derive(Debug, Clone)]
pub struct QuickAction {
    pub label: String,
    pub path: String,
    pub icon: Option<String>,
    pub description: Option<String>,
}

/// Navigation state
#[derive(Debug, Clone)]
pub struct NavigationState {
    pub current_path: String,
    pub breadcrumbs: Vec<BreadcrumbItem>,
    pub expanded_items: Vec<String>,
    pub user_permissions: Vec<String>,
}

impl NavigationState {
    pub fn new() -> Self {
        Self {
            current_path: String::new(),
            breadcrumbs: Vec::new(),
            expanded_items: Vec::new(),
            user_permissions: Vec::new(),
        }
    }
}

/// Hierarchical navigation component
#[component]
pub fn HierarchicalNavigation(
    nav_items: ReadSignal<Vec<NavItem>>,
    #[prop(optional)] class: Option<String>,
    #[prop(optional)] on_navigate: Option<Box<dyn Fn(String)>>,
) -> impl IntoView {
    let (nav_state, set_nav_state) = create_signal(NavigationState::new());
    let location = use_location();

    // Update navigation state when location changes
    create_effect(move |_| {
        let current_path = location.pathname.get();
        set_nav_state.update(|state| {
            state.current_path = current_path.clone();
            state.breadcrumbs = generate_breadcrumbs(&current_path, &nav_items.get());
        });
    });

    let toggle_item = move |item_id: String| {
        set_nav_state.update(|state| {
            if state.expanded_items.contains(&item_id) {
                state.expanded_items.retain(|id| id != &item_id);
            } else {
                state.expanded_items.push(item_id);
            }
        });
    };

    view! {
        <nav class=format!("hierarchical-navigation {}", class.unwrap_or_default())>
            <div class="bg-white shadow-sm border-r border-gray-200 h-full">
                // Navigation header
                <div class="p-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Navigation</h2>
                </div>

                // Navigation items
                <div class="p-2 overflow-y-auto">
                    <NavItemList
                        items=nav_items
                        nav_state=nav_state
                        on_toggle=toggle_item
                        on_navigate=on_navigate.clone()
                        level=0
                    />
                </div>
            </div>
        </nav>
    }
}

/// Navigation item list component
#[component]
fn NavItemList(
    items: ReadSignal<Vec<NavItem>>,
    nav_state: ReadSignal<NavigationState>,
    on_toggle: impl Fn(String) + 'static + Copy,
    #[prop(optional)] on_navigate: Option<Box<dyn Fn(String)>>,
    level: u32,
) -> impl IntoView {
    view! {
        <ul class="space-y-1">
            {move || items.get().into_iter().map(|item| {
                let item_clone = item.clone();
                let item_id = item.id.clone();
                let has_children = !item.children.is_empty();
                let is_expanded = create_memo(move |_| {
                    nav_state.get().expanded_items.contains(&item_id)
                });
                let is_active = create_memo(move |_| {
                    nav_state.get().current_path == item.path ||
                    nav_state.get().current_path.starts_with(&format!("{}/", item.path))
                });

                // Check permissions
                let has_permission = create_memo(move |_| {
                    if item.permissions.is_empty() {
                        true
                    } else {
                        let user_perms = &nav_state.get().user_permissions;
                        item.permissions.iter().any(|perm| user_perms.contains(perm))
                    }
                });

                view! {
                    <li class="nav-item" style:display=move || if has_permission.get() { "block" } else { "none" }>
                        <div class=move || format!("nav-item-content flex items-center {}",
                            if level > 0 { format!("ml-{}", level * 4) } else { String::new() }
                        )>
                            // Expand/collapse button for items with children
                            {if has_children {
                                view! {
                                    <button
                                        class="flex-shrink-0 w-6 h-6 flex items-center justify-center text-gray-400 hover:text-gray-600"
                                        on:click=move |_| on_toggle(item_id.clone())
                                    >
                                        <svg class=move || format!("w-4 h-4 transform transition-transform {}",
                                            if is_expanded.get() { "rotate-90" } else { "" }
                                        ) fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                                        </svg>
                                    </button>
                                }.into_view()
                            } else {
                                view! {
                                    <div class="w-6 h-6"></div>
                                }.into_view()
                            }}

                            // Navigation link
                            <A
                                href=item.path.clone()
                                class=move || format!("flex-1 flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors {}",
                                    if is_active.get() {
                                        "bg-blue-100 text-blue-700"
                                    } else if item.disabled {
                                        "text-gray-400 cursor-not-allowed"
                                    } else {
                                        "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                                    }
                                )
                                on:click=move |_| {
                                    if let Some(ref handler) = on_navigate {
                                        handler(item.path.clone());
                                    }
                                }
                            >
                                // Icon
                                {item.icon.clone().map(|icon| {
                                    view! {
                                        <span class="mr-3 text-lg">{icon}</span>
                                    }.into_view()
                                })}

                                // Label
                                <span class="flex-1">{item.label.clone()}</span>

                                // Badge
                                {item.badge.clone().map(|badge| {
                                    view! {
                                        <span class="ml-2 px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
                                            {badge}
                                        </span>
                                    }.into_view()
                                })}
                            </A>
                        </div>

                        // Children (if expanded)
                        {if has_children && is_expanded.get() {
                            let children_signal = create_signal(item_clone.children.clone()).0;
                            view! {
                                <div class="mt-1">
                                    <NavItemList
                                        items=children_signal
                                        nav_state=nav_state
                                        on_toggle=on_toggle
                                        on_navigate=on_navigate.clone()
                                        level=level + 1
                                    />
                                </div>
                            }.into_view()
                        } else {
                            view! { <div></div> }.into_view()
                        }}
                    </li>
                }
            }).collect::<Vec<_>>()}
        </ul>
    }
}

/// Enhanced breadcrumb navigation component with context awareness
#[component]
pub fn BreadcrumbNavigation(
    breadcrumbs: ReadSignal<Vec<BreadcrumbItem>>,
    #[prop(optional)] class: Option<String>,
    #[prop(optional)] show_quick_actions: Option<bool>,
    #[prop(optional)] compact_mode: Option<bool>,
) -> impl IntoView {
    let show_actions = show_quick_actions.unwrap_or(true);
    let compact = compact_mode.unwrap_or(false);
    let (show_dropdown, set_show_dropdown) = create_signal(None::<usize>);

    view! {
        <nav class=format!("breadcrumb-navigation {}", class.unwrap_or_default())>
            <div class="flex items-center justify-between">
                <ol class="flex items-center space-x-2 text-sm text-gray-500">
                    {move || breadcrumbs.get().into_iter().enumerate().map(|(i, item)| {
                        let is_last = i == breadcrumbs.get().len() - 1;
                        let has_quick_actions = !item.quick_actions.is_empty();
                        let item_clone = item.clone();

                        view! {
                            <li class="flex items-center relative">
                                {if i > 0 {
                                    view! {
                                        <svg class="w-4 h-4 mx-2 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                                        </svg>
                                    }.into_view()
                                } else {
                                    view! { <div></div> }.into_view()
                                }}

                                <div class="flex items-center">
                                    // Icon (if available)
                                    {item.icon.clone().map(|icon| {
                                        view! {
                                            <span class="mr-1 text-base">{icon}</span>
                                        }.into_view()
                                    })}

                                    // Breadcrumb link/text
                                    {if is_last || item.active {
                                        view! {
                                            <span class="font-medium text-gray-900 flex items-center">
                                                {item.label.clone()}
                                                {if has_quick_actions && show_actions {
                                                    view! {
                                                        <button
                                                            class="ml-1 p-1 rounded hover:bg-gray-100"
                                                            on:click=move |_| {
                                                                if show_dropdown.get() == Some(i) {
                                                                    set_show_dropdown.set(None);
                                                                } else {
                                                                    set_show_dropdown.set(Some(i));
                                                                }
                                                            }
                                                        >
                                                            <svg class="w-3 h-3 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                                            </svg>
                                                        </button>
                                                    }.into_view()
                                                } else {
                                                    view! { <div></div> }.into_view()
                                                }}
                                            </span>
                                        }.into_view()
                                    } else {
                                        view! {
                                            <A href=item.path.clone() class="hover:text-gray-700 transition-colors flex items-center">
                                                {item.label.clone()}
                                                {if has_quick_actions && show_actions {
                                                    view! {
                                                        <button
                                                            class="ml-1 p-1 rounded hover:bg-gray-100"
                                                            on:click=move |e| {
                                                                e.prevent_default();
                                                                if show_dropdown.get() == Some(i) {
                                                                    set_show_dropdown.set(None);
                                                                } else {
                                                                    set_show_dropdown.set(Some(i));
                                                                }
                                                            }
                                                        >
                                                            <svg class="w-3 h-3 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                                            </svg>
                                                        </button>
                                                    }.into_view()
                                                } else {
                                                    view! { <div></div> }.into_view()
                                                }}
                                            </A>
                                        }.into_view()
                                    }}
                                </div>

                                // Quick actions dropdown
                                {if has_quick_actions && show_actions && show_dropdown.get() == Some(i) {
                                    view! {
                                        <div class="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 min-w-48">
                                            <div class="py-1">
                                                {item_clone.quick_actions.into_iter().map(|action| {
                                                    view! {
                                                        <A
                                                            href=action.path.clone()
                                                            class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                                            on:click=move |_| set_show_dropdown.set(None)
                                                        >
                                                            {action.icon.clone().map(|icon| {
                                                                view! {
                                                                    <span class="mr-2 text-base">{icon}</span>
                                                                }.into_view()
                                                            })}
                                                            <div>
                                                                <div class="font-medium">{action.label.clone()}</div>
                                                                {action.description.clone().map(|desc| {
                                                                    view! {
                                                                        <div class="text-xs text-gray-500">{desc}</div>
                                                                    }.into_view()
                                                                })}
                                                            </div>
                                                        </A>
                                                    }
                                                }).collect::<Vec<_>>()}
                                            </div>
                                        </div>
                                    }.into_view()
                                } else {
                                    view! { <div></div> }.into_view()
                                }}
                            </li>
                        }
                    }).collect::<Vec<_>>()}
                </ol>

                // Compact mode toggle or additional actions
                {if !compact {
                    view! {
                        <div class="flex items-center space-x-2">
                            <QuickNavigationMenu />
                        </div>
                    }.into_view()
                } else {
                    view! { <div></div> }.into_view()
                }}
            </div>
        </nav>
    }
}

/// Quick navigation menu component
#[component]
pub fn QuickNavigationMenu() -> impl IntoView {
    let (show_menu, set_show_menu) = create_signal(false);

    view! {
        <div class="relative">
            <button
                class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                on:click=move |_| set_show_menu.update(|show| *show = !*show)
                title="Quick Navigation"
            >
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                </svg>
            </button>

            {move || if show_menu.get() {
                view! {
                    <div class="absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 min-w-48">
                        <div class="py-1">
                            <A
                                href="/"
                                class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                on:click=move |_| set_show_menu.set(false)
                            >
                                <span class="mr-2">🏠</span>
                                <span>Home</span>
                            </A>
                            <A
                                href="/status"
                                class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                on:click=move |_| set_show_menu.set(false)
                            >
                                <span class="mr-2">📊</span>
                                <span>Status</span>
                            </A>
                            <A
                                href="/network"
                                class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                on:click=move |_| set_show_menu.set(false)
                            >
                                <span class="mr-2">🌐</span>
                                <span>Network</span>
                            </A>
                            <A
                                href="/system"
                                class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                on:click=move |_| set_show_menu.set(false)
                            >
                                <span class="mr-2">⚙️</span>
                                <span>System</span>
                            </A>
                            <div class="border-t border-gray-100 my-1"></div>
                            <A
                                href="/help"
                                class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                on:click=move |_| set_show_menu.set(false)
                            >
                                <span class="mr-2">❓</span>
                                <span>Help</span>
                            </A>
                        </div>
                    </div>
                }.into_view()
            } else {
                view! { <div></div> }.into_view()
            }}
        </div>
    }
}

/// Context-aware breadcrumb navigation
#[component]
pub fn ContextAwareBreadcrumbs(
    nav_items: ReadSignal<Vec<NavItem>>,
    #[prop(optional)] class: Option<String>,
) -> impl IntoView {
    let location = use_location();
    let breadcrumbs = create_memo(move |_| {
        let current_path = location.pathname.get();
        generate_enhanced_breadcrumbs(&current_path, &nav_items.get())
    });

    view! {
        <BreadcrumbNavigation
            breadcrumbs=breadcrumbs
            class=class
            show_quick_actions=Some(true)
        />
    }
}

/// Generate breadcrumbs from current path and navigation items
fn generate_breadcrumbs(current_path: &str, nav_items: &[NavItem]) -> Vec<BreadcrumbItem> {
    let mut breadcrumbs = Vec::new();
    let path_segments: Vec<&str> = current_path.trim_start_matches('/').split('/').collect();

    // Always add home
    breadcrumbs.push(BreadcrumbItem {
        label: "Home".to_string(),
        path: "/".to_string(),
        active: current_path == "/",
        icon: Some("🏠".to_string()),
        description: Some("OpenWrt Administration".to_string()),
        quick_actions: vec![
            QuickAction {
                label: "Status Overview".to_string(),
                path: "/status".to_string(),
                icon: Some("📊".to_string()),
                description: Some("System status and monitoring".to_string()),
            },
            QuickAction {
                label: "Network Settings".to_string(),
                path: "/network".to_string(),
                icon: Some("🌐".to_string()),
                description: Some("Network configuration".to_string()),
            },
        ],
    });

    // Find matching navigation items for path segments
    let mut current_items = nav_items;
    let mut current_path_build = String::new();

    for segment in path_segments {
        if segment.is_empty() {
            continue;
        }

        current_path_build.push('/');
        current_path_build.push_str(segment);

        if let Some(item) = find_nav_item_by_path(&current_path_build, current_items) {
            breadcrumbs.push(BreadcrumbItem {
                label: item.label.clone(),
                path: item.path.clone(),
                active: item.path == current_path,
                icon: item.icon.clone(),
                description: item.description.clone(),
                quick_actions: generate_quick_actions_for_item(item),
            });
            current_items = &item.children;
        }
    }

    breadcrumbs
}

/// Generate enhanced breadcrumbs with context awareness
fn generate_enhanced_breadcrumbs(current_path: &str, nav_items: &[NavItem]) -> Vec<BreadcrumbItem> {
    let mut breadcrumbs = generate_breadcrumbs(current_path, nav_items);

    // Add context-specific quick actions based on current path
    if let Some(last_crumb) = breadcrumbs.last_mut() {
        match current_path {
            path if path.starts_with("/network") => {
                last_crumb.quick_actions.extend(vec![
                    QuickAction {
                        label: "Interface Status".to_string(),
                        path: "/network/interfaces".to_string(),
                        icon: Some("🔌".to_string()),
                        description: Some("View network interfaces".to_string()),
                    },
                    QuickAction {
                        label: "Wireless Settings".to_string(),
                        path: "/network/wireless".to_string(),
                        icon: Some("📶".to_string()),
                        description: Some("Configure wireless networks".to_string()),
                    },
                ]);
            }
            path if path.starts_with("/system") => {
                last_crumb.quick_actions.extend(vec![
                    QuickAction {
                        label: "System Log".to_string(),
                        path: "/system/log".to_string(),
                        icon: Some("📋".to_string()),
                        description: Some("View system logs".to_string()),
                    },
                    QuickAction {
                        label: "Reboot System".to_string(),
                        path: "/system/reboot".to_string(),
                        icon: Some("🔄".to_string()),
                        description: Some("Restart the system".to_string()),
                    },
                ]);
            }
            _ => {}
        }
    }

    breadcrumbs
}

/// Generate quick actions for a navigation item
fn generate_quick_actions_for_item(item: &NavItem) -> Vec<QuickAction> {
    let mut actions = Vec::new();

    // Add child items as quick actions (up to 5)
    for child in item.children.iter().take(5) {
        actions.push(QuickAction {
            label: child.label.clone(),
            path: child.path.clone(),
            icon: child.icon.clone(),
            description: child.description.clone(),
        });
    }

    // Add context-specific actions based on item path
    match item.path.as_str() {
        "/network" => {
            actions.push(QuickAction {
                label: "Network Diagnostics".to_string(),
                path: "/network/diagnostics".to_string(),
                icon: Some("🔍".to_string()),
                description: Some("Run network diagnostics".to_string()),
            });
        }
        "/system" => {
            actions.push(QuickAction {
                label: "Backup Settings".to_string(),
                path: "/system/backup".to_string(),
                icon: Some("💾".to_string()),
                description: Some("Backup system configuration".to_string()),
            });
        }
        _ => {}
    }

    actions
}

/// Find navigation item by path
fn find_nav_item_by_path(path: &str, items: &[NavItem]) -> Option<&NavItem> {
    for item in items {
        if item.path == path {
            return Some(item);
        }
        if let Some(child) = find_nav_item_by_path(path, &item.children) {
            return Some(child);
        }
    }
    None
}
