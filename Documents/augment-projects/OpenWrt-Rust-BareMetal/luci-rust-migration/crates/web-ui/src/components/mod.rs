//! UI Components for LuCI Web Interface
//!
//! This module contains reusable UI components built with Leptos
//! following OpenWrt design patterns and optimized for embedded systems.

use leptos::*;
use leptos_meta::*;
use leptos_router::*;

pub mod header;
pub mod footer;
pub mod navigation;
pub mod forms;
pub mod advanced_forms;
pub mod tables;
pub mod charts;
pub mod dynamic_menu;
pub mod permission_menu;
pub mod network_services;
pub mod security_apps;
pub mod dns_apps;
pub mod vpn_apps;
pub mod file_sharing_apps;
pub mod system_monitoring_apps;
pub mod modals;
pub mod status;
pub mod buttons;
pub mod cards;
pub mod alerts;
pub mod loading;
pub mod package_manager;
pub mod file_browser;
pub mod terminal;

// Re-export commonly used components
pub use header::*;
pub use footer::*;
pub use navigation::*;
pub use forms::*;
pub use advanced_forms::*;
pub use tables::*;
pub use charts::*;
pub use dynamic_menu::*;
pub use permission_menu::*;
pub use network_services::*;
pub use security_apps::*;
pub use dns_apps::*;
pub use vpn_apps::*;
pub use file_sharing_apps::*;
pub use system_monitoring_apps::*;
pub use modals::*;
pub use status::*;
pub use buttons::*;
pub use cards::*;
pub use alerts::*;
pub use loading::*;
pub use package_manager::*;
pub use file_browser::*;
pub use terminal::*;