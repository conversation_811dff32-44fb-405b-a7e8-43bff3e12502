//! UI Components for LuCI Web Interface
//!
//! This module contains reusable UI components built with Leptos
//! following OpenWrt design patterns and optimized for embedded systems.

use leptos::*;
use leptos_meta::*;
use leptos_router::*;

pub mod header;
pub mod footer;
pub mod navigation;
pub mod forms;
pub mod tables;
pub mod modals;
pub mod status;
pub mod charts;
pub mod buttons;
pub mod cards;
pub mod alerts;
pub mod loading;
pub mod package_manager;
pub mod file_browser;

// Re-export commonly used components
pub use header::*;
pub use footer::*;
pub use navigation::*;
pub use forms::*;
pub use tables::*;
pub use modals::*;
pub use status::*;
pub use charts::*;
pub use buttons::*;
pub use cards::*;
pub use alerts::*;
pub use loading::*;
pub use package_manager::*;
pub use file_browser::*;