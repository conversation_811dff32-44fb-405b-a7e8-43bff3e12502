//! Modal components for OpenWrt LuCI interface
//!
//! This module provides reusable modal dialog components for user interactions
//! following OpenWrt design patterns.

use leptos::*;
use std::rc::Rc;
use wasm_bindgen::prelude::*;
use wasm_bindgen::JsCast;
use web_sys;

#[wasm_bindgen]
extern "C" {
    #[wasm_bindgen(js_name = setTimeout)]
    fn set_timeout_with_callback_and_timeout_and_arguments_0(
        handler: &::js_sys::Function,
        timeout: i32,
    ) -> i32;
}

fn set_timeout<F>(f: F, duration: std::time::Duration)
where
    F: FnOnce() + 'static,
{
    let closure = Closure::once_into_js(f);
    set_timeout_with_callback_and_timeout_and_arguments_0(
        closure.as_ref().unchecked_ref(),
        duration.as_millis() as i32,
    );
}

/// Modal size variants
#[derive(Clone, PartialEq)]
pub enum ModalSize {
    Small,
    Medium,
    Large,
    ExtraLarge,
}

impl ModalSize {
    fn to_class(&self) -> &'static str {
        match self {
            ModalSize::Small => "max-w-md",
            ModalSize::Medium => "max-w-lg",
            ModalSize::Large => "max-w-2xl",
            ModalSize::ExtraLarge => "max-w-4xl",
        }
    }
}

/// Enhanced modal component with advanced features
#[component]
pub fn Modal(
    /// Whether the modal is open
    open: ReadSignal<bool>,
    /// Signal to close the modal
    on_close: WriteSignal<bool>,
    /// Modal title
    title: String,
    /// Modal content
    children: Children,
    /// Modal size
    #[prop(default = ModalSize::Medium)]
    size: ModalSize,
    /// Whether clicking outside closes the modal
    #[prop(default = true)]
    close_on_backdrop: bool,
    /// Whether the modal can be closed
    #[prop(default = true)]
    closable: bool,
    /// Modal animation type
    #[prop(default = ModalAnimation::Fade)]
    animation: ModalAnimation,
    /// Modal position
    #[prop(default = ModalPosition::Center)]
    position: ModalPosition,
    /// Whether to show modal header
    #[prop(default = true)]
    show_header: bool,
    /// Custom header content
    #[prop(optional)]
    header_content: Option<Children>,
    /// Custom footer content
    #[prop(optional)]
    footer_content: Option<Box<dyn Fn() -> View>>,
    /// Whether modal is loading
    #[prop(default = false)]
    loading: bool,
    /// Loading message
    #[prop(default = "Loading...".to_string())]
    loading_message: String,
    /// Z-index for modal stacking
    #[prop(default = 50)]
    z_index: u32,
) -> impl IntoView {
    // Handle backdrop click
    let handle_backdrop_click = move |ev: web_sys::MouseEvent| {
        if close_on_backdrop && closable && !loading {
            // Check if click was on backdrop, not modal content
            if let Some(target) = ev.target() {
                if let Some(element) = target.dyn_ref::<web_sys::HtmlElement>() {
                    if element.class_list().contains("modal-overlay") {
                        on_close.set(false);
                    }
                }
            }
        }
    };

    // Handle escape key
    let handle_keydown = move |ev: web_sys::KeyboardEvent| {
        if closable && !loading && ev.key() == "Escape" {
            on_close.set(false);
        }
    };

    // Modal classes
    let overlay_class = move || {
        format!(
            "fixed inset-0 bg-black bg-opacity-50 flex {} {} modal-overlay {}",
            position.to_class(),
            animation.to_class(),
            if open.get() { "opacity-100" } else { "opacity-0 pointer-events-none" }
        )
    };

    let modal_class = format!(
        "bg-white rounded-lg shadow-xl transform transition-all duration-300 {} {}",
        size.to_class(),
        if loading { "pointer-events-none" } else { "" }
    );

    view! {
        <div
            class=overlay_class
            style=format!("z-index: {}", z_index)
            on:click=handle_backdrop_click
            on:keydown=handle_keydown
            tabindex="-1"
        >
            <div class=modal_class on:click=|ev| ev.stop_propagation()>
                {if show_header {
                    view! {
                        <div class="flex items-center justify-between p-4 border-b border-gray-200">
                            {if let Some(header) = header_content {
                                header().into_view()
                            } else {
                                view! {
                                    <h3 class="text-lg font-medium text-gray-900">{title.clone()}</h3>
                                }.into_view()
                            }}
                            {if closable && !loading {
                                view! {
                                    <button
                                        type="button"
                                        class="text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600 transition-colors"
                                        on:click=move |_| on_close.set(false)
                                    >
                                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                        </svg>
                                    </button>
                                }.into_view()
                            } else {
                                view! { <div></div> }.into_view()
                            }}
                        </div>
                    }.into_view()
                } else {
                    view! { <div></div> }.into_view()
                }}

                <div class="relative p-4">
                    {if loading {
                        view! {
                            <div class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
                                <div class="flex items-center space-x-3">
                                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                                    <span class="text-sm text-gray-600">{loading_message.clone()}</span>
                                </div>
                            </div>
                        }.into_view()
                    } else {
                        view! { <div></div> }.into_view()
                    }}
                    {children()}
                </div>

                {if let Some(footer) = &footer_content {
                    view! {
                        <div class="border-t border-gray-200 p-4">
                            {footer()}
                        </div>
                    }.into_view()
                } else {
                    view! { <div></div> }.into_view()
                }}
            </div>
        </div>
    }
}

/// Form modal with validation support
#[component]
pub fn FormModal(
    /// Whether the modal is open
    open: ReadSignal<bool>,
    /// Signal to close the modal
    on_close: WriteSignal<bool>,
    /// Modal title
    title: String,
    /// Form content
    children: Children,
    /// Form submission handler
    on_submit: Rc<dyn Fn() -> ValidationResult>,
    /// Submit button text
    #[prop(default = "Submit".to_string())]
    submit_text: String,
    /// Cancel button text
    #[prop(default = "Cancel".to_string())]
    cancel_text: String,
    /// Modal size
    #[prop(default = ModalSize::Medium)]
    size: ModalSize,
    /// Whether form is submitting
    #[prop(optional)]
    submitting: Option<ReadSignal<bool>>,
    /// Validation errors
    #[prop(optional)]
    validation_errors: Option<ReadSignal<Vec<ValidationError>>>,
    /// Whether to show form buttons
    #[prop(default = true)]
    show_buttons: bool,
    /// Custom button content
    #[prop(optional)]
    button_content: Option<Box<dyn Fn() -> Fragment>>,
) -> impl IntoView {
    let (is_submitting, set_is_submitting) = create_signal(false);
    let (errors, set_errors) = create_signal(Vec::<ValidationError>::new());

    let actual_submitting = submitting.unwrap_or(is_submitting);
    let actual_errors = validation_errors.unwrap_or(errors);

    let handle_submit = {
        let on_submit = on_submit.clone();
        move |ev: web_sys::SubmitEvent| {
            ev.prevent_default();

            if actual_submitting.get() {
                return;
            }

            set_is_submitting.set(true);
            set_errors.set(Vec::new());

            match on_submit() {
                Ok(()) => {
                    on_close.set(false);
                    set_is_submitting.set(false);
                },
                Err(validation_errors) => {
                    set_errors.set(validation_errors);
                    set_is_submitting.set(false);
                }
            }
        }
    };

    let handle_cancel = move |_| {
        if !actual_submitting.get() {
            on_close.set(false);
        }
    };

    let footer_content = move || {
        if show_buttons {
            if let Some(custom_buttons) = &button_content {
                custom_buttons().into_view()
            } else {
                view! {
                    <div class="flex justify-end space-x-3">
                        <button
                            type="button"
                            class="btn-secondary"
                            disabled=actual_submitting.get()
                            on:click=handle_cancel
                        >
                            {cancel_text.clone()}
                        </button>
                        <button
                            type="submit"
                            class="btn-primary"
                            disabled=actual_submitting.get()
                        >
                            {if actual_submitting.get() {
                                view! {
                                    <div class="flex items-center space-x-2">
                                        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                        <span>"Submitting..."</span>
                                    </div>
                                }.into_view()
                            } else {
                                view! { <span>{submit_text.clone()}</span> }.into_view()
                            }}
                        </button>
                    </div>
                }.into_view()
            }
        } else {
            view! { <div></div> }.into_view()
        }
    };

    view! {
        <Modal
            open=open
            on_close=on_close
            title=title
            size=size
            closable=!actual_submitting.get()
            close_on_backdrop=!actual_submitting.get()
            loading=actual_submitting.get()
            loading_message="Processing...".to_string()
            footer_content=Box::new(footer_content)
        >
            <form on:submit=handle_submit>
                {if !actual_errors.get().is_empty() {
                    view! {
                        <div class="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                            <div class="flex">
                                <svg class="w-5 h-5 text-red-400 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <div>
                                    <h3 class="text-sm font-medium text-red-800 mb-1">"Validation Errors"</h3>
                                    <ul class="text-sm text-red-700 space-y-1">
                                        {actual_errors.get().into_iter().map(|error| {
                                            view! {
                                                <li>
                                                    <strong>{error.field}": "</strong>
                                                    {error.message}
                                                </li>
                                            }
                                        }).collect::<Vec<_>>()}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    }.into_view()
                } else {
                    view! { <div></div> }.into_view()
                }}
                {children()}
            </form>
        </Modal>
    }
}

/// Progress modal for long-running operations
#[component]
pub fn ProgressModal(
    /// Whether the modal is open
    open: ReadSignal<bool>,
    /// Signal to close the modal
    on_close: WriteSignal<bool>,
    /// Modal title
    title: String,
    /// Progress state
    progress: ReadSignal<ProgressState>,
    /// Whether operation can be cancelled
    #[prop(default = false)]
    cancellable: bool,
    /// Cancel handler
    #[prop(optional)]
    on_cancel: Option<Rc<dyn Fn()>>,
    /// Auto-close on success
    #[prop(default = true)]
    auto_close_on_success: bool,
    /// Auto-close delay in milliseconds
    #[prop(default = 2000)]
    auto_close_delay: u32,
) -> impl IntoView {
    // Auto-close effect
    create_effect(move |_| {
        if auto_close_on_success {
            if let ProgressState::Success { .. } = progress.get() {
                let on_close = on_close.clone();
                set_timeout(
                    move || on_close.set(false),
                    std::time::Duration::from_millis(auto_close_delay as u64),
                );
            }
        }
    });

    let handle_cancel = {
        let on_cancel = on_cancel.clone();
        let on_close = on_close.clone();
        move |_| {
            if let Some(cancel_handler) = &on_cancel {
                cancel_handler();
            }
            on_close.set(false);
        }
    };

    let can_close = move || {
        match progress.get() {
            ProgressState::InProgress { .. } => cancellable,
            _ => true,
        }
    };

    view! {
        <Modal
            open=open
            on_close=on_close
            title=title
            size=ModalSize::Medium
            closable=can_close()
            close_on_backdrop=can_close()
        >
            <div class="space-y-4">
                {move || match progress.get() {
                    ProgressState::Idle => view! {
                        <div class="text-center py-8">
                            <div class="text-gray-500">"Ready to start..."</div>
                        </div>
                    }.into_view(),

                    ProgressState::InProgress { message, percentage } => view! {
                        <div class="space-y-4">
                            <div class="flex items-center space-x-3">
                                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                                <span class="text-sm text-gray-700">{message}</span>
                            </div>

                            {if let Some(pct) = percentage {
                                view! {
                                    <div class="space-y-2">
                                        <div class="flex justify-between text-sm">
                                            <span class="text-gray-600">"Progress"</span>
                                            <span class="text-gray-900">{format!("{:.1}%", pct)}</span>
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-2">
                                            <div
                                                class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                                style=format!("width: {}%", pct)
                                            ></div>
                                        </div>
                                    </div>
                                }.into_view()
                            } else {
                                view! {
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-blue-600 h-2 rounded-full animate-pulse"></div>
                                    </div>
                                }.into_view()
                            }}

                            {if cancellable {
                                let handle_cancel_clone = handle_cancel.clone();
                                view! {
                                    <div class="flex justify-end">
                                        <button
                                            type="button"
                                            class="btn-secondary"
                                            on:click=handle_cancel_clone
                                        >
                                            "Cancel"
                                        </button>
                                    </div>
                                }.into_view()
                            } else {
                                view! { <div></div> }.into_view()
                            }}
                        </div>
                    }.into_view(),

                    ProgressState::Success { message } => view! {
                        <div class="text-center py-8 space-y-4">
                            <div class="flex justify-center">
                                <svg class="w-12 h-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-medium text-green-800 mb-2">"Success!"</h3>
                                <p class="text-sm text-gray-600">{message}</p>
                            </div>
                            <button
                                type="button"
                                class="btn-primary"
                                on:click=move |_| on_close.set(false)
                            >
                                "Close"
                            </button>
                        </div>
                    }.into_view(),

                    ProgressState::Error { message } => view! {
                        <div class="text-center py-8 space-y-4">
                            <div class="flex justify-center">
                                <svg class="w-12 h-12 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-medium text-red-800 mb-2">"Error"</h3>
                                <p class="text-sm text-gray-600">{message}</p>
                            </div>
                            <div class="flex justify-center space-x-3">
                                {if let Some(_) = &on_cancel {
                                    let handle_cancel_clone = handle_cancel.clone();
                                    view! {
                                        <button
                                            type="button"
                                            class="btn-secondary"
                                            on:click=handle_cancel_clone
                                        >
                                            "Retry"
                                        </button>
                                    }.into_view()
                                } else {
                                    view! { <div></div> }.into_view()
                                }}
                                <button
                                    type="button"
                                    class="btn-primary"
                                    on:click=move |_| on_close.set(false)
                                >
                                    "Close"
                                </button>
                            </div>
                        </div>
                    }.into_view(),
                }}
            </div>
        </Modal>
    }
}

/// Step in a multi-step modal
#[derive(Clone)]
pub struct ModalStep {
    pub id: String,
    pub title: String,
    pub description: Option<String>,
    pub content: Rc<dyn Fn() -> View>,
    pub validator: Option<Rc<dyn Fn() -> ValidationResult>>,
    pub skippable: bool,
}

/// Multi-step modal for wizard-like interfaces
#[component]
pub fn MultiStepModal(
    /// Whether the modal is open
    open: ReadSignal<bool>,
    /// Signal to close the modal
    on_close: WriteSignal<bool>,
    /// Modal title
    title: String,
    /// Steps in the wizard
    steps: Vec<ModalStep>,
    /// Current step index
    #[prop(optional)]
    current_step: Option<ReadSignal<usize>>,
    /// Current step setter
    #[prop(optional)]
    set_current_step: Option<WriteSignal<usize>>,
    /// Completion handler
    on_complete: Rc<dyn Fn() -> ValidationResult>,
    /// Modal size
    #[prop(default = ModalSize::Large)]
    size: ModalSize,
    /// Whether to show step indicators
    #[prop(default = true)]
    show_step_indicators: bool,
    /// Whether steps can be navigated freely
    #[prop(default = false)]
    free_navigation: bool,
) -> impl IntoView {
    let (internal_step, set_internal_step) = create_signal(0usize);
    let (validation_errors, set_validation_errors) = create_signal(Vec::<ValidationError>::new());
    let (is_completing, set_is_completing) = create_signal(false);

    let current_step_signal = current_step.unwrap_or(internal_step);
    let set_current_step_signal = set_current_step.unwrap_or(set_internal_step);

    let steps_clone_for_current = steps.clone();
    let current_step_data = move || {
        let step_idx = current_step_signal.get();
        steps_clone_for_current.get(step_idx).cloned()
    };

    let is_first_step = move || current_step_signal.get() == 0;
    let steps_len = steps.len();
    let is_last_step = move || current_step_signal.get() >= steps_len.saturating_sub(1);

    let steps_clone_for_next = steps.clone();
    let handle_next = Rc::new(move |_: web_sys::MouseEvent| {
        set_validation_errors.set(Vec::new());

        let step_idx = current_step_signal.get();
        if let Some(step) = steps_clone_for_next.get(step_idx).cloned() {
            if let Some(validator) = &step.validator {
                match validator() {
                    Ok(()) => {
                        if !is_last_step() {
                            set_current_step_signal.update(|s| *s += 1);
                        }
                    },
                    Err(errors) => {
                        set_validation_errors.set(errors);
                    }
                }
            } else if !is_last_step() {
                set_current_step_signal.update(|s| *s += 1);
            }
        }
    });

    let handle_previous = Rc::new(move |_: web_sys::MouseEvent| {
        if !is_first_step() {
            set_current_step_signal.update(|s| *s = s.saturating_sub(1));
            set_validation_errors.set(Vec::new());
        }
    });

    let handle_complete = {
        let on_complete = on_complete.clone();
        move |_: web_sys::MouseEvent| {
            set_is_completing.set(true);
            set_validation_errors.set(Vec::new());

            match on_complete() {
                Ok(()) => {
                    on_close.set(false);
                    set_is_completing.set(false);
                    set_current_step_signal.set(0);
                },
                Err(errors) => {
                    set_validation_errors.set(errors);
                    set_is_completing.set(false);
                }
            }
        }
    };

    let handle_step_click = move |step_idx: usize| {
        if free_navigation || step_idx < current_step_signal.get() {
            set_current_step_signal.set(step_idx);
            set_validation_errors.set(Vec::new());
        }
    };

    let footer_content = {
        let on_complete = on_complete.clone();
        let set_is_completing = set_is_completing.clone();
        let set_validation_errors = set_validation_errors.clone();
        let on_close = on_close.clone();
        let set_current_step_signal = set_current_step_signal.clone();
        let current_step_signal = current_step_signal.clone();
        let is_completing = is_completing.clone();
        let steps_len = steps_len;
        let handle_next = handle_next.clone();
        let handle_previous = handle_previous.clone();
        move || {
            let is_first_step = move || current_step_signal.get() == 0;
            let is_last_step = move || current_step_signal.get() == steps_len - 1;

            let handle_previous = move |_| {
                if !is_first_step() {
                    set_current_step_signal.update(|s| *s = s.saturating_sub(1));
                    set_validation_errors.set(Vec::new());
                }
            };

            let handle_complete = {
                let on_complete = on_complete.clone();
                move |_| {
                    set_is_completing.set(true);
                    set_validation_errors.set(Vec::new());

                    match on_complete() {
                        Ok(()) => {
                            on_close.set(false);
                            set_is_completing.set(false);
                            set_current_step_signal.set(0);
                        },
                        Err(errors) => {
                            set_validation_errors.set(errors);
                            set_is_completing.set(false);
                        }
                    }
                }
            };

            view! {
                <div class="flex justify-between items-center">
                    <div class="flex space-x-2">
                        {if !is_first_step() {
                        view! {
                            <button
                                type="button"
                                class="btn-secondary"
                                disabled=is_completing.get()
                                on:click={
                                    let handle_previous = handle_previous.clone();
                                    move |e| handle_previous(e)
                                }
                            >
                                "Previous"
                            </button>
                        }.into_view()
                    } else {
                        view! { <div></div> }.into_view()
                    }}
                </div>

                <div class="flex space-x-2">
                    <button
                        type="button"
                        class="btn-secondary"
                        disabled=is_completing.get()
                        on:click=move |_| on_close.set(false)
                    >
                        "Cancel"
                    </button>

                    {if is_last_step() {
                        view! {
                            <button
                                type="button"
                                class="btn-primary"
                                disabled=is_completing.get()
                                on:click=handle_complete
                            >
                                {if is_completing.get() {
                                    view! {
                                        <div class="flex items-center space-x-2">
                                            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                            <span>"Completing..."</span>
                                        </div>
                                    }.into_view()
                                } else {
                                    view! { <span>"Complete"</span> }.into_view()
                                }}
                            </button>
                        }.into_view()
                    } else {
                        view! {
                            <button
                                type="button"
                                class="btn-primary"
                                disabled=is_completing.get()
                                on:click={
                                    let handle_next = handle_next.clone();
                                    move |e| handle_next(e)
                                }
                            >
                                "Next"
                            </button>
                        }.into_view()
                    }}
                </div>
            </div>
        }.into_view()
        }
    };

    view! {
        <Modal
            open=open
            on_close=on_close
            title=title
            size=size
            closable=!is_completing.get()
            close_on_backdrop=!is_completing.get()
            footer_content=Box::new(footer_content)
        >
            <div class="space-y-6">
                {if show_step_indicators {
                    view! {
                        <div class="flex items-center justify-between">
                            {steps.iter().enumerate().map(|(idx, step)| {
                                let is_current = idx == current_step_signal.get();
                                let is_completed = idx < current_step_signal.get();
                                let is_clickable = free_navigation || idx < current_step_signal.get();

                                view! {
                                    <div class="flex items-center">
                                        <div
                                            class={format!(
                                                "flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium {}",
                                                if is_current {
                                                    "bg-blue-600 text-white"
                                                } else if is_completed {
                                                    "bg-green-600 text-white"
                                                } else {
                                                    "bg-gray-200 text-gray-600"
                                                }
                                            )}
                                            class:cursor-pointer=is_clickable
                                            on:click=move |_| if is_clickable { handle_step_click(idx) }
                                        >
                                            {if is_completed {
                                                view! {
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                                    </svg>
                                                }.into_view()
                                            } else {
                                                view! { <span>{(idx + 1).to_string()}</span> }.into_view()
                                            }}
                                        </div>
                                        <div class="ml-2 text-sm">
                                            <div class={format!(
                                                "font-medium {}",
                                                if is_current { "text-blue-600" } else { "text-gray-900" }
                                            )}>
                                                {step.title.clone()}
                                            </div>
                                            {if let Some(desc) = &step.description {
                                                view! {
                                                    <div class="text-gray-500">{desc.clone()}</div>
                                                }.into_view()
                                            } else {
                                                view! { <div></div> }.into_view()
                                            }}
                                        </div>
                                        {if idx < steps.len() - 1 {
                                            view! {
                                                <div class="flex-1 h-px bg-gray-200 mx-4"></div>
                                            }.into_view()
                                        } else {
                                            view! { <div></div> }.into_view()
                                        }}
                                    </div>
                                }
                            }).collect::<Vec<_>>()}
                        </div>
                    }.into_view()
                } else {
                    view! { <div></div> }.into_view()
                }}

                {if !validation_errors.get().is_empty() {
                    view! {
                        <div class="p-3 bg-red-50 border border-red-200 rounded-md">
                            <div class="flex">
                                <svg class="w-5 h-5 text-red-400 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <div>
                                    <h3 class="text-sm font-medium text-red-800 mb-1">"Validation Errors"</h3>
                                    <ul class="text-sm text-red-700 space-y-1">
                                        {validation_errors.get().into_iter().map(|error| {
                                            view! {
                                                <li>
                                                    <strong>{error.field}": "</strong>
                                                    {error.message}
                                                </li>
                                            }
                                        }).collect::<Vec<_>>()}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    }.into_view()
                } else {
                    view! { <div></div> }.into_view()
                }}

                <div class="min-h-[200px]">
                    {move || {
                        if let Some(step) = current_step_data() {
                            (step.content)()
                        } else {
                            view! { <div class="text-center text-gray-500">"No step content"</div> }.into_view()
                        }
                    }}
                </div>
            </div>
        </Modal>
    }
}

/// Common validation functions
pub mod validators {
    use super::*;

    /// Validates that a field is not empty
    pub fn required(field_name: &str) -> FieldValidator {
        let field_name = field_name.to_string();
        Rc::new(move |value: &str| {
            if value.trim().is_empty() {
                Err(format!("{} is required", field_name))
            } else {
                Ok(())
            }
        })
    }

    /// Validates minimum length
    pub fn min_length(field_name: &str, min_len: usize) -> FieldValidator {
        let field_name = field_name.to_string();
        Rc::new(move |value: &str| {
            if value.len() < min_len {
                Err(format!("{} must be at least {} characters", field_name, min_len))
            } else {
                Ok(())
            }
        })
    }

    /// Validates maximum length
    pub fn max_length(field_name: &str, max_len: usize) -> FieldValidator {
        let field_name = field_name.to_string();
        Rc::new(move |value: &str| {
            if value.len() > max_len {
                Err(format!("{} must be no more than {} characters", field_name, max_len))
            } else {
                Ok(())
            }
        })
    }

    /// Validates email format
    pub fn email(field_name: &str) -> FieldValidator {
        let field_name = field_name.to_string();
        Rc::new(move |value: &str| {
            if value.contains('@') && value.contains('.') {
                Ok(())
            } else {
                Err(format!("{} must be a valid email address", field_name))
            }
        })
    }

    /// Validates IP address format
    pub fn ip_address(field_name: &str) -> FieldValidator {
        let field_name = field_name.to_string();
        Rc::new(move |value: &str| {
            let parts: Vec<&str> = value.split('.').collect();
            if parts.len() == 4 && parts.iter().all(|part| {
                part.parse::<u8>().is_ok()
            }) {
                Ok(())
            } else {
                Err(format!("{} must be a valid IP address", field_name))
            }
        })
    }

    /// Validates numeric range
    pub fn numeric_range(field_name: &str, min: f64, max: f64) -> FieldValidator {
        let field_name = field_name.to_string();
        Rc::new(move |value: &str| {
            match value.parse::<f64>() {
                Ok(num) if num >= min && num <= max => Ok(()),
                Ok(_) => Err(format!("{} must be between {} and {}", field_name, min, max)),
                Err(_) => Err(format!("{} must be a valid number", field_name)),
            }
        })
    }

    /// Combines multiple validators
    pub fn combine(validators: Vec<FieldValidator>) -> FieldValidator {
        Rc::new(move |value: &str| {
            for validator in &validators {
                if let Err(error) = validator(value) {
                    return Err(error);
                }
            }
            Ok(())
        })
    }
}

/// Modal management utilities
pub mod modal_utils {
    use super::*;

    /// Creates a simple confirmation dialog
    pub fn confirm_action(
        title: &str,
        message: &str,
        on_confirm: impl Fn() + 'static,
    ) -> (ReadSignal<bool>, WriteSignal<bool>, impl IntoView) {
        let (open, set_open) = create_signal(false);

        let modal = view! {
            <ConfirmModal
                open=open
                on_close=set_open
                title=title.to_string()
                message=message.to_string()
                on_confirm=Box::new(on_confirm)
            />
        };

        (open, set_open, modal)
    }

    /// Creates a simple alert dialog
    pub fn show_alert(
        title: &str,
        message: &str,
        alert_type: AlertType,
    ) -> (ReadSignal<bool>, WriteSignal<bool>, impl IntoView) {
        let (open, set_open) = create_signal(false);

        let modal = view! {
            <AlertModal
                open=open
                on_close=set_open
                title=title.to_string()
                message=message.to_string()
                alert_type=alert_type
            />
        };

        (open, set_open, modal)
    }

    /// Creates a progress dialog for async operations
    pub fn show_progress<F, Fut>(
        title: &str,
        operation: F,
    ) -> (ReadSignal<bool>, WriteSignal<bool>, ReadSignal<ProgressState>, impl IntoView)
    where
        F: Fn() -> Fut + 'static,
        Fut: std::future::Future<Output = Result<String, String>> + 'static,
    {
        let (open, set_open) = create_signal(false);
        let (progress, set_progress) = create_signal(ProgressState::Idle);

        let start_operation = {
            let operation = Rc::new(operation);
            move || {
                let operation = operation.clone();
                let set_progress = set_progress.clone();

                spawn_local(async move {
                    set_progress.set(ProgressState::InProgress {
                        message: "Processing...".to_string(),
                        percentage: None,
                    });

                    match operation().await {
                        Ok(success_msg) => {
                            set_progress.set(ProgressState::Success {
                                message: success_msg,
                            });
                        },
                        Err(error_msg) => {
                            set_progress.set(ProgressState::Error {
                                message: error_msg,
                            });
                        }
                    }
                });
            }
        };

        // Auto-start when modal opens
        create_effect(move |_| {
            if open.get() && matches!(progress.get(), ProgressState::Idle) {
                start_operation();
            }
        });

        let modal = view! {
            <ProgressModal
                open=open
                on_close=set_open
                title=title.to_string()
                progress=progress
            />
        };

        (open, set_open, progress, modal)
    }
}

/// Confirmation dialog modal
#[component]
pub fn ConfirmModal(
    /// Whether the modal is open
    open: ReadSignal<bool>,
    /// Signal to close the modal
    on_close: WriteSignal<bool>,
    /// Confirmation title
    title: String,
    /// Confirmation message
    message: String,
    /// Confirm button text
    #[prop(default = "Confirm".to_string())]
    confirm_text: String,
    /// Cancel button text
    #[prop(default = "Cancel".to_string())]
    cancel_text: String,
    /// Confirm button variant
    #[prop(default = super::navigation::ButtonVariant::Error)]
    confirm_variant: super::navigation::ButtonVariant,
    /// Confirmation handler
    on_confirm: Box<dyn Fn() + 'static>,
) -> impl IntoView {
    let handle_confirm = move |_| {
        on_confirm();
        on_close.set(false);
    };

    let handle_cancel = move |_| {
        on_close.set(false);
    };

    view! {
        <Modal
            open=open
            on_close=on_close
            title=title
            size=ModalSize::Small
        >
            <div class="space-y-4">
                <p class="text-sm text-gray-600">{message}</p>
                <div class="flex justify-end space-x-3">
                    <button
                        type="button"
                        class="btn-secondary"
                        on:click=handle_cancel
                    >
                        {cancel_text}
                    </button>
                    <button
                        type="button"
                        class={confirm_variant.to_class()}
                        on:click=handle_confirm
                    >
                        {confirm_text}
                    </button>
                </div>
            </div>
        </Modal>
    }
}

/// Alert modal for displaying messages
#[component]
pub fn AlertModal(
    /// Whether the modal is open
    open: ReadSignal<bool>,
    /// Signal to close the modal
    on_close: WriteSignal<bool>,
    /// Alert title
    title: String,
    /// Alert message
    message: String,
    /// Alert type (affects styling)
    #[prop(default = AlertType::Info)]
    alert_type: AlertType,
    /// OK button text
    #[prop(default = "OK".to_string())]
    ok_text: String,
) -> impl IntoView {
    let handle_ok = move |_| {
        on_close.set(false);
    };

    let (icon, title_class) = match alert_type {
        AlertType::Success => (
            view! {
                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
            },
            "text-green-800"
        ),
        AlertType::Warning => (
            view! {
                <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                </svg>
            },
            "text-yellow-800"
        ),
        AlertType::Error => (
            view! {
                <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
            },
            "text-red-800"
        ),
        AlertType::Info => (
            view! {
                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
            },
            "text-blue-800"
        ),
    };

    view! {
        <Modal
            open=open
            on_close=on_close
            title="".to_string()
            size=ModalSize::Small
            closable=false
        >
            <div class="space-y-4">
                <div class="flex items-center space-x-3">
                    {icon}
                    <h3 class={format!("text-lg font-medium {}", title_class)}>{title}</h3>
                </div>
                <p class="text-sm text-gray-600">{message}</p>
                <div class="flex justify-end">
                    <button
                        type="button"
                        class="btn-primary"
                        on:click=handle_ok
                    >
                        {ok_text}
                    </button>
                </div>
            </div>
        </Modal>
    }
}

/// Alert type for styling
#[derive(Clone, PartialEq)]
pub enum AlertType {
    Success,
    Warning,
    Error,
    Info,
}

/// Form validation error
#[derive(Clone, Debug)]
pub struct ValidationError {
    pub field: String,
    pub message: String,
}

/// Form validation result
pub type ValidationResult = Result<(), Vec<ValidationError>>;

/// Form field validator function type
pub type FieldValidator = Rc<dyn Fn(&str) -> Result<(), String>>;

/// Progress indicator state
#[derive(Clone, PartialEq)]
pub enum ProgressState {
    Idle,
    InProgress { message: String, percentage: Option<f32> },
    Success { message: String },
    Error { message: String },
}

/// Modal animation type
#[derive(Clone, PartialEq)]
pub enum ModalAnimation {
    Fade,
    Scale,
    SlideDown,
    SlideUp,
}

impl ModalAnimation {
    fn to_class(&self) -> &'static str {
        match self {
            ModalAnimation::Fade => "modal-fade",
            ModalAnimation::Scale => "modal-scale",
            ModalAnimation::SlideDown => "modal-slide-down",
            ModalAnimation::SlideUp => "modal-slide-up",
        }
    }
}

/// Modal position
#[derive(Clone, PartialEq)]
pub enum ModalPosition {
    Center,
    Top,
    Bottom,
    Left,
    Right,
}

impl ModalPosition {
    fn to_class(&self) -> &'static str {
        match self {
            ModalPosition::Center => "items-center justify-center",
            ModalPosition::Top => "items-start justify-center pt-16",
            ModalPosition::Bottom => "items-end justify-center pb-16",
            ModalPosition::Left => "items-center justify-start pl-16",
            ModalPosition::Right => "items-center justify-end pr-16",
        }
    }
}
