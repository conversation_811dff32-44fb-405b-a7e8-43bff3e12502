use leptos::*;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// File sharing application types
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum FileSharingAppType {
    SambaSmb,
    FtpServer,
    SftpServer,
    NfsServer,
    WebDav,
    MinioS3,
    NextcloudFiles,
    FileManager,
    TorrentClient,
    DlnaMediaServer,
    PrintServer,
    BackupServer,
}

impl FileSharingAppType {
    pub fn display_name(&self) -> &'static str {
        match self {
            FileSharingAppType::SambaSmb => "Samba/SMB",
            FileSharingAppType::FtpServer => "FTP Server",
            FileSharingAppType::SftpServer => "SFTP Server",
            FileSharingAppType::NfsServer => "NFS Server",
            FileSharingAppType::WebDav => "WebDAV Server",
            FileSharingAppType::MinioS3 => "MinIO S3 Storage",
            FileSharingAppType::NextcloudFiles => "Nextcloud Files",
            FileSharingAppType::FileManager => "Web File Manager",
            FileSharingAppType::TorrentClient => "Torrent Client",
            FileSharingAppType::DlnaMediaServer => "DLNA Media Server",
            FileSharingAppType::PrintServer => "Print Server",
            FileSharingAppType::BackupServer => "Backup Server",
        }
    }
    
    pub fn icon(&self) -> &'static str {
        match self {
            FileSharingAppType::SambaSmb => "🗂️",
            FileSharingAppType::FtpServer => "📁",
            FileSharingAppType::SftpServer => "🔒",
            FileSharingAppType::NfsServer => "🌐",
            FileSharingAppType::WebDav => "☁️",
            FileSharingAppType::MinioS3 => "🪣",
            FileSharingAppType::NextcloudFiles => "☁️",
            FileSharingAppType::FileManager => "📋",
            FileSharingAppType::TorrentClient => "🌊",
            FileSharingAppType::DlnaMediaServer => "📺",
            FileSharingAppType::PrintServer => "🖨️",
            FileSharingAppType::BackupServer => "💾",
        }
    }
    
    pub fn description(&self) -> &'static str {
        match self {
            FileSharingAppType::SambaSmb => "Windows file sharing with SMB/CIFS protocol",
            FileSharingAppType::FtpServer => "File Transfer Protocol server for file uploads/downloads",
            FileSharingAppType::SftpServer => "Secure FTP server with SSH encryption",
            FileSharingAppType::NfsServer => "Network File System for Unix/Linux file sharing",
            FileSharingAppType::WebDav => "Web-based file sharing and collaboration",
            FileSharingAppType::MinioS3 => "S3-compatible object storage server",
            FileSharingAppType::NextcloudFiles => "Self-hosted cloud storage and collaboration",
            FileSharingAppType::FileManager => "Web-based file management interface",
            FileSharingAppType::TorrentClient => "BitTorrent client for P2P file sharing",
            FileSharingAppType::DlnaMediaServer => "Digital media server for streaming",
            FileSharingAppType::PrintServer => "Network printer sharing service",
            FileSharingAppType::BackupServer => "Automated backup and restore service",
        }
    }
    
    pub fn protocol(&self) -> &'static str {
        match self {
            FileSharingAppType::SambaSmb => "SMB/CIFS",
            FileSharingAppType::FtpServer => "FTP",
            FileSharingAppType::SftpServer => "SFTP/SSH",
            FileSharingAppType::NfsServer => "NFS",
            FileSharingAppType::WebDav => "WebDAV/HTTP",
            FileSharingAppType::MinioS3 => "S3/HTTP",
            FileSharingAppType::NextcloudFiles => "HTTP/WebDAV",
            FileSharingAppType::FileManager => "HTTP",
            FileSharingAppType::TorrentClient => "BitTorrent",
            FileSharingAppType::DlnaMediaServer => "DLNA/UPnP",
            FileSharingAppType::PrintServer => "IPP/LPD",
            FileSharingAppType::BackupServer => "Various",
        }
    }
    
    pub fn default_port(&self) -> u16 {
        match self {
            FileSharingAppType::SambaSmb => 445,
            FileSharingAppType::FtpServer => 21,
            FileSharingAppType::SftpServer => 22,
            FileSharingAppType::NfsServer => 2049,
            FileSharingAppType::WebDav => 80,
            FileSharingAppType::MinioS3 => 9000,
            FileSharingAppType::NextcloudFiles => 80,
            FileSharingAppType::FileManager => 8080,
            FileSharingAppType::TorrentClient => 6881,
            FileSharingAppType::DlnaMediaServer => 8200,
            FileSharingAppType::PrintServer => 631,
            FileSharingAppType::BackupServer => 8443,
        }
    }
}

/// File share configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileShare {
    pub id: String,
    pub name: String,
    pub path: String,
    pub description: String,
    pub read_only: bool,
    pub guest_access: bool,
    pub valid_users: Vec<String>,
    pub max_connections: u32,
    pub active_connections: u32,
    pub bytes_transferred: u64,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub last_accessed: Option<chrono::DateTime<chrono::Utc>>,
}

/// File sharing application
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileSharingApp {
    pub id: String,
    pub name: String,
    pub app_type: FileSharingAppType,
    pub enabled: bool,
    pub running: bool,
    pub config: HashMap<String, String>,
    pub shares: Vec<FileShare>,
    pub listen_port: u16,
    pub max_connections: u32,
    pub active_connections: u32,
    pub total_bytes_sent: u64,
    pub total_bytes_received: u64,
    pub uptime: Option<std::time::Duration>,
    pub last_restart: Option<chrono::DateTime<chrono::Utc>>,
    pub storage_used: u64,
    pub storage_available: u64,
}

impl FileSharingApp {
    pub fn new(app_type: FileSharingAppType) -> Self {
        Self {
            id: format!("{:?}", app_type).to_lowercase().replace("_", "-"),
            name: app_type.display_name().to_string(),
            app_type: app_type.clone(),
            enabled: false,
            running: false,
            config: HashMap::new(),
            shares: Vec::new(),
            listen_port: app_type.default_port(),
            max_connections: match app_type {
                FileSharingAppType::SambaSmb => 50,
                FileSharingAppType::FtpServer => 20,
                FileSharingAppType::SftpServer => 10,
                FileSharingAppType::NfsServer => 100,
                FileSharingAppType::WebDav => 30,
                FileSharingAppType::MinioS3 => 100,
                FileSharingAppType::NextcloudFiles => 50,
                FileSharingAppType::FileManager => 20,
                FileSharingAppType::TorrentClient => 200,
                FileSharingAppType::DlnaMediaServer => 10,
                FileSharingAppType::PrintServer => 20,
                FileSharingAppType::BackupServer => 5,
            },
            active_connections: 0,
            total_bytes_sent: 0,
            total_bytes_received: 0,
            uptime: None,
            last_restart: None,
            storage_used: 0,
            storage_available: 0,
        }
    }
}

/// File sharing statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileSharingStats {
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub total_shares: u32,
    pub active_connections: u32,
    pub bytes_per_second_in: f64,
    pub bytes_per_second_out: f64,
    pub storage_usage_percent: f64,
    pub top_shares: Vec<(String, u64)>,
    pub protocol_distribution: HashMap<String, u32>,
    pub user_activity: HashMap<String, u32>,
}

/// File sharing applications manager component
#[component]
pub fn FileSharingAppsManager(
    #[prop(optional)] class: Option<String>,
) -> impl IntoView {
    let (apps, set_apps) = create_signal(get_default_file_sharing_apps());
    let (stats, set_stats) = create_signal(get_mock_file_sharing_stats());
    let (selected_app, set_selected_app) = create_signal(None::<String>);
    let (loading, set_loading) = create_signal(false);
    let (error, set_error) = create_signal(None::<String>);
    
    // Load file sharing apps on mount
    create_effect(move |_| {
        spawn_local(async move {
            set_loading.set(true);
            set_error.set(None);
            
            match load_file_sharing_apps().await {
                Ok(loaded_apps) => {
                    set_apps.set(loaded_apps);
                }
                Err(e) => {
                    set_error.set(Some(e));
                }
            }
            
            set_loading.set(false);
        });
    });
    
    // Auto-refresh stats every 20 seconds
    create_effect(move |_| {
        let interval = gloo_timers::callback::Interval::new(20000, move || {
            spawn_local(async move {
                if let Ok(new_stats) = load_file_sharing_stats().await {
                    set_stats.set(new_stats);
                }
            });
        });
        
        // Keep interval alive
        std::mem::forget(interval);
    });
    
    view! {
        <div class=format!("file-sharing-apps-manager {}", class.unwrap_or_default())>
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-lg font-semibold text-gray-900">File Sharing Applications</h2>
                            <p class="mt-1 text-sm text-gray-600">Manage SMB, FTP, and other file sharing services</p>
                        </div>
                        <div class="flex space-x-2">
                            <button 
                                class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
                                on:click=move |_| {
                                    spawn_local(async move {
                                        if let Ok(refreshed) = load_file_sharing_apps().await {
                                            set_apps.set(refreshed);
                                        }
                                    });
                                }
                            >
                                🔄 Refresh
                            </button>
                            <button class="bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700 transition-colors">
                                "➕ Add Share"
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="p-6">
                    {move || {
                        if loading.get() {
                            view! {
                                <div class="flex items-center justify-center py-8">
                                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                    <span class="ml-3 text-gray-600">Loading file sharing applications...</span>
                                </div>
                            }.into_view()
                        } else if let Some(err) = error.get() {
                            view! {
                                <div class="bg-red-50 border border-red-200 rounded-md p-4">
                                    <div class="flex">
                                        <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                                        </svg>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-red-800">File Sharing Apps Loading Error</h3>
                                            <p class="mt-1 text-sm text-red-700">{err}</p>
                                        </div>
                                    </div>
                                </div>
                            }.into_view()
                        } else {
                            view! {
                                <div class="space-y-6">
                                    // File sharing overview
                                    <FileSharingOverview apps=apps stats=stats />
                                    
                                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                        // File sharing apps list
                                        <div>
                                            <FileSharingAppsList 
                                                apps=apps
                                                selected_app=selected_app
                                                on_select=move |id: String| set_selected_app.set(Some(id))
                                                on_app_action=move |action: FileSharingAppAction| {
                                                    spawn_local(async move {
                                                        if let Ok(updated) = handle_file_sharing_app_action(action).await {
                                                            set_apps.set(updated);
                                                        }
                                                    });
                                                }
                                            />
                                        </div>
                                        
                                        // File shares
                                        <div>
                                            <FileSharesList apps=apps selected_app=selected_app />
                                        </div>
                                    </div>
                                </div>
                            }.into_view()
                        }
                    }}
                </div>
            </div>
        </div>
    }
}

/// File sharing app action types
#[derive(Debug, Clone)]
pub enum FileSharingAppAction {
    Enable(String),
    Disable(String),
    Start(String),
    Stop(String),
    Restart(String),
    Configure(String, HashMap<String, String>),
    AddShare(String, FileShare),
    RemoveShare(String, String),
    UpdateShare(String, FileShare),
    DisconnectUser(String, String),
}

/// File sharing overview component
#[component]
fn FileSharingOverview(
    apps: ReadSignal<Vec<FileSharingApp>>,
    stats: ReadSignal<FileSharingStats>,
) -> impl IntoView {
    view! {
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {move || {
                let app_list = apps.get();
                let current_stats = stats.get();

                let active_services = app_list.iter().filter(|app| app.running).count();
                let total_shares = app_list.iter().map(|app| app.shares.len()).sum::<usize>();
                let total_connections = app_list.iter().map(|app| app.active_connections).sum::<u32>();
                let total_storage_used = app_list.iter().map(|app| app.storage_used).sum::<u64>();

                view! {
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 19a2 2 0 01-2-2V7a2 2 0 012-2h4l2 2h4a2 2 0 012 2v1M5 19h14a2 2 0 002-2v-5a2 2 0 00-2-2H9a2 2 0 00-2 2v5a2 2 0 01-2 2z"/>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-blue-600">Active Services</p>
                                <p class="text-2xl font-semibold text-blue-900">{active_services}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-green-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"/>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-green-600">File Shares</p>
                                <p class="text-2xl font-semibold text-green-900">{total_shares}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-purple-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-purple-600">Connections</p>
                                <p class="text-2xl font-semibold text-purple-900">{total_connections}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-yellow-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"/>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-yellow-600">Storage Used</p>
                                <p class="text-2xl font-semibold text-yellow-900">{format_bytes(total_storage_used)}</p>
                            </div>
                        </div>
                    </div>
                }
            }}
        </div>
    }
}

/// File sharing apps list component
#[component]
fn FileSharingAppsList(
    apps: ReadSignal<Vec<FileSharingApp>>,
    selected_app: ReadSignal<Option<String>>,
    on_select: impl Fn(String) + 'static + Copy,
    on_app_action: impl Fn(FileSharingAppAction) + 'static + Copy,
) -> impl IntoView {
    view! {
        <div class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900">File Sharing Services</h3>

            <div class="space-y-2">
                {move || {
                    let app_list = apps.get();

                    if app_list.is_empty() {
                        view! {
                            <div class="text-center py-8 text-gray-500">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 19a2 2 0 01-2-2V7a2 2 0 012-2h4l2 2h4a2 2 0 012 2v1M5 19h14a2 2 0 002-2v-5a2 2 0 00-2-2H9a2 2 0 00-2 2v5a2 2 0 01-2 2z"/>
                                </svg>
                                <p class="mt-2">No file sharing services configured</p>
                            </div>
                        }.into_view()
                    } else {
                        app_list.into_iter().map(|app| {
                            let app_id = app.id.clone();
                            let is_selected = selected_app.get().as_ref() == Some(&app.id);

                            view! {
                                <FileSharingAppCard
                                    app=app
                                    is_selected=is_selected
                                    on_select=move || on_select(app_id.clone())
                                    on_action=on_app_action
                                />
                            }
                        }).collect_view()
                    }
                }}
            </div>
        </div>
    }
}

/// File sharing app card component
#[component]
fn FileSharingAppCard(
    app: FileSharingApp,
    is_selected: bool,
    on_select: impl Fn() + 'static + Copy,
    on_action: impl Fn(FileSharingAppAction) + 'static + Copy,
) -> impl IntoView {
    let app_id = app.id.clone();

    view! {
        <div
            class=move || format!(
                "border rounded-lg p-4 cursor-pointer transition-colors {}",
                if is_selected {
                    "border-blue-500 bg-blue-50"
                } else {
                    "border-gray-200 hover:border-gray-300 bg-white"
                }
            )
            on:click=move |_| on_select()
        >
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <span class="text-2xl">{app.app_type.icon()}</span>
                    <div>
                        <h4 class="font-medium text-gray-900">{app.name.clone()}</h4>
                        <p class="text-sm text-gray-500">{app.app_type.protocol()} " • Port " {app.listen_port}</p>
                    </div>
                </div>

                <div class="flex items-center space-x-2">
                    <div class="flex items-center space-x-1">
                        {if app.enabled {
                            view! {
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-green-600 bg-green-100">
                                    "✓ Enabled"
                                </span>
                            }.into_view()
                        } else {
                            view! {
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-gray-600 bg-gray-100">
                                    "○ Disabled"
                                </span>
                            }.into_view()
                        }}

                        {if app.running {
                            view! {
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-blue-600 bg-blue-100">
                                    ▶️ Running
                                </span>
                            }.into_view()
                        } else {
                            view! {
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-gray-600 bg-gray-100">
                                    ⏹️ Stopped
                                </span>
                            }.into_view()
                        }}
                    </div>

                    <div class="flex space-x-1">
                        <button
                            class="p-1 text-blue-600 hover:bg-blue-100 rounded"
                            title="Restart service"
                            on:click=move |e| {
                                e.stop_propagation();
                                on_action(FileSharingAppAction::Restart(app_id.clone()));
                            }
                        >
                            🔄
                        </button>

                        <button
                            class="p-1 text-green-600 hover:bg-green-100 rounded"
                            title="Add share"
                            on:click=move |e| {
                                e.stop_propagation();
                                // Would open add share dialog
                            }
                        >
                            "➕"
                        </button>
                    </div>
                </div>
            </div>

            <div class="mt-3 grid grid-cols-3 gap-4 text-sm text-gray-500">
                <div>
                    <span class="font-medium">Shares:</span> {app.shares.len()}
                </div>
                <div>
                    <span class="font-medium">Connections:</span> {app.active_connections}/{app.max_connections}
                </div>
                <div>
                    <span class="font-medium">Storage:</span> {format_bytes(app.storage_used)}
                </div>
            </div>

            {if app.storage_used > 0 && app.storage_available > 0 {
                let usage_percent = (app.storage_used as f64 / (app.storage_used + app.storage_available) as f64) * 100.0;
                view! {
                    <div class="mt-2">
                        <div class="flex justify-between text-xs text-gray-500 mb-1">
                            <span>Storage Usage</span>
                            <span>{format!("{:.1}%", usage_percent)}</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div
                                class=format!(
                                    "h-2 rounded-full {}",
                                    if usage_percent > 90.0 {
                                        "bg-red-500"
                                    } else if usage_percent > 75.0 {
                                        "bg-yellow-500"
                                    } else {
                                        "bg-green-500"
                                    }
                                )
                                style=format!("width: {}%", usage_percent)
                            ></div>
                        </div>
                    </div>
                }.into_view()
            } else {
                view! {
                    <div class="mt-2 text-xs text-gray-400">
                        Storage information not available
                    </div>
                }.into_view()
            }}
        </div>
    }
}

/// File shares list component
#[component]
fn FileSharesList(
    apps: ReadSignal<Vec<FileSharingApp>>,
    selected_app: ReadSignal<Option<String>>,
) -> impl IntoView {
    view! {
        <div class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900">File Shares</h3>

            <div class="space-y-2 max-h-96 overflow-y-auto">
                {move || {
                    let app_list = apps.get();
                    let selected_id = selected_app.get();

                    let shares: Vec<(String, FileShare)> = if let Some(app_id) = selected_id {
                        app_list.iter()
                            .find(|app| app.id == app_id)
                            .map(|app| app.shares.iter().map(|share| (app.name.clone(), share.clone())).collect())
                            .unwrap_or_default()
                    } else {
                        app_list.iter()
                            .flat_map(|app| app.shares.iter().map(|share| (app.name.clone(), share.clone())))
                            .collect()
                    };

                    if shares.is_empty() {
                        view! {
                            <div class="text-center py-8 text-gray-500">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"/>
                                </svg>
                                <p class="mt-2">No file shares configured</p>
                            </div>
                        }.into_view()
                    } else {
                        shares.into_iter().map(|(service_name, share)| {
                            view! {
                                <FileShareCard service_name=service_name share=share />
                            }
                        }).collect_view()
                    }
                }}
            </div>
        </div>
    }
}

/// File share card component
#[component]
fn FileShareCard(
    service_name: String,
    share: FileShare,
) -> impl IntoView {
    view! {
        <div class="border rounded-lg p-3 bg-white hover:bg-gray-50 transition-colors">
            <div class="flex items-start justify-between">
                <div class="flex items-start space-x-3">
                    <span class="text-lg">📁</span>
                    <div class="flex-1">
                        <div class="flex items-center space-x-2">
                            <h4 class="font-medium text-gray-900">{share.name.clone()}</h4>
                            <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                                {service_name}
                            </span>
                        </div>
                        <p class="text-sm text-gray-600 mt-1">{share.description.clone()}</p>
                        <div class="mt-2 space-y-1">
                            <div class="flex items-center space-x-4 text-sm text-gray-600">
                                <span>Path: {share.path.clone()}</span>
                                {if share.read_only {
                                    view! {
                                        <span class="text-yellow-600">🔒 Read-only</span>
                                    }.into_view()
                                } else {
                                    view! {
                                        <span class="text-green-600">✏️ Read-write</span>
                                    }.into_view()
                                }}
                                {if share.guest_access {
                                    view! {
                                        <span class="text-blue-600">👤 Guest access</span>
                                    }.into_view()
                                } else {
                                    view! { <span></span> }.into_view()
                                }}
                            </div>
                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                <span>Connections: {share.active_connections}/{share.max_connections}</span>
                                <span>Data: {format_bytes(share.bytes_transferred)}</span>
                                {if let Some(last_access) = share.last_accessed {
                                    let time_ago = chrono::Utc::now() - last_access;
                                    view! {
                                        <span>Last access: {format_time_ago(time_ago)}</span>
                                    }.into_view()
                                } else {
                                    view! {
                                        <span>Never accessed</span>
                                    }.into_view()
                                }}
                            </div>
                            {if !share.valid_users.is_empty() {
                                view! {
                                    <div class="text-sm text-gray-500">
                                        <span>Users: </span>
                                        {share.valid_users.iter().take(3).map(|user| {
                                            view! {
                                                <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded mr-1">
                                                    {user.clone()}
                                                </span>
                                            }
                                        }).collect::<Vec<_>>()}
                                        {if share.valid_users.len() > 3 {
                                            view! {
                                                <span class="text-xs text-gray-400">
                                                    +{share.valid_users.len() - 3} more
                                                </span>
                                            }.into_view()
                                        } else {
                                            view! { <span></span> }.into_view()
                                        }}
                                    </div>
                                }.into_view()
                            } else {
                                view! { <div></div> }.into_view()
                            }}
                        </div>
                    </div>
                </div>

                <div class="flex space-x-1">
                    <button class="p-1 text-blue-600 hover:bg-blue-100 rounded" title="Edit share">
                        ✏️
                    </button>
                    <button class="p-1 text-red-600 hover:bg-red-100 rounded" title="Remove share">
                        🗑️
                    </button>
                </div>
            </div>
        </div>
    }
}

/// Utility function to format bytes
fn format_bytes(bytes: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = bytes as f64;
    let mut unit_index = 0;

    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }

    if unit_index == 0 {
        format!("{} {}", bytes, UNITS[unit_index])
    } else {
        format!("{:.1} {}", size, UNITS[unit_index])
    }
}

/// Utility function to format time ago
fn format_time_ago(duration: chrono::Duration) -> String {
    let total_seconds = duration.num_seconds();

    if total_seconds < 60 {
        format!("{}s ago", total_seconds)
    } else if total_seconds < 3600 {
        format!("{}m ago", total_seconds / 60)
    } else if total_seconds < 86400 {
        format!("{}h ago", total_seconds / 3600)
    } else {
        format!("{}d ago", total_seconds / 86400)
    }
}

/// Get default file sharing applications
fn get_default_file_sharing_apps() -> Vec<FileSharingApp> {
    let mut apps = vec![
        FileSharingApp::new(FileSharingAppType::SambaSmb),
        FileSharingApp::new(FileSharingAppType::FtpServer),
        FileSharingApp::new(FileSharingAppType::SftpServer),
        FileSharingApp::new(FileSharingAppType::NfsServer),
        FileSharingApp::new(FileSharingAppType::WebDav),
        FileSharingApp::new(FileSharingAppType::FileManager),
        FileSharingApp::new(FileSharingAppType::DlnaMediaServer),
        FileSharingApp::new(FileSharingAppType::BackupServer),
    ];

    // Configure Samba with realistic settings
    if let Some(samba) = apps.iter_mut().find(|app| app.app_type == FileSharingAppType::SambaSmb) {
        samba.enabled = true;
        samba.running = true;
        samba.active_connections = 5;
        samba.total_bytes_sent = 1024 * 1024 * 1024 * 2; // 2 GB
        samba.total_bytes_received = 1024 * 1024 * 512; // 512 MB
        samba.storage_used = 1024 * 1024 * 1024 * 15; // 15 GB
        samba.storage_available = 1024 * 1024 * 1024 * 85; // 85 GB
        samba.uptime = Some(std::time::Duration::from_secs(86400 * 7)); // 7 days
        samba.config.insert("workgroup".to_string(), "WORKGROUP".to_string());
        samba.config.insert("security".to_string(), "user".to_string());

        // Add sample shares
        samba.shares = vec![
            FileShare {
                id: "share-public".to_string(),
                name: "Public".to_string(),
                path: "/mnt/storage/public".to_string(),
                description: "Public file sharing for all users".to_string(),
                read_only: false,
                guest_access: true,
                valid_users: vec!["admin".to_string(), "user1".to_string(), "user2".to_string()],
                max_connections: 20,
                active_connections: 3,
                bytes_transferred: 1024 * 1024 * 456, // 456 MB
                created_at: chrono::Utc::now() - chrono::Duration::days(30),
                last_accessed: Some(chrono::Utc::now() - chrono::Duration::minutes(15)),
            },
            FileShare {
                id: "share-private".to_string(),
                name: "Private".to_string(),
                path: "/mnt/storage/private".to_string(),
                description: "Private file sharing for authorized users only".to_string(),
                read_only: false,
                guest_access: false,
                valid_users: vec!["admin".to_string()],
                max_connections: 5,
                active_connections: 1,
                bytes_transferred: 1024 * 1024 * 123, // 123 MB
                created_at: chrono::Utc::now() - chrono::Duration::days(15),
                last_accessed: Some(chrono::Utc::now() - chrono::Duration::hours(2)),
            },
        ];
    }

    // Configure FTP Server
    if let Some(ftp) = apps.iter_mut().find(|app| app.app_type == FileSharingAppType::FtpServer) {
        ftp.enabled = true;
        ftp.running = true;
        ftp.active_connections = 2;
        ftp.total_bytes_sent = 1024 * 1024 * 789; // 789 MB
        ftp.total_bytes_received = 1024 * 1024 * 234; // 234 MB
        ftp.storage_used = 1024 * 1024 * 1024 * 5; // 5 GB
        ftp.storage_available = 1024 * 1024 * 1024 * 95; // 95 GB
        ftp.config.insert("passive_mode".to_string(), "true".to_string());
        ftp.config.insert("max_clients".to_string(), "20".to_string());

        ftp.shares = vec![
            FileShare {
                id: "ftp-uploads".to_string(),
                name: "Uploads".to_string(),
                path: "/mnt/ftp/uploads".to_string(),
                description: "FTP upload directory".to_string(),
                read_only: false,
                guest_access: false,
                valid_users: vec!["ftpuser".to_string(), "admin".to_string()],
                max_connections: 10,
                active_connections: 2,
                bytes_transferred: 1024 * 1024 * 345, // 345 MB
                created_at: chrono::Utc::now() - chrono::Duration::days(10),
                last_accessed: Some(chrono::Utc::now() - chrono::Duration::minutes(30)),
            },
        ];
    }

    apps
}

/// Get mock file sharing statistics
fn get_mock_file_sharing_stats() -> FileSharingStats {
    FileSharingStats {
        timestamp: chrono::Utc::now(),
        total_shares: 12,
        active_connections: 7,
        bytes_per_second_in: 2048.7,
        bytes_per_second_out: 4096.3,
        storage_usage_percent: 18.5,
        top_shares: vec![
            ("Public".to_string(), 1024 * 1024 * 456),
            ("Uploads".to_string(), 1024 * 1024 * 345),
            ("Private".to_string(), 1024 * 1024 * 123),
            ("Media".to_string(), 1024 * 1024 * 789),
            ("Backup".to_string(), 1024 * 1024 * 234),
        ],
        protocol_distribution: HashMap::from([
            ("SMB/CIFS".to_string(), 5),
            ("FTP".to_string(), 2),
            ("SFTP".to_string(), 0),
            ("NFS".to_string(), 0),
            ("WebDAV".to_string(), 0),
        ]),
        user_activity: HashMap::from([
            ("admin".to_string(), 15),
            ("user1".to_string(), 8),
            ("user2".to_string(), 5),
            ("ftpuser".to_string(), 3),
            ("guest".to_string(), 12),
        ]),
    }
}

/// Load file sharing applications from system (mock implementation)
async fn load_file_sharing_apps() -> Result<Vec<FileSharingApp>, String> {
    // Simulate network delay
    gloo_timers::future::TimeoutFuture::new(800).await;

    // In a real implementation, this would query the file sharing system
    // For now, return mock data
    Ok(get_default_file_sharing_apps())
}

/// Load file sharing statistics (mock implementation)
async fn load_file_sharing_stats() -> Result<FileSharingStats, String> {
    // Simulate network delay
    gloo_timers::future::TimeoutFuture::new(500).await;

    // In a real implementation, this would query file sharing statistics
    // For now, return mock data with some variation
    let mut stats = get_mock_file_sharing_stats();

    // Add some random variation to make it look live
    use js_sys::Math;
    stats.bytes_per_second_in = 1500.0 + (Math::random() * 1000.0);
    stats.bytes_per_second_out = 3000.0 + (Math::random() * 2000.0);
    stats.active_connections = 5 + (Math::random() * 8.0) as u32;
    stats.storage_usage_percent = 15.0 + (Math::random() * 10.0);

    Ok(stats)
}

/// Handle file sharing app actions (mock implementation)
async fn handle_file_sharing_app_action(action: FileSharingAppAction) -> Result<Vec<FileSharingApp>, String> {
    // Simulate processing delay
    gloo_timers::future::TimeoutFuture::new(1200).await;

    match action {
        FileSharingAppAction::Enable(app_id) => {
            log::info!("Enabling file sharing app: {}", app_id);
            // In real implementation, would enable the file sharing service
        }
        FileSharingAppAction::Disable(app_id) => {
            log::info!("Disabling file sharing app: {}", app_id);
            // In real implementation, would disable the file sharing service
        }
        FileSharingAppAction::Start(app_id) => {
            log::info!("Starting file sharing app: {}", app_id);
            // In real implementation, would start the file sharing service
        }
        FileSharingAppAction::Stop(app_id) => {
            log::info!("Stopping file sharing app: {}", app_id);
            // In real implementation, would stop the file sharing service
        }
        FileSharingAppAction::Restart(app_id) => {
            log::info!("Restarting file sharing app: {}", app_id);
            // In real implementation, would restart the file sharing service
        }
        FileSharingAppAction::Configure(app_id, config) => {
            log::info!("Configuring file sharing app: {} with {:?}", app_id, config);
            // In real implementation, would update file sharing app configuration
        }
        FileSharingAppAction::AddShare(app_id, share) => {
            log::info!("Adding share: {} to app: {}", share.name, app_id);
            // In real implementation, would add new file share
        }
        FileSharingAppAction::RemoveShare(app_id, share_id) => {
            log::info!("Removing share: {} from app: {}", share_id, app_id);
            // In real implementation, would remove file share
        }
        FileSharingAppAction::UpdateShare(app_id, share) => {
            log::info!("Updating share: {} in app: {}", share.name, app_id);
            // In real implementation, would update file share configuration
        }
        FileSharingAppAction::DisconnectUser(app_id, user_id) => {
            log::info!("Disconnecting user: {} from app: {}", user_id, app_id);
            // In real implementation, would disconnect specific user
        }
    }

    // Return updated apps list
    load_file_sharing_apps().await
}
