use leptos::*;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Network service types
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum NetworkServiceType {
    NTP,
    SNMP,
    MDNS,
    DNS,
    DHCP,
    SSH,
    HTTP,
    HTTPS,
    Telnet,
    FTP,
    TFTP,
}

impl NetworkServiceType {
    pub fn display_name(&self) -> &'static str {
        match self {
            NetworkServiceType::NTP => "Network Time Protocol",
            NetworkServiceType::SNMP => "Simple Network Management Protocol",
            NetworkServiceType::MDNS => "Multicast DNS",
            NetworkServiceType::DNS => "Domain Name System",
            NetworkServiceType::DHCP => "Dynamic Host Configuration Protocol",
            NetworkServiceType::SSH => "Secure Shell",
            NetworkServiceType::HTTP => "HTTP Server",
            NetworkServiceType::HTTPS => "HTTPS Server",
            NetworkServiceType::Telnet => "Telnet Server",
            NetworkServiceType::FTP => "FTP Server",
            NetworkServiceType::TFTP => "TFTP Server",
        }
    }
    
    pub fn icon(&self) -> &'static str {
        match self {
            NetworkServiceType::NTP => "🕐",
            NetworkServiceType::SNMP => "📊",
            NetworkServiceType::MDNS => "🔍",
            NetworkServiceType::DNS => "🌐",
            NetworkServiceType::DHCP => "📡",
            NetworkServiceType::SSH => "🔐",
            NetworkServiceType::HTTP => "🌍",
            NetworkServiceType::HTTPS => "🔒",
            NetworkServiceType::Telnet => "💻",
            NetworkServiceType::FTP => "📁",
            NetworkServiceType::TFTP => "📂",
        }
    }
    
    pub fn default_port(&self) -> u16 {
        match self {
            NetworkServiceType::NTP => 123,
            NetworkServiceType::SNMP => 161,
            NetworkServiceType::MDNS => 5353,
            NetworkServiceType::DNS => 53,
            NetworkServiceType::DHCP => 67,
            NetworkServiceType::SSH => 22,
            NetworkServiceType::HTTP => 80,
            NetworkServiceType::HTTPS => 443,
            NetworkServiceType::Telnet => 23,
            NetworkServiceType::FTP => 21,
            NetworkServiceType::TFTP => 69,
        }
    }
}

/// Network service status
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ServiceStatus {
    Running,
    Stopped,
    Starting,
    Stopping,
    Error(String),
    Unknown,
}

impl ServiceStatus {
    pub fn display_name(&self) -> String {
        match self {
            ServiceStatus::Running => "Running".to_string(),
            ServiceStatus::Stopped => "Stopped".to_string(),
            ServiceStatus::Starting => "Starting".to_string(),
            ServiceStatus::Stopping => "Stopping".to_string(),
            ServiceStatus::Error(msg) => format!("Error: {}", msg),
            ServiceStatus::Unknown => "Unknown".to_string(),
        }
    }
    
    pub fn css_class(&self) -> &'static str {
        match self {
            ServiceStatus::Running => "text-green-600 bg-green-100",
            ServiceStatus::Stopped => "text-red-600 bg-red-100",
            ServiceStatus::Starting => "text-blue-600 bg-blue-100",
            ServiceStatus::Stopping => "text-yellow-600 bg-yellow-100",
            ServiceStatus::Error(_) => "text-red-600 bg-red-100",
            ServiceStatus::Unknown => "text-gray-600 bg-gray-100",
        }
    }
}

/// Network service configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkService {
    pub id: String,
    pub name: String,
    pub service_type: NetworkServiceType,
    pub status: ServiceStatus,
    pub enabled: bool,
    pub port: u16,
    pub interface: String,
    pub config: HashMap<String, String>,
    pub description: String,
    pub last_started: Option<chrono::DateTime<chrono::Utc>>,
    pub uptime: Option<std::time::Duration>,
    pub process_id: Option<u32>,
}

impl NetworkService {
    pub fn new(service_type: NetworkServiceType) -> Self {
        Self {
            id: format!("{:?}", service_type).to_lowercase(),
            name: service_type.display_name().to_string(),
            service_type: service_type.clone(),
            status: ServiceStatus::Stopped,
            enabled: false,
            port: service_type.default_port(),
            interface: "0.0.0.0".to_string(),
            config: HashMap::new(),
            description: format!("{} service", service_type.display_name()),
            last_started: None,
            uptime: None,
            process_id: None,
        }
    }
}

/// Network services manager component
#[component]
pub fn NetworkServicesManager(
    #[prop(optional)] class: Option<String>,
) -> impl IntoView {
    let (services, set_services) = create_signal(get_default_services());
    let (selected_service, set_selected_service) = create_signal(None::<String>);
    let (loading, set_loading) = create_signal(false);
    let (error, set_error) = create_signal(None::<String>);
    
    // Load services on mount
    create_effect(move |_| {
        spawn_local(async move {
            set_loading.set(true);
            set_error.set(None);
            
            match load_network_services().await {
                Ok(loaded_services) => {
                    set_services.set(loaded_services);
                }
                Err(e) => {
                    set_error.set(Some(e));
                }
            }
            
            set_loading.set(false);
        });
    });
    
    view! {
        <div class=format!("network-services-manager {}", class.unwrap_or_default())>
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-lg font-semibold text-gray-900">Network Services</h2>
                            <p class="mt-1 text-sm text-gray-600">Manage network services and protocols</p>
                        </div>
                        <div class="flex space-x-2">
                            <button 
                                class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
                                on:click=move |_| {
                                    spawn_local(async move {
                                        if let Ok(refreshed) = load_network_services().await {
                                            set_services.set(refreshed);
                                        }
                                    });
                                }
                            >
                                "🔄 Refresh"
                            </button>
                            <button class="bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700 transition-colors">
                                "➕ Add Service"
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="p-6">
                    {move || {
                        if loading.get() {
                            view! {
                                <div class="flex items-center justify-center py-8">
                                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                    <span class="ml-3 text-gray-600">Loading services...</span>
                                </div>
                            }.into_view()
                        } else if let Some(err) = error.get() {
                            view! {
                                <div class="bg-red-50 border border-red-200 rounded-md p-4">
                                    <div class="flex">
                                        <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                                        </svg>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-red-800">Service Loading Error</h3>
                                            <p class="mt-1 text-sm text-red-700">{err}</p>
                                        </div>
                                    </div>
                                </div>
                            }.into_view()
                        } else {
                            view! {
                                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                                    // Services list
                                    <div class="lg:col-span-2">
                                        <NetworkServicesList 
                                            services=services
                                            selected_service=selected_service
                                            on_select=move |id: String| set_selected_service.set(Some(id))
                                            on_service_action=move |action: ServiceAction| {
                                                spawn_local(async move {
                                                    if let Ok(updated) = handle_service_action(action).await {
                                                        set_services.set(updated);
                                                    }
                                                });
                                            }
                                        />
                                    </div>
                                    
                                    // Service details
                                    <div>
                                        <NetworkServiceDetails 
                                            services=services
                                            selected_service=selected_service
                                        />
                                    </div>
                                </div>
                            }.into_view()
                        }
                    }}
                </div>
            </div>
        </div>
    }
}

/// Service action types
#[derive(Debug, Clone)]
pub enum ServiceAction {
    Start(String),
    Stop(String),
    Restart(String),
    Enable(String),
    Disable(String),
    Configure(String, HashMap<String, String>),
}

/// Network services list component
#[component]
fn NetworkServicesList(
    services: ReadSignal<Vec<NetworkService>>,
    selected_service: ReadSignal<Option<String>>,
    on_select: impl Fn(String) + 'static + Copy,
    on_service_action: impl Fn(ServiceAction) + 'static + Copy,
) -> impl IntoView {
    view! {
        <div class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900">Available Services</h3>
            
            <div class="space-y-2">
                {move || {
                    let service_list = services.get();
                    
                    if service_list.is_empty() {
                        view! {
                            <div class="text-center py-8 text-gray-500">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                </svg>
                                <p class="mt-2">No network services configured</p>
                            </div>
                        }.into_view()
                    } else {
                        service_list.into_iter().map(|service| {
                            let service_id = service.id.clone();
                            let is_selected = selected_service.get().as_ref() == Some(&service.id);
                            
                            view! {
                                <NetworkServiceCard
                                    service=service
                                    is_selected=is_selected
                                    on_select=move || on_select(service_id.clone())
                                    on_action=on_service_action
                                />
                            }
                        }).collect_view()
                    }
                }}
            </div>
        </div>
    }
}

/// Network service card component
#[component]
fn NetworkServiceCard(
    service: NetworkService,
    is_selected: bool,
    on_select: impl Fn() + 'static + Copy,
    on_action: impl Fn(ServiceAction) + 'static + Copy,
) -> impl IntoView {
    let service_id = service.id.clone();
    let can_start = matches!(service.status, ServiceStatus::Stopped);
    let can_stop = matches!(service.status, ServiceStatus::Running);

    view! {
        <div
            class=move || format!(
                "border rounded-lg p-4 cursor-pointer transition-colors {}",
                if is_selected {
                    "border-blue-500 bg-blue-50"
                } else {
                    "border-gray-200 hover:border-gray-300 bg-white"
                }
            )
            on:click=move |_| on_select()
        >
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <span class="text-2xl">{service.service_type.icon()}</span>
                    <div>
                        <h4 class="font-medium text-gray-900">{service.name.clone()}</h4>
                        <p class="text-sm text-gray-500">{service.description.clone()}</p>
                    </div>
                </div>

                <div class="flex items-center space-x-2">
                    <span class=format!(
                        "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {}",
                        service.status.css_class()
                    )>
                        {service.status.display_name()}
                    </span>

                    <div class="flex space-x-1">
                        {if can_start {
                            let service_id = service_id.clone();
                            view! {
                                <button
                                    class="p-1 text-green-600 hover:bg-green-100 rounded"
                                    title="Start service"
                                    on:click=move |e| {
                                        e.stop_propagation();
                                        on_action(ServiceAction::Start(service_id.clone()));
                                    }
                                >
                                    ▶️
                                </button>
                            }.into_view()
                        } else {
                            view! { <div></div> }.into_view()
                        }}

                        {if can_stop {
                            let service_id = service_id.clone();
                            view! {
                                <button
                                    class="p-1 text-red-600 hover:bg-red-100 rounded"
                                    title="Stop service"
                                    on:click=move |e| {
                                        e.stop_propagation();
                                        on_action(ServiceAction::Stop(service_id.clone()));
                                    }
                                >
                                    ⏹️
                                </button>
                            }.into_view()
                        } else {
                            view! { <div></div> }.into_view()
                        }}

                        <button
                            class="p-1 text-blue-600 hover:bg-blue-100 rounded"
                            title="Restart service"
                            on:click=move |e| {
                                e.stop_propagation();
                                on_action(ServiceAction::Restart(service_id.clone()));
                            }
                        >
                            🔄
                        </button>
                    </div>
                </div>
            </div>

            <div class="mt-3 flex items-center justify-between text-sm text-gray-500">
                <div class="flex items-center space-x-4">
                    <span>Port: {service.port}</span>
                    <span>Interface: {service.interface.clone()}</span>
                    {if service.enabled {
                        view! {
                            <span class="text-green-600">"✓ Enabled"</span>
                        }.into_view()
                    } else {
                        view! {
                            <span class="text-gray-400">"○ Disabled"</span>
                        }.into_view()
                    }}
                </div>

                {if let Some(uptime) = service.uptime {
                    view! {
                        <span>Uptime: {format_duration(uptime)}</span>
                    }.into_view()
                } else {
                    view! { <div></div> }.into_view()
                }}
            </div>
        </div>
    }
}

/// Network service details component
#[component]
fn NetworkServiceDetails(
    services: ReadSignal<Vec<NetworkService>>,
    selected_service: ReadSignal<Option<String>>,
) -> impl IntoView {
    view! {
        <div class="bg-gray-50 rounded-lg p-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Service Details</h3>

            {move || {
                if let Some(service_id) = selected_service.get() {
                    let service_list = services.get();
                    if let Some(service) = service_list.iter().find(|s| s.id == service_id) {
                        view! {
                            <div class="space-y-4">
                                <div class="flex items-center space-x-3">
                                    <span class="text-3xl">{service.service_type.icon()}</span>
                                    <div>
                                        <h4 class="text-lg font-semibold text-gray-900">{service.name.clone()}</h4>
                                        <p class="text-sm text-gray-600">{service.description.clone()}</p>
                                    </div>
                                </div>

                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Status</label>
                                        <span class=format!(
                                            "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {}",
                                            service.status.css_class()
                                        )>
                                            {service.status.display_name()}
                                        </span>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Port</label>
                                        <p class="text-sm text-gray-900">{service.port}</p>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Interface</label>
                                        <p class="text-sm text-gray-900">{service.interface.clone()}</p>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Enabled</label>
                                        <p class="text-sm text-gray-900">
                                            {if service.enabled { "Yes" } else { "No" }}
                                        </p>
                                    </div>

                                    {if let Some(pid) = service.process_id {
                                        view! {
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Process ID</label>
                                                <p class="text-sm text-gray-900">{pid}</p>
                                            </div>
                                        }.into_view()
                                    } else {
                                        view! { <div></div> }.into_view()
                                    }}

                                    {if let Some(started) = service.last_started {
                                        view! {
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Last Started</label>
                                                <p class="text-sm text-gray-900">{started.format("%Y-%m-%d %H:%M:%S UTC")}</p>
                                            </div>
                                        }.into_view()
                                    } else {
                                        view! { <div></div> }.into_view()
                                    }}
                                </div>

                                {if !service.config.is_empty() {
                                    view! {
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Configuration</label>
                                            <div class="bg-white rounded border p-3 space-y-2">
                                                {service.config.iter().map(|(key, value)| {
                                                    view! {
                                                        <div class="flex justify-between">
                                                            <span class="text-sm font-medium text-gray-600">{key.clone()}</span>
                                                            <span class="text-sm text-gray-900">{value.clone()}</span>
                                                        </div>
                                                    }
                                                }).collect_view()}
                                            </div>
                                        </div>
                                    }.into_view()
                                } else {
                                    view! { <div></div> }.into_view()
                                }}

                                <div class="pt-4 border-t border-gray-200">
                                    <div class="flex space-x-2">
                                        <button class="bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700">
                                            Configure
                                        </button>
                                        <button class="bg-gray-600 text-white px-3 py-2 rounded text-sm hover:bg-gray-700">
                                            View Logs
                                        </button>
                                        <button class="bg-yellow-600 text-white px-3 py-2 rounded text-sm hover:bg-yellow-700">
                                            Diagnostics
                                        </button>
                                    </div>
                                </div>
                            </div>
                        }.into_view()
                    } else {
                        view! {
                            <p class="text-gray-500">Service not found</p>
                        }.into_view()
                    }
                } else {
                    view! {
                        <div class="text-center py-8 text-gray-500">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            <p class="mt-2">Select a service to view details</p>
                        </div>
                    }.into_view()
                }
            }}
        </div>
    }
}

/// Utility function to format duration
fn format_duration(duration: std::time::Duration) -> String {
    let total_seconds = duration.as_secs();
    let days = total_seconds / 86400;
    let hours = (total_seconds % 86400) / 3600;
    let minutes = (total_seconds % 3600) / 60;
    let seconds = total_seconds % 60;

    if days > 0 {
        format!("{}d {}h {}m", days, hours, minutes)
    } else if hours > 0 {
        format!("{}h {}m", hours, minutes)
    } else if minutes > 0 {
        format!("{}m {}s", minutes, seconds)
    } else {
        format!("{}s", seconds)
    }
}

/// Get default network services configuration
fn get_default_services() -> Vec<NetworkService> {
    let mut services = vec![
        NetworkService::new(NetworkServiceType::NTP),
        NetworkService::new(NetworkServiceType::SNMP),
        NetworkService::new(NetworkServiceType::MDNS),
        NetworkService::new(NetworkServiceType::DNS),
        NetworkService::new(NetworkServiceType::DHCP),
        NetworkService::new(NetworkServiceType::SSH),
        NetworkService::new(NetworkServiceType::HTTP),
        NetworkService::new(NetworkServiceType::HTTPS),
    ];

    // Configure some services with realistic settings
    if let Some(ntp) = services.iter_mut().find(|s| s.service_type == NetworkServiceType::NTP) {
        ntp.enabled = true;
        ntp.status = ServiceStatus::Running;
        ntp.last_started = Some(chrono::Utc::now() - chrono::Duration::hours(2));
        ntp.uptime = Some(std::time::Duration::from_secs(7200));
        ntp.process_id = Some(1234);
        ntp.config.insert("servers".to_string(), "pool.ntp.org".to_string());
        ntp.config.insert("sync_interval".to_string(), "300".to_string());
    }

    if let Some(ssh) = services.iter_mut().find(|s| s.service_type == NetworkServiceType::SSH) {
        ssh.enabled = true;
        ssh.status = ServiceStatus::Running;
        ssh.last_started = Some(chrono::Utc::now() - chrono::Duration::hours(24));
        ssh.uptime = Some(std::time::Duration::from_secs(86400));
        ssh.process_id = Some(5678);
        ssh.config.insert("port".to_string(), "22".to_string());
        ssh.config.insert("permit_root_login".to_string(), "yes".to_string());
    }

    if let Some(http) = services.iter_mut().find(|s| s.service_type == NetworkServiceType::HTTP) {
        http.enabled = true;
        http.status = ServiceStatus::Running;
        http.last_started = Some(chrono::Utc::now() - chrono::Duration::hours(1));
        http.uptime = Some(std::time::Duration::from_secs(3600));
        http.process_id = Some(9012);
        http.config.insert("document_root".to_string(), "/www".to_string());
        http.config.insert("index_file".to_string(), "index.html".to_string());
    }

    services
}

/// Load network services from system (mock implementation)
async fn load_network_services() -> Result<Vec<NetworkService>, String> {
    // Simulate network delay
    gloo_timers::future::TimeoutFuture::new(500).await;

    // In a real implementation, this would query the system
    // For now, return mock data
    Ok(get_default_services())
}

/// Handle service actions (mock implementation)
async fn handle_service_action(action: ServiceAction) -> Result<Vec<NetworkService>, String> {
    // Simulate processing delay
    gloo_timers::future::TimeoutFuture::new(1000).await;

    match action {
        ServiceAction::Start(service_id) => {
            log::info!("Starting service: {}", service_id);
            // In real implementation, would start the actual service
        }
        ServiceAction::Stop(service_id) => {
            log::info!("Stopping service: {}", service_id);
            // In real implementation, would stop the actual service
        }
        ServiceAction::Restart(service_id) => {
            log::info!("Restarting service: {}", service_id);
            // In real implementation, would restart the actual service
        }
        ServiceAction::Enable(service_id) => {
            log::info!("Enabling service: {}", service_id);
            // In real implementation, would enable service autostart
        }
        ServiceAction::Disable(service_id) => {
            log::info!("Disabling service: {}", service_id);
            // In real implementation, would disable service autostart
        }
        ServiceAction::Configure(service_id, config) => {
            log::info!("Configuring service: {} with {:?}", service_id, config);
            // In real implementation, would update service configuration
        }
    }

    // Return updated services list
    load_network_services().await
}
