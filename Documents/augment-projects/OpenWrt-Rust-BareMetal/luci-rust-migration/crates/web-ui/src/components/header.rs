//! Header component for LuCI Web Interface
//!
//! Provides the main navigation header with OpenWrt branding,
//! user authentication status, and primary navigation menu.

use leptos::*;
use leptos_router::*;
use crate::auth::{use_auth_context, UserInfo, RequirePermission, AdminOnly};

/// Main header component with navigation and user info
#[component]
pub fn Header() -> impl IntoView {
    view! {
        <header class="bg-blue-600 text-white shadow-lg">
            <div class="container mx-auto px-4">
                <div class="flex items-center justify-between h-16">
                    // Logo and title
                    <div class="flex items-center space-x-4">
                        <div class="flex-shrink-0">
                            <img
                                class="h-8 w-8"
                                src="/static/logo.png"
                                alt="OpenWrt"
                            />
                        </div>
                        <div class="hidden md:block">
                            <h1 class="text-xl font-semibold">
                                "OpenWrt"
                            </h1>
                        </div>
                    </div>

                    // Main navigation
                    <nav class="hidden md:block">
                        <div class="ml-10 flex items-baseline space-x-4">
                            // Status - available to all authenticated users
                            <NavLink href="/" class="nav-link">
                                "Status"
                            </NavLink>

                            // Processes - requires system.read permission
                            <RequirePermission permission="system.read".to_string()>
                                <NavLink href="/processes" class="nav-link">
                                    "Processes"
                                </NavLink>
                            </RequirePermission>

                            // System - requires system.read permission
                            <RequirePermission permission="system.read".to_string()>
                                <NavLink href="/system" class="nav-link">
                                    "System"
                                </NavLink>
                            </RequirePermission>

                            // Network - requires network.read permission
                            <RequirePermission permission="network.read".to_string()>
                                <NavLink href="/network" class="nav-link">
                                    "Network"
                                </NavLink>
                            </RequirePermission>

                            // Firewall - requires network.write permission
                            <RequirePermission permission="network.write".to_string()>
                                <NavLink href="/firewall" class="nav-link">
                                    "Firewall"
                                </NavLink>
                            </RequirePermission>

                            // Wireless - requires network.read permission
                            <RequirePermission permission="network.read".to_string()>
                                <NavLink href="/wireless" class="nav-link">
                                    "Wireless"
                                </NavLink>
                            </RequirePermission>

                            // Services - requires system.read permission
                            <RequirePermission permission="system.read".to_string()>
                                <NavLink href="/services" class="nav-link">
                                    "Services"
                                </NavLink>
                            </RequirePermission>

                            // Software/Packages - requires packages.read permission
                            <RequirePermission permission="packages.read".to_string()>
                                <NavLink href="/packages" class="nav-link">
                                    "Software"
                                </NavLink>
                            </RequirePermission>

                            // Administration - admin only
                            <AdminOnly>
                                <NavLink href="/administration" class="nav-link">
                                    "Administration"
                                </NavLink>
                            </AdminOnly>
                        </div>
                    </nav>

                    // User menu
                    <div class="flex items-center space-x-4">
                        <UserMenu/>
                        <MobileMenuButton/>
                    </div>
                </div>
            </div>

            // Mobile navigation menu
            <MobileNav/>
        </header>
    }
}

/// Navigation link component with active state styling
#[component]
pub fn NavLink(
    href: &'static str,
    class: &'static str,
    children: Children,
) -> impl IntoView {
    view! {
        <A
            href=href
            class=move || format!("{} hover:bg-blue-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors", class)
            active_class="bg-blue-800 text-white"
            exact=false
        >
            {children()}
        </A>
    }
}

/// User menu dropdown component
#[component]
pub fn UserMenu() -> impl IntoView {
    let auth = use_auth_context();
    let (show_menu, set_show_menu) = create_signal(false);

    // Close menu when clicking outside
    let close_menu = move || set_show_menu.set(false);

    view! {
        <div class="relative">
            {move || {
                let auth_state = auth.state.get();
                if auth_state.authenticated {
                    let user = auth_state.user.as_ref();
                    let username = user.map(|u| u.username.clone()).unwrap_or_else(|| "User".to_string());
                    let user_initial = username.chars().next().unwrap_or('U').to_uppercase().to_string();

                    view! {
                        <button
                            class="flex items-center space-x-2 text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-blue-600 focus:ring-white"
                            on:click=move |_| set_show_menu.update(|show| *show = !*show)
                        >
                            <span class="sr-only">"Open user menu"</span>
                            <div class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center">
                                <span class="text-xs font-medium">{user_initial}</span>
                            </div>
                            <span class="hidden md:block">{username.clone()}</span>
                            <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
                            </svg>
                        </button>

                        <div
                            class=move || if show_menu.get() {
                                "absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50"
                            } else {
                                "hidden"
                            }
                        >
                            <div class="py-1">
                                <div class="px-4 py-2 text-xs text-gray-500 border-b">
                                    "Signed in as " {username.clone()}
                                    <br/>
                                    <span class="text-blue-600 font-medium">
                                        {user.map(|u| u.role.clone()).unwrap_or_else(|| "guest".to_string())}
                                    </span>
                                </div>

                                // Profile/User Settings - available to all users
                                <A
                                    href="/profile"
                                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                    on:click=move |_| close_menu()
                                >
                                    "Profile"
                                </A>

                                // User Management - admin only
                                <AdminOnly>
                                    <A
                                        href="/administration/users"
                                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                        on:click=move |_| close_menu()
                                    >
                                        "User Management"
                                    </A>
                                </AdminOnly>

                                // System Settings - requires system.write permission
                                <RequirePermission permission="system.write".to_string()>
                                    <A
                                        href="/administration/system"
                                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                        on:click=move |_| close_menu()
                                    >
                                        "System Settings"
                                    </A>
                                </RequirePermission>

                                <hr class="my-1"/>
                                <A
                                    href="/logout"
                                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                    on:click=move |_| close_menu()
                                >
                                    "Logout"
                                </A>
                            </div>
                        </div>
                    }.into_view()
                } else {
                    view! {
                        <A
                            href="/login"
                            class="bg-blue-500 hover:bg-blue-400 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                        >
                            "Login"
                        </A>
                    }.into_view()
                }
            }}
        </div>
    }
}

/// Mobile menu button component
#[component]
pub fn MobileMenuButton() -> impl IntoView {
    let (show_mobile_menu, set_show_mobile_menu) = create_signal(false);

    view! {
        <button
            class="md:hidden inline-flex items-center justify-center p-2 rounded-md text-blue-200 hover:text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
            on:click=move |_| set_show_mobile_menu.update(|show| *show = !*show)
        >
            <span class="sr-only">"Open main menu"</span>
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
            </svg>
        </button>
    }
}

/// Mobile navigation menu component
#[component]
pub fn MobileNav() -> impl IntoView {
    view! {
        <div class="md:hidden">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-blue-700">
                // Status - available to all authenticated users
                <A href="/" class="mobile-nav-link">
                    "Status"
                </A>

                // Processes - requires system.read permission
                <RequirePermission permission="system.read".to_string()>
                    <A href="/processes" class="mobile-nav-link">
                        "Processes"
                    </A>
                </RequirePermission>

                // System - requires system.read permission
                <RequirePermission permission="system.read".to_string()>
                    <A href="/system" class="mobile-nav-link">
                        "System"
                    </A>
                </RequirePermission>

                // Network - requires network.read permission
                <RequirePermission permission="network.read".to_string()>
                    <A href="/network" class="mobile-nav-link">
                        "Network"
                    </A>
                </RequirePermission>

                // Firewall - requires network.write permission
                <RequirePermission permission="network.write".to_string()>
                    <A href="/firewall" class="mobile-nav-link">
                        "Firewall"
                    </A>
                </RequirePermission>

                // Wireless - requires network.read permission
                <RequirePermission permission="network.read".to_string()>
                    <A href="/wireless" class="mobile-nav-link">
                        "Wireless"
                    </A>
                </RequirePermission>

                // Services - requires system.read permission
                <RequirePermission permission="system.read".to_string()>
                    <A href="/services" class="mobile-nav-link">
                        "Services"
                    </A>
                </RequirePermission>

                // Software/Packages - requires packages.read permission
                <RequirePermission permission="packages.read".to_string()>
                    <A href="/packages" class="mobile-nav-link">
                        "Software"
                    </A>
                </RequirePermission>

                // Administration - admin only
                <AdminOnly>
                    <A href="/administration" class="mobile-nav-link">
                        "Administration"
                    </A>
                </AdminOnly>
            </div>
        </div>
    }
}