use leptos::*;
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use crate::components::navigation::NavItem;

/// User permission level with hierarchical structure
#[derive(Debug, <PERSON>lone, PartialEq, PartialOrd, Serialize, Deserialize)]
pub enum PermissionLevel {
    Guest = 0,
    User = 1,
    Admin = 2,
    Root = 3,
}

impl PermissionLevel {
    /// Check if this permission level has access to a required level
    pub fn has_access(&self, required: &PermissionLevel) -> bool {
        self >= required
    }
    
    /// Get all permissions available at this level
    pub fn get_available_permissions(&self) -> Vec<String> {
        match self {
            PermissionLevel::Guest => vec![
                "system.read".to_string(),
                "network.read".to_string(),
                "status.read".to_string(),
            ],
            PermissionLevel::User => {
                let mut perms = Self::Guest.get_available_permissions();
                perms.extend(vec![
                    "network.basic_write".to_string(),
                    "system.basic_write".to_string(),
                    "services.read".to_string(),
                ]);
                perms
            }
            PermissionLevel::Admin => {
                let mut perms = Self::User.get_available_permissions();
                perms.extend(vec![
                    "network.write".to_string(),
                    "system.write".to_string(),
                    "services.write".to_string(),
                    "firewall.write".to_string(),
                    "packages.write".to_string(),
                    "users.write".to_string(),
                ]);
                perms
            }
            PermissionLevel::Root => {
                let mut perms = Self::Admin.get_available_permissions();
                perms.extend(vec![
                    "system.admin".to_string(),
                    "security.admin".to_string(),
                    "debug.access".to_string(),
                    "advanced.config".to_string(),
                ]);
                perms
            }
        }
    }
}

/// User context with permissions and features
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserContext {
    pub username: String,
    pub permission_level: PermissionLevel,
    pub custom_permissions: HashSet<String>,
    pub groups: Vec<String>,
    pub features_enabled: HashSet<String>,
    pub session_id: String,
    pub login_time: chrono::DateTime<chrono::Utc>,
}

impl UserContext {
    /// Check if user has a specific permission
    pub fn has_permission(&self, permission: &str) -> bool {
        // Check custom permissions first
        if self.custom_permissions.contains(permission) {
            return true;
        }
        
        // Check level-based permissions
        let available_perms = self.permission_level.get_available_permissions();
        available_perms.contains(&permission.to_string())
    }
    
    /// Check if user has access to a feature
    pub fn has_feature(&self, feature: &str) -> bool {
        self.features_enabled.contains(feature)
    }
    
    /// Check if user is in a specific group
    pub fn in_group(&self, group: &str) -> bool {
        self.groups.contains(&group.to_string())
    }
}

/// Menu item with permission requirements
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionAwareMenuItem {
    pub id: String,
    pub label: String,
    pub path: String,
    pub icon: Option<String>,
    pub description: Option<String>,
    pub required_permissions: Vec<String>,
    pub required_features: Vec<String>,
    pub required_groups: Vec<String>,
    pub min_permission_level: PermissionLevel,
    pub children: Vec<PermissionAwareMenuItem>,
    pub order: i32,
    pub category: String,
    pub visible_when_disabled: bool,
}

impl PermissionAwareMenuItem {
    /// Check if user can access this menu item
    pub fn can_access(&self, user: &UserContext) -> bool {
        // Check minimum permission level
        if !user.permission_level.has_access(&self.min_permission_level) {
            return false;
        }
        
        // Check required permissions
        for perm in &self.required_permissions {
            if !user.has_permission(perm) {
                return false;
            }
        }
        
        // Check required features
        for feature in &self.required_features {
            if !user.has_feature(feature) {
                return false;
            }
        }
        
        // Check required groups
        if !self.required_groups.is_empty() {
            let has_group = self.required_groups.iter().any(|group| user.in_group(group));
            if !has_group {
                return false;
            }
        }
        
        true
    }
    
    /// Convert to NavItem if user has access
    pub fn to_nav_item(&self, user: &UserContext) -> Option<NavItem> {
        if self.can_access(user) {
            Some(NavItem {
                label: self.label.clone(),
                path: self.path.clone(),
                icon: self.icon.clone(),
                description: self.description.clone(),
                children: self.children
                    .iter()
                    .filter_map(|child| child.to_nav_item(user))
                    .collect(),
                active: false,
                disabled: false,
                badge: None,
            })
        } else if self.visible_when_disabled {
            Some(NavItem {
                label: self.label.clone(),
                path: self.path.clone(),
                icon: self.icon.clone(),
                description: Some("Access restricted".to_string()),
                children: vec![],
                active: false,
                disabled: true,
                badge: Some("🔒".to_string()),
            })
        } else {
            None
        }
    }
}

/// Permission-based menu filter component
#[component]
pub fn PermissionBasedMenuFilter(
    menu_items: ReadSignal<Vec<PermissionAwareMenuItem>>,
    user_context: ReadSignal<UserContext>,
    #[prop(optional)] class: Option<String>,
    #[prop(optional)] on_menu_filtered: Option<Box<dyn Fn(Vec<NavItem>)>>,
) -> impl IntoView {
    let (filtered_menu, set_filtered_menu) = create_signal(Vec::<NavItem>::new());
    
    // Filter menu items based on user permissions
    create_effect(move |_| {
        let items = menu_items.get();
        let user = user_context.get();
        
        let filtered: Vec<NavItem> = items
            .iter()
            .filter_map(|item| item.to_nav_item(&user))
            .collect();
        
        set_filtered_menu.set(filtered.clone());
        
        if let Some(handler) = &on_menu_filtered {
            handler(filtered);
        }
    });
    
    view! {
        <div class=format!("permission-menu-filter {}", class.unwrap_or_default())>
            <PermissionMenuDisplay 
                menu_items=filtered_menu
                user_context=user_context
            />
        </div>
    }
}

/// Display filtered menu with permission indicators
#[component]
fn PermissionMenuDisplay(
    menu_items: ReadSignal<Vec<NavItem>>,
    user_context: ReadSignal<UserContext>,
) -> impl IntoView {
    view! {
        <div class="space-y-2">
            {move || {
                let items = menu_items.get();
                let user = user_context.get();
                
                if items.is_empty() {
                    view! {
                        <div class="text-center py-8 text-gray-500">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                            </svg>
                            <p class="mt-2">No accessible menu items</p>
                            <p class="text-sm text-gray-400">Contact administrator for access</p>
                        </div>
                    }.into_view()
                } else {
                    items.into_iter().map(|item| {
                        view! {
                            <PermissionMenuItem 
                                item=item.clone()
                                user_level=user.permission_level.clone()
                            />
                        }
                    }).collect::<Vec<_>>()
                }
            }}
        </div>
    }
}

/// Individual menu item with permission indicators
#[component]
fn PermissionMenuItem(
    item: NavItem,
    user_level: PermissionLevel,
) -> impl IntoView {
    let has_children = !item.children.is_empty();
    let (expanded, set_expanded) = create_signal(false);
    
    view! {
        <div class="menu-item">
            <div 
                class=move || format!(
                    "flex items-center justify-between p-3 rounded-lg transition-colors cursor-pointer {}",
                    if item.disabled {
                        "bg-gray-100 text-gray-400 cursor-not-allowed"
                    } else {
                        "bg-white hover:bg-gray-50 text-gray-900"
                    }
                )
                on:click=move |_| {
                    if has_children && !item.disabled {
                        set_expanded.update(|e| *e = !*e);
                    }
                }
            >
                <div class="flex items-center space-x-3">
                    {if let Some(icon) = &item.icon {
                        view! {
                            <span class="text-lg">{icon.clone()}</span>
                        }.into_view()
                    } else {
                        view! { <div></div> }.into_view()
                    }}
                    
                    <div class="flex-1">
                        <div class="flex items-center space-x-2">
                            <span class="font-medium">{item.label.clone()}</span>
                            {if let Some(badge) = &item.badge {
                                view! {
                                    <span class="text-sm">{badge.clone()}</span>
                                }.into_view()
                            } else {
                                view! { <div></div> }.into_view()
                            }}
                        </div>
                        {if let Some(desc) = &item.description {
                            view! {
                                <p class="text-sm text-gray-500 mt-1">{desc.clone()}</p>
                            }.into_view()
                        } else {
                            view! { <div></div> }.into_view()
                        }}
                    </div>
                </div>
                
                <div class="flex items-center space-x-2">
                    <PermissionLevelIndicator level=user_level.clone() />
                    
                    {if has_children {
                        view! {
                            <svg 
                                class=move || format!(
                                    "w-5 h-5 text-gray-400 transition-transform {}",
                                    if expanded.get() { "rotate-90" } else { "" }
                                )
                                fill="currentColor" 
                                viewBox="0 0 20 20"
                            >
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                            </svg>
                        }.into_view()
                    } else {
                        view! { <div></div> }.into_view()
                    }}
                </div>
            </div>
            
            {move || {
                if has_children && expanded.get() {
                    view! {
                        <div class="ml-6 mt-2 space-y-1 border-l-2 border-gray-200 pl-4">
                            {item.children.iter().map(|child| {
                                view! {
                                    <PermissionMenuItem 
                                        item=child.clone()
                                        user_level=user_level.clone()
                                    />
                                }
                            }).collect::<Vec<_>>()}
                        </div>
                    }.into_view()
                } else {
                    view! { <div></div> }.into_view()
                }
            }}
        </div>
    }
}

/// Permission level indicator component
#[component]
fn PermissionLevelIndicator(level: PermissionLevel) -> impl IntoView {
    let (color, label) = match level {
        PermissionLevel::Guest => ("bg-gray-100 text-gray-800", "Guest"),
        PermissionLevel::User => ("bg-blue-100 text-blue-800", "User"),
        PermissionLevel::Admin => ("bg-green-100 text-green-800", "Admin"),
        PermissionLevel::Root => ("bg-red-100 text-red-800", "Root"),
    };
    
    view! {
        <span class=format!("inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {}", color)>
            {label}
        </span>
    }
}

/// Get default menu configuration with permission requirements
pub fn get_default_permission_menu() -> Vec<PermissionAwareMenuItem> {
    vec![
        PermissionAwareMenuItem {
            id: "status".to_string(),
            label: "Status".to_string(),
            path: "/status".to_string(),
            icon: Some("📊".to_string()),
            description: Some("System status and overview".to_string()),
            required_permissions: vec!["status.read".to_string()],
            required_features: vec![],
            required_groups: vec![],
            min_permission_level: PermissionLevel::Guest,
            children: vec![
                PermissionAwareMenuItem {
                    id: "status-overview".to_string(),
                    label: "Overview".to_string(),
                    path: "/status/overview".to_string(),
                    icon: Some("🏠".to_string()),
                    description: Some("System overview".to_string()),
                    required_permissions: vec!["status.read".to_string()],
                    required_features: vec![],
                    required_groups: vec![],
                    min_permission_level: PermissionLevel::Guest,
                    children: vec![],
                    order: 1,
                    category: "status".to_string(),
                    visible_when_disabled: true,
                },
                PermissionAwareMenuItem {
                    id: "status-processes".to_string(),
                    label: "Processes".to_string(),
                    path: "/status/processes".to_string(),
                    icon: Some("⚙️".to_string()),
                    description: Some("Running processes".to_string()),
                    required_permissions: vec!["system.read".to_string()],
                    required_features: vec![],
                    required_groups: vec![],
                    min_permission_level: PermissionLevel::User,
                    children: vec![],
                    order: 2,
                    category: "status".to_string(),
                    visible_when_disabled: true,
                },
            ],
            order: 1,
            category: "main".to_string(),
            visible_when_disabled: true,
        },
        PermissionAwareMenuItem {
            id: "system".to_string(),
            label: "System".to_string(),
            path: "/system".to_string(),
            icon: Some("🖥️".to_string()),
            description: Some("System configuration".to_string()),
            required_permissions: vec!["system.read".to_string()],
            required_features: vec![],
            required_groups: vec![],
            min_permission_level: PermissionLevel::User,
            children: vec![
                PermissionAwareMenuItem {
                    id: "system-general".to_string(),
                    label: "General".to_string(),
                    path: "/system/general".to_string(),
                    icon: Some("⚙️".to_string()),
                    description: Some("General system settings".to_string()),
                    required_permissions: vec!["system.write".to_string()],
                    required_features: vec![],
                    required_groups: vec![],
                    min_permission_level: PermissionLevel::Admin,
                    children: vec![],
                    order: 1,
                    category: "system".to_string(),
                    visible_when_disabled: true,
                },
                PermissionAwareMenuItem {
                    id: "system-users".to_string(),
                    label: "User Management".to_string(),
                    path: "/system/users".to_string(),
                    icon: Some("👥".to_string()),
                    description: Some("Manage system users".to_string()),
                    required_permissions: vec!["users.write".to_string()],
                    required_features: vec![],
                    required_groups: vec![],
                    min_permission_level: PermissionLevel::Admin,
                    children: vec![],
                    order: 2,
                    category: "system".to_string(),
                    visible_when_disabled: true,
                },
                PermissionAwareMenuItem {
                    id: "system-advanced".to_string(),
                    label: "Advanced Settings".to_string(),
                    path: "/system/advanced".to_string(),
                    icon: Some("🔧".to_string()),
                    description: Some("Advanced system configuration".to_string()),
                    required_permissions: vec!["advanced.config".to_string()],
                    required_features: vec![],
                    required_groups: vec![],
                    min_permission_level: PermissionLevel::Root,
                    children: vec![],
                    order: 3,
                    category: "system".to_string(),
                    visible_when_disabled: true,
                },
            ],
            order: 2,
            category: "main".to_string(),
            visible_when_disabled: true,
        },
        PermissionAwareMenuItem {
            id: "network".to_string(),
            label: "Network".to_string(),
            path: "/network".to_string(),
            icon: Some("🌐".to_string()),
            description: Some("Network configuration".to_string()),
            required_permissions: vec!["network.read".to_string()],
            required_features: vec![],
            required_groups: vec![],
            min_permission_level: PermissionLevel::User,
            children: vec![
                PermissionAwareMenuItem {
                    id: "network-interfaces".to_string(),
                    label: "Interfaces".to_string(),
                    path: "/network/interfaces".to_string(),
                    icon: Some("🔌".to_string()),
                    description: Some("Network interfaces".to_string()),
                    required_permissions: vec!["network.write".to_string()],
                    required_features: vec![],
                    required_groups: vec![],
                    min_permission_level: PermissionLevel::Admin,
                    children: vec![],
                    order: 1,
                    category: "network".to_string(),
                    visible_when_disabled: true,
                },
                PermissionAwareMenuItem {
                    id: "network-wireless".to_string(),
                    label: "Wireless".to_string(),
                    path: "/network/wireless".to_string(),
                    icon: Some("📶".to_string()),
                    description: Some("Wireless configuration".to_string()),
                    required_permissions: vec!["network.write".to_string()],
                    required_features: vec!["wireless".to_string()],
                    required_groups: vec![],
                    min_permission_level: PermissionLevel::Admin,
                    children: vec![],
                    order: 2,
                    category: "network".to_string(),
                    visible_when_disabled: true,
                },
                PermissionAwareMenuItem {
                    id: "network-firewall".to_string(),
                    label: "Firewall".to_string(),
                    path: "/network/firewall".to_string(),
                    icon: Some("🛡️".to_string()),
                    description: Some("Firewall configuration".to_string()),
                    required_permissions: vec!["firewall.write".to_string()],
                    required_features: vec![],
                    required_groups: vec![],
                    min_permission_level: PermissionLevel::Admin,
                    children: vec![],
                    order: 3,
                    category: "network".to_string(),
                    visible_when_disabled: true,
                },
            ],
            order: 3,
            category: "main".to_string(),
            visible_when_disabled: true,
        },
        PermissionAwareMenuItem {
            id: "services".to_string(),
            label: "Services".to_string(),
            path: "/services".to_string(),
            icon: Some("🔧".to_string()),
            description: Some("System services".to_string()),
            required_permissions: vec!["services.read".to_string()],
            required_features: vec![],
            required_groups: vec![],
            min_permission_level: PermissionLevel::User,
            children: vec![
                PermissionAwareMenuItem {
                    id: "services-vpn".to_string(),
                    label: "VPN".to_string(),
                    path: "/services/vpn".to_string(),
                    icon: Some("🔐".to_string()),
                    description: Some("VPN configuration".to_string()),
                    required_permissions: vec!["services.write".to_string()],
                    required_features: vec!["vpn".to_string()],
                    required_groups: vec![],
                    min_permission_level: PermissionLevel::Admin,
                    children: vec![],
                    order: 1,
                    category: "services".to_string(),
                    visible_when_disabled: true,
                },
            ],
            order: 4,
            category: "main".to_string(),
            visible_when_disabled: true,
        },
    ]
}

/// Mock user context for testing
pub fn get_mock_user_context(level: PermissionLevel) -> UserContext {
    let mut features = HashSet::new();
    features.insert("wireless".to_string());
    features.insert("vpn".to_string());

    UserContext {
        username: format!("test_{:?}", level).to_lowercase(),
        permission_level: level,
        custom_permissions: HashSet::new(),
        groups: vec!["users".to_string()],
        features_enabled: features,
        session_id: "test_session".to_string(),
        login_time: chrono::Utc::now(),
    }
}

/// Permission menu manager component
#[component]
pub fn PermissionMenuManager(
    #[prop(optional)] class: Option<String>,
    #[prop(optional)] user_level: Option<PermissionLevel>,
) -> impl IntoView {
    let current_level = user_level.unwrap_or(PermissionLevel::User);
    let (menu_items, set_menu_items) = create_signal(get_default_permission_menu());
    let (user_context, set_user_context) = create_signal(get_mock_user_context(current_level.clone()));
    let (filtered_items, set_filtered_items) = create_signal(Vec::<NavItem>::new());

    view! {
        <div class=format!("permission-menu-manager {}", class.unwrap_or_default())>
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-lg font-semibold text-gray-900">Menu Access Control</h2>
                            <p class="mt-1 text-sm text-gray-600">Menu items filtered by user permissions</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm text-gray-500">Current Level:</span>
                            <PermissionLevelIndicator level=current_level />
                        </div>
                    </div>
                </div>

                <div class="p-6">
                    <PermissionBasedMenuFilter
                        menu_items=menu_items
                        user_context=user_context
                        on_menu_filtered=Box::new(move |items| set_filtered_items.set(items))
                    />
                </div>

                <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                    <div class="text-sm text-gray-600">
                        <p>
                            <strong>{move || filtered_items.get().len()}</strong>
                            {" menu items accessible at current permission level"}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    }
}
