use leptos::*;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Security application types
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum SecurityAppType {
    IntrusionDetection,
    AccessControl,
    SecurityMonitoring,
    VulnerabilityScanner,
    LogAnalyzer,
    ThreatIntelligence,
    NetworkScanner,
    PortScanner,
    BruteForceProtection,
    DDoSProtection,
}

impl SecurityAppType {
    pub fn display_name(&self) -> &'static str {
        match self {
            SecurityAppType::IntrusionDetection => "Intrusion Detection System",
            SecurityAppType::AccessControl => "Access Control Manager",
            SecurityAppType::SecurityMonitoring => "Security Monitoring Dashboard",
            SecurityAppType::VulnerabilityScanner => "Vulnerability Scanner",
            SecurityAppType::LogAnalyzer => "Security Log Analyzer",
            SecurityAppType::ThreatIntelligence => "Threat Intelligence Feed",
            SecurityAppType::NetworkScanner => "Network Security Scanner",
            SecurityAppType::PortScanner => "Port Scanner",
            SecurityAppType::BruteForceProtection => "Brute Force Protection",
            SecurityAppType::DDoSProtection => "DDoS Protection",
        }
    }
    
    pub fn icon(&self) -> &'static str {
        match self {
            SecurityAppType::IntrusionDetection => "🛡️",
            SecurityAppType::AccessControl => "🔐",
            SecurityAppType::SecurityMonitoring => "👁️",
            SecurityAppType::VulnerabilityScanner => "🔍",
            SecurityAppType::LogAnalyzer => "📊",
            SecurityAppType::ThreatIntelligence => "🧠",
            SecurityAppType::NetworkScanner => "🌐",
            SecurityAppType::PortScanner => "🔌",
            SecurityAppType::BruteForceProtection => "🚫",
            SecurityAppType::DDoSProtection => "⚡",
        }
    }
    
    pub fn description(&self) -> &'static str {
        match self {
            SecurityAppType::IntrusionDetection => "Monitor and detect unauthorized access attempts",
            SecurityAppType::AccessControl => "Manage user access and permissions",
            SecurityAppType::SecurityMonitoring => "Real-time security status monitoring",
            SecurityAppType::VulnerabilityScanner => "Scan for security vulnerabilities",
            SecurityAppType::LogAnalyzer => "Analyze security logs for threats",
            SecurityAppType::ThreatIntelligence => "Threat intelligence and indicators",
            SecurityAppType::NetworkScanner => "Scan network for security issues",
            SecurityAppType::PortScanner => "Scan for open ports and services",
            SecurityAppType::BruteForceProtection => "Protect against brute force attacks",
            SecurityAppType::DDoSProtection => "Protect against DDoS attacks",
        }
    }
}

/// Security threat level
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ThreatLevel {
    Low,
    Medium,
    High,
    Critical,
}

impl ThreatLevel {
    pub fn display_name(&self) -> &'static str {
        match self {
            ThreatLevel::Low => "Low",
            ThreatLevel::Medium => "Medium",
            ThreatLevel::High => "High",
            ThreatLevel::Critical => "Critical",
        }
    }
    
    pub fn css_class(&self) -> &'static str {
        match self {
            ThreatLevel::Low => "text-green-600 bg-green-100",
            ThreatLevel::Medium => "text-yellow-600 bg-yellow-100",
            ThreatLevel::High => "text-orange-600 bg-orange-100",
            ThreatLevel::Critical => "text-red-600 bg-red-100",
        }
    }
    
    pub fn icon(&self) -> &'static str {
        match self {
            ThreatLevel::Low => "🟢",
            ThreatLevel::Medium => "🟡",
            ThreatLevel::High => "🟠",
            ThreatLevel::Critical => "🔴",
        }
    }
}

/// Security event
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityEvent {
    pub id: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub event_type: String,
    pub threat_level: ThreatLevel,
    pub source_ip: String,
    pub target_ip: String,
    pub description: String,
    pub details: HashMap<String, String>,
    pub resolved: bool,
    pub false_positive: bool,
}

/// Security application
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityApp {
    pub id: String,
    pub name: String,
    pub app_type: SecurityAppType,
    pub enabled: bool,
    pub running: bool,
    pub config: HashMap<String, String>,
    pub last_scan: Option<chrono::DateTime<chrono::Utc>>,
    pub events_count: u32,
    pub threats_detected: u32,
    pub false_positives: u32,
}

impl SecurityApp {
    pub fn new(app_type: SecurityAppType) -> Self {
        Self {
            id: format!("{:?}", app_type).to_lowercase().replace("_", "-"),
            name: app_type.display_name().to_string(),
            app_type,
            enabled: false,
            running: false,
            config: HashMap::new(),
            last_scan: None,
            events_count: 0,
            threats_detected: 0,
            false_positives: 0,
        }
    }
}

/// Security applications manager component
#[component]
pub fn SecurityAppsManager(
    #[prop(optional)] class: Option<String>,
) -> impl IntoView {
    let (apps, set_apps) = create_signal(get_default_security_apps());
    let (events, set_events) = create_signal(get_mock_security_events());
    let (selected_app, set_selected_app) = create_signal(None::<String>);
    let (loading, set_loading) = create_signal(false);
    let (error, set_error) = create_signal(None::<String>);
    
    // Load security apps on mount
    create_effect(move |_| {
        spawn_local(async move {
            set_loading.set(true);
            set_error.set(None);
            
            match load_security_apps().await {
                Ok(loaded_apps) => {
                    set_apps.set(loaded_apps);
                }
                Err(e) => {
                    set_error.set(Some(e));
                }
            }
            
            set_loading.set(false);
        });
    });
    
    view! {
        <div class=format!("security-apps-manager {}", class.unwrap_or_default())>
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-lg font-semibold text-gray-900">Security Applications</h2>
                            <p class="mt-1 text-sm text-gray-600">Manage security monitoring and protection systems</p>
                        </div>
                        <div class="flex space-x-2">
                            <button 
                                class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
                                on:click=move |_| {
                                    spawn_local(async move {
                                        if let Ok(refreshed) = load_security_apps().await {
                                            set_apps.set(refreshed);
                                        }
                                    });
                                }
                            >
                                "🔄 Refresh"
                            </button>
                            <button class="bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 transition-colors">
                                "🚨 Security Scan"
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="p-6">
                    {move || {
                        if loading.get() {
                            view! {
                                <div class="flex items-center justify-center py-8">
                                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
                                    <span class="ml-3 text-gray-600">Loading security applications...</span>
                                </div>
                            }.into_view()
                        } else if let Some(err) = error.get() {
                            view! {
                                <div class="bg-red-50 border border-red-200 rounded-md p-4">
                                    <div class="flex">
                                        <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                                        </svg>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-red-800">Security Apps Loading Error</h3>
                                            <p class="mt-1 text-sm text-red-700">{err}</p>
                                        </div>
                                    </div>
                                </div>
                            }.into_view()
                        } else {
                            view! {
                                <div class="space-y-6">
                                    // Security overview
                                    <SecurityOverview apps=apps events=events />
                                    
                                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                        // Security apps list
                                        <div>
                                            <SecurityAppsList 
                                                apps=apps
                                                selected_app=selected_app
                                                on_select=move |id: String| set_selected_app.set(Some(id))
                                                on_app_action=move |action: SecurityAppAction| {
                                                    spawn_local(async move {
                                                        if let Ok(updated) = handle_security_app_action(action).await {
                                                            set_apps.set(updated);
                                                        }
                                                    });
                                                }
                                            />
                                        </div>
                                        
                                        // Recent security events
                                        <div>
                                            <SecurityEventsList events=events />
                                        </div>
                                    </div>
                                </div>
                            }.into_view()
                        }
                    }}
                </div>
            </div>
        </div>
    }
}

/// Security app action types
#[derive(Debug, Clone)]
pub enum SecurityAppAction {
    Enable(String),
    Disable(String),
    Start(String),
    Stop(String),
    Scan(String),
    Configure(String, HashMap<String, String>),
}

/// Security overview component
#[component]
fn SecurityOverview(
    apps: ReadSignal<Vec<SecurityApp>>,
    events: ReadSignal<Vec<SecurityEvent>>,
) -> impl IntoView {
    view! {
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {move || {
                let app_list = apps.get();
                let event_list = events.get();

                let active_apps = app_list.iter().filter(|app| app.running).count();
                let total_threats = app_list.iter().map(|app| app.threats_detected).sum::<u32>();
                let critical_events = event_list.iter().filter(|e| e.threat_level == ThreatLevel::Critical).count();
                let unresolved_events = event_list.iter().filter(|e| !e.resolved).count();

                view! {
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-blue-600">Active Apps</p>
                                <p class="text-2xl font-semibold text-blue-900">{active_apps}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-yellow-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-yellow-600">Total Threats</p>
                                <p class="text-2xl font-semibold text-yellow-900">{total_threats}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-red-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-red-600">Critical Events</p>
                                <p class="text-2xl font-semibold text-red-900">{critical_events}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-gray-600">Unresolved</p>
                                <p class="text-2xl font-semibold text-gray-900">{unresolved_events}</p>
                            </div>
                        </div>
                    </div>
                }
            }}
        </div>
    }
}

/// Security apps list component
#[component]
fn SecurityAppsList(
    apps: ReadSignal<Vec<SecurityApp>>,
    selected_app: ReadSignal<Option<String>>,
    on_select: impl Fn(String) + 'static + Copy,
    on_app_action: impl Fn(SecurityAppAction) + 'static + Copy,
) -> impl IntoView {
    view! {
        <div class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900">Security Applications</h3>

            <div class="space-y-2">
                {move || {
                    let app_list = apps.get();

                    if app_list.is_empty() {
                        view! {
                            <div class="text-center py-8 text-gray-500">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                                </svg>
                                <p class="mt-2">No security applications configured</p>
                            </div>
                        }.into_view()
                    } else {
                        app_list.into_iter().map(|app| {
                            let app_id = app.id.clone();
                            let is_selected = selected_app.get().as_ref() == Some(&app.id);

                            view! {
                                <SecurityAppCard
                                    app=app
                                    is_selected=is_selected
                                    on_select=move || on_select(app_id.clone())
                                    on_action=on_app_action
                                />
                            }
                        }).collect_view()
                    }
                }}
            </div>
        </div>
    }
}

/// Security app card component
#[component]
fn SecurityAppCard(
    app: SecurityApp,
    is_selected: bool,
    on_select: impl Fn() + 'static,
    on_action: impl Fn(SecurityAppAction) + 'static,
) -> impl IntoView {
    let app_id = app.id.clone();
    let on_action = std::rc::Rc::new(on_action);

    view! {
        <div
            class=move || format!(
                "border rounded-lg p-4 cursor-pointer transition-colors {}",
                if is_selected {
                    "border-red-500 bg-red-50"
                } else {
                    "border-gray-200 hover:border-gray-300 bg-white"
                }
            )
            on:click=move |_| on_select()
        >
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <span class="text-2xl">{app.app_type.icon()}</span>
                    <div>
                        <h4 class="font-medium text-gray-900">{app.name.clone()}</h4>
                        <p class="text-sm text-gray-500">{app.app_type.description()}</p>
                    </div>
                </div>

                <div class="flex items-center space-x-2">
                    <div class="flex items-center space-x-1">
                        {if app.enabled {
                            view! {
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-green-600 bg-green-100">
                                    "✓ Enabled"
                                </span>
                            }.into_view()
                        } else {
                            view! {
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-gray-600 bg-gray-100">
                                    "○ Disabled"
                                </span>
                            }.into_view()
                        }}

                        {if app.running {
                            view! {
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-blue-600 bg-blue-100">
                                    "▶️ Running"
                                </span>
                            }.into_view()
                        } else {
                            view! {
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-gray-600 bg-gray-100">
                                    "⏹️ Stopped"
                                </span>
                            }.into_view()
                        }}
                    </div>

                    <div class="flex space-x-1">
                        {if !app.enabled {
                            let app_id = app_id.clone();
                            view! {
                                <button
                                    class="p-1 text-green-600 hover:bg-green-100 rounded"
                                    title="Enable application"
                                    on:click={
                                        let app_id = app_id.clone();
                                        let on_action = on_action.clone();
                                        move |e| {
                                            e.stop_propagation();
                                            on_action(SecurityAppAction::Enable(app_id.clone()));
                                        }
                                    }
                                >
                                    "✅"
                                </button>
                            }.into_view()
                        } else {
                            let app_id = app_id.clone();
                            view! {
                                <button
                                    class="p-1 text-gray-600 hover:bg-gray-100 rounded"
                                    title="Disable application"
                                    on:click=move |e| {
                                        e.stop_propagation();
                                        on_action(SecurityAppAction::Disable(app_id.clone()));
                                    }
                                >
                                    "❌"
                                </button>
                            }.into_view()
                        }}

                        <button
                            class="p-1 text-red-600 hover:bg-red-100 rounded"
                            title="Run security scan"
                            on:click={
                                let app_id = app_id.clone();
                                let on_action = on_action.clone();
                                move |e| {
                                    e.stop_propagation();
                                    on_action(SecurityAppAction::Scan(app_id.clone()));
                                }
                            }
                        >
                            "🔍"
                        </button>
                    </div>
                </div>
            </div>

            <div class="mt-3 grid grid-cols-3 gap-4 text-sm text-gray-500">
                <div>
                    <span class="font-medium">Events:</span> {app.events_count}
                </div>
                <div>
                    <span class="font-medium">Threats:</span> {app.threats_detected}
                </div>
                <div>
                    <span class="font-medium">False Positives:</span> {app.false_positives}
                </div>
            </div>

            {if let Some(last_scan) = app.last_scan {
                view! {
                    <div class="mt-2 text-xs text-gray-400">
                        Last scan: {last_scan.format("%Y-%m-%d %H:%M:%S UTC").to_string()}
                    </div>
                }.into_view()
            } else {
                view! {
                    <div class="mt-2 text-xs text-gray-400">
                        Never scanned
                    </div>
                }.into_view()
            }}
        </div>
    }
}

/// Security events list component
#[component]
fn SecurityEventsList(
    events: ReadSignal<Vec<SecurityEvent>>,
) -> impl IntoView {
    view! {
        <div class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900">Recent Security Events</h3>

            <div class="space-y-2 max-h-96 overflow-y-auto">
                {move || {
                    let event_list = events.get();

                    if event_list.is_empty() {
                        view! {
                            <div class="text-center py-8 text-gray-500">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <p class="mt-2">No security events detected</p>
                            </div>
                        }.into_view()
                    } else {
                        event_list.into_iter().take(10).map(|event| {
                            view! {
                                <SecurityEventCard event=event />
                            }
                        }).collect_view()
                    }
                }}
            </div>
        </div>
    }
}

/// Security event card component
#[component]
fn SecurityEventCard(
    event: SecurityEvent,
) -> impl IntoView {
    view! {
        <div class="border rounded-lg p-3 bg-white hover:bg-gray-50 transition-colors">
            <div class="flex items-start justify-between">
                <div class="flex items-start space-x-3">
                    <span class="text-lg">{event.threat_level.icon()}</span>
                    <div class="flex-1">
                        <div class="flex items-center space-x-2">
                            <h4 class="font-medium text-gray-900">{event.event_type.clone()}</h4>
                            <span class=format!(
                                "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {}",
                                event.threat_level.css_class()
                            )>
                                {event.threat_level.display_name()}
                            </span>
                            {if event.resolved {
                                view! {
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-green-600 bg-green-100">
                                        "✓ Resolved"
                                    </span>
                                }.into_view()
                            } else {
                                view! {
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-yellow-600 bg-yellow-100">
                                        "⏳ Pending"
                                    </span>
                                }.into_view()
                            }}
                        </div>
                        <p class="text-sm text-gray-600 mt-1">{event.description.clone()}</p>
                        <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                            <span>From: {event.source_ip.clone()}</span>
                            <span>To: {event.target_ip.clone()}</span>
                            <span>{event.timestamp.format("%H:%M:%S").to_string()}</span>
                        </div>
                    </div>
                </div>

                <div class="flex space-x-1">
                    <button class="p-1 text-blue-600 hover:bg-blue-100 rounded" title="View details">
                        "👁️"
                    </button>
                    {if !event.resolved {
                        view! {
                            <button class="p-1 text-green-600 hover:bg-green-100 rounded" title="Mark resolved">
                                "✅"
                            </button>
                        }.into_view()
                    } else {
                        view! { <div></div> }.into_view()
                    }}
                    <button class="p-1 text-yellow-600 hover:bg-yellow-100 rounded" title="Mark false positive">
                        "⚠️"
                    </button>
                </div>
            </div>
        </div>
    }
}

/// Get default security applications
fn get_default_security_apps() -> Vec<SecurityApp> {
    let mut apps = vec![
        SecurityApp::new(SecurityAppType::IntrusionDetection),
        SecurityApp::new(SecurityAppType::AccessControl),
        SecurityApp::new(SecurityAppType::SecurityMonitoring),
        SecurityApp::new(SecurityAppType::VulnerabilityScanner),
        SecurityApp::new(SecurityAppType::LogAnalyzer),
        SecurityApp::new(SecurityAppType::ThreatIntelligence),
        SecurityApp::new(SecurityAppType::NetworkScanner),
        SecurityApp::new(SecurityAppType::PortScanner),
        SecurityApp::new(SecurityAppType::BruteForceProtection),
        SecurityApp::new(SecurityAppType::DDoSProtection),
    ];

    // Configure some apps with realistic settings
    if let Some(ids) = apps.iter_mut().find(|app| app.app_type == SecurityAppType::IntrusionDetection) {
        ids.enabled = true;
        ids.running = true;
        ids.events_count = 156;
        ids.threats_detected = 12;
        ids.false_positives = 3;
        ids.last_scan = Some(chrono::Utc::now() - chrono::Duration::minutes(30));
        ids.config.insert("sensitivity".to_string(), "medium".to_string());
        ids.config.insert("log_level".to_string(), "info".to_string());
    }

    if let Some(ac) = apps.iter_mut().find(|app| app.app_type == SecurityAppType::AccessControl) {
        ac.enabled = true;
        ac.running = true;
        ac.events_count = 89;
        ac.threats_detected = 5;
        ac.false_positives = 1;
        ac.last_scan = Some(chrono::Utc::now() - chrono::Duration::hours(1));
        ac.config.insert("max_attempts".to_string(), "3".to_string());
        ac.config.insert("lockout_duration".to_string(), "300".to_string());
    }

    if let Some(sm) = apps.iter_mut().find(|app| app.app_type == SecurityAppType::SecurityMonitoring) {
        sm.enabled = true;
        sm.running = true;
        sm.events_count = 234;
        sm.threats_detected = 8;
        sm.false_positives = 2;
        sm.last_scan = Some(chrono::Utc::now() - chrono::Duration::minutes(15));
        sm.config.insert("refresh_interval".to_string(), "60".to_string());
        sm.config.insert("alert_threshold".to_string(), "high".to_string());
    }

    apps
}

/// Get mock security events
fn get_mock_security_events() -> Vec<SecurityEvent> {
    vec![
        SecurityEvent {
            id: "evt-001".to_string(),
            timestamp: chrono::Utc::now() - chrono::Duration::minutes(5),
            event_type: "Brute Force Attack".to_string(),
            threat_level: ThreatLevel::High,
            source_ip: "*************".to_string(),
            target_ip: "***********".to_string(),
            description: "Multiple failed SSH login attempts detected".to_string(),
            details: HashMap::from([
                ("attempts".to_string(), "15".to_string()),
                ("username".to_string(), "admin".to_string()),
                ("protocol".to_string(), "SSH".to_string()),
            ]),
            resolved: false,
            false_positive: false,
        },
        SecurityEvent {
            id: "evt-002".to_string(),
            timestamp: chrono::Utc::now() - chrono::Duration::minutes(12),
            event_type: "Port Scan".to_string(),
            threat_level: ThreatLevel::Medium,
            source_ip: "*********".to_string(),
            target_ip: "***********".to_string(),
            description: "Port scanning activity detected".to_string(),
            details: HashMap::from([
                ("ports_scanned".to_string(), "22,80,443,8080".to_string()),
                ("scan_type".to_string(), "TCP SYN".to_string()),
            ]),
            resolved: true,
            false_positive: false,
        },
        SecurityEvent {
            id: "evt-003".to_string(),
            timestamp: chrono::Utc::now() - chrono::Duration::minutes(25),
            event_type: "Suspicious Traffic".to_string(),
            threat_level: ThreatLevel::Low,
            source_ip: "***********".to_string(),
            target_ip: "*******".to_string(),
            description: "Unusual DNS query pattern detected".to_string(),
            details: HashMap::from([
                ("queries_per_minute".to_string(), "150".to_string()),
                ("query_type".to_string(), "A".to_string()),
            ]),
            resolved: false,
            false_positive: true,
        },
    ]
}

/// Load security applications from system (mock implementation)
async fn load_security_apps() -> Result<Vec<SecurityApp>, String> {
    // Simulate network delay
    gloo_timers::future::TimeoutFuture::new(800).await;

    // In a real implementation, this would query the security system
    // For now, return mock data
    Ok(get_default_security_apps())
}

/// Handle security app actions (mock implementation)
async fn handle_security_app_action(action: SecurityAppAction) -> Result<Vec<SecurityApp>, String> {
    // Simulate processing delay
    gloo_timers::future::TimeoutFuture::new(1200).await;

    match action {
        SecurityAppAction::Enable(app_id) => {
            log::info!("Enabling security app: {}", app_id);
            // In real implementation, would enable the security application
        }
        SecurityAppAction::Disable(app_id) => {
            log::info!("Disabling security app: {}", app_id);
            // In real implementation, would disable the security application
        }
        SecurityAppAction::Start(app_id) => {
            log::info!("Starting security app: {}", app_id);
            // In real implementation, would start the security service
        }
        SecurityAppAction::Stop(app_id) => {
            log::info!("Stopping security app: {}", app_id);
            // In real implementation, would stop the security service
        }
        SecurityAppAction::Scan(app_id) => {
            log::info!("Running security scan with app: {}", app_id);
            // In real implementation, would trigger a security scan
        }
        SecurityAppAction::Configure(app_id, config) => {
            log::info!("Configuring security app: {} with {:?}", app_id, config);
            // In real implementation, would update app configuration
        }
    }

    // Return updated apps list
    load_security_apps().await
}
