use leptos::*;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// VPN application types
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum VpnAppType {
    OpenVpn,
    WireGuard,
    IpsecVpn,
    L2tpVpn,
    PptpVpn,
    SslVpn,
    SiteToSite,
    ClientToSite,
    VpnLoadBalancer,
    VpnMonitoring,
    VpnFirewall,
    VpnAnalytics,
}

impl VpnAppType {
    pub fn display_name(&self) -> &'static str {
        match self {
            VpnAppType::OpenVpn => "OpenVPN",
            VpnAppType::WireGuard => "WireGuard",
            VpnAppType::IpsecVpn => "IPsec VPN",
            VpnAppType::L2tpVpn => "L2TP VPN",
            VpnAppType::PptpVpn => "PPTP VPN",
            VpnAppType::SslVpn => "SSL VPN",
            VpnAppType::SiteToSite => "Site-to-Site VPN",
            VpnAppType::ClientToSite => "Client-to-Site VPN",
            VpnAppType::VpnLoadBalancer => "VPN Load Balancer",
            VpnAppType::VpnMonitoring => "VPN Monitoring",
            VpnAppType::VpnFirewall => "VPN Firewall",
            VpnAppType::VpnAnalytics => "VPN Analytics",
        }
    }
    
    pub fn icon(&self) -> &'static str {
        match self {
            VpnAppType::OpenVpn => "🔐",
            VpnAppType::WireGuard => "⚡",
            VpnAppType::IpsecVpn => "🛡️",
            VpnAppType::L2tpVpn => "🔒",
            VpnAppType::PptpVpn => "🔑",
            VpnAppType::SslVpn => "🌐",
            VpnAppType::SiteToSite => "🏢",
            VpnAppType::ClientToSite => "👤",
            VpnAppType::VpnLoadBalancer => "⚖️",
            VpnAppType::VpnMonitoring => "📊",
            VpnAppType::VpnFirewall => "🚫",
            VpnAppType::VpnAnalytics => "📈",
        }
    }
    
    pub fn description(&self) -> &'static str {
        match self {
            VpnAppType::OpenVpn => "Traditional OpenVPN server with SSL/TLS encryption",
            VpnAppType::WireGuard => "Modern, fast, and secure VPN protocol",
            VpnAppType::IpsecVpn => "IPsec-based VPN for enterprise security",
            VpnAppType::L2tpVpn => "Layer 2 Tunneling Protocol VPN",
            VpnAppType::PptpVpn => "Point-to-Point Tunneling Protocol VPN",
            VpnAppType::SslVpn => "SSL-based VPN for web browser access",
            VpnAppType::SiteToSite => "Connect multiple network sites securely",
            VpnAppType::ClientToSite => "Remote client access to local network",
            VpnAppType::VpnLoadBalancer => "Distribute VPN connections across servers",
            VpnAppType::VpnMonitoring => "Monitor VPN connections and performance",
            VpnAppType::VpnFirewall => "Firewall rules specific to VPN traffic",
            VpnAppType::VpnAnalytics => "Analyze VPN usage patterns and statistics",
        }
    }
    
    pub fn protocol(&self) -> &'static str {
        match self {
            VpnAppType::OpenVpn => "OpenVPN",
            VpnAppType::WireGuard => "WireGuard",
            VpnAppType::IpsecVpn => "IPsec",
            VpnAppType::L2tpVpn => "L2TP/IPsec",
            VpnAppType::PptpVpn => "PPTP",
            VpnAppType::SslVpn => "SSL/TLS",
            VpnAppType::SiteToSite => "Various",
            VpnAppType::ClientToSite => "Various",
            VpnAppType::VpnLoadBalancer => "N/A",
            VpnAppType::VpnMonitoring => "N/A",
            VpnAppType::VpnFirewall => "N/A",
            VpnAppType::VpnAnalytics => "N/A",
        }
    }
}

/// VPN connection status
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum VpnConnectionStatus {
    Connected,
    Connecting,
    Disconnected,
    Error,
    Authenticating,
}

impl VpnConnectionStatus {
    pub fn display_name(&self) -> &'static str {
        match self {
            VpnConnectionStatus::Connected => "Connected",
            VpnConnectionStatus::Connecting => "Connecting",
            VpnConnectionStatus::Disconnected => "Disconnected",
            VpnConnectionStatus::Error => "Error",
            VpnConnectionStatus::Authenticating => "Authenticating",
        }
    }
    
    pub fn css_class(&self) -> &'static str {
        match self {
            VpnConnectionStatus::Connected => "text-green-600 bg-green-100",
            VpnConnectionStatus::Connecting => "text-yellow-600 bg-yellow-100",
            VpnConnectionStatus::Disconnected => "text-gray-600 bg-gray-100",
            VpnConnectionStatus::Error => "text-red-600 bg-red-100",
            VpnConnectionStatus::Authenticating => "text-blue-600 bg-blue-100",
        }
    }
    
    pub fn icon(&self) -> &'static str {
        match self {
            VpnConnectionStatus::Connected => "✅",
            VpnConnectionStatus::Connecting => "🔄",
            VpnConnectionStatus::Disconnected => "❌",
            VpnConnectionStatus::Error => "⚠️",
            VpnConnectionStatus::Authenticating => "🔐",
        }
    }
}

/// VPN client connection
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VpnConnection {
    pub id: String,
    pub client_name: String,
    pub client_ip: String,
    pub virtual_ip: String,
    pub status: VpnConnectionStatus,
    pub connected_at: Option<chrono::DateTime<chrono::Utc>>,
    pub bytes_sent: u64,
    pub bytes_received: u64,
    pub duration: Option<std::time::Duration>,
    pub protocol: String,
    pub encryption: String,
    pub last_activity: chrono::DateTime<chrono::Utc>,
}

/// VPN application
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VpnApp {
    pub id: String,
    pub name: String,
    pub app_type: VpnAppType,
    pub enabled: bool,
    pub running: bool,
    pub config: HashMap<String, String>,
    pub connections: Vec<VpnConnection>,
    pub listen_port: u16,
    pub max_clients: u32,
    pub active_clients: u32,
    pub total_bytes_sent: u64,
    pub total_bytes_received: u64,
    pub uptime: Option<std::time::Duration>,
    pub last_restart: Option<chrono::DateTime<chrono::Utc>>,
    pub certificate_expiry: Option<chrono::DateTime<chrono::Utc>>,
}

impl VpnApp {
    pub fn new(app_type: VpnAppType) -> Self {
        let default_port = match app_type {
            VpnAppType::OpenVpn => 1194,
            VpnAppType::WireGuard => 51820,
            VpnAppType::IpsecVpn => 500,
            VpnAppType::L2tpVpn => 1701,
            VpnAppType::PptpVpn => 1723,
            VpnAppType::SslVpn => 443,
            _ => 0,
        };
        
        Self {
            id: format!("{:?}", app_type).to_lowercase().replace("_", "-"),
            name: app_type.display_name().to_string(),
            app_type,
            enabled: false,
            running: false,
            config: HashMap::new(),
            connections: Vec::new(),
            listen_port: default_port,
            max_clients: 10,
            active_clients: 0,
            total_bytes_sent: 0,
            total_bytes_received: 0,
            uptime: None,
            last_restart: None,
            certificate_expiry: None,
        }
    }
}

/// VPN statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VpnStats {
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub total_connections: u32,
    pub active_connections: u32,
    pub bytes_per_second_in: f64,
    pub bytes_per_second_out: f64,
    pub connection_success_rate: f64,
    pub average_connection_duration: f64,
    pub top_clients: Vec<(String, u64)>,
    pub protocol_distribution: HashMap<String, u32>,
}

/// VPN applications manager component
#[component]
pub fn VpnAppsManager(
    #[prop(optional)] class: Option<String>,
) -> impl IntoView {
    let (apps, set_apps) = create_signal(get_default_vpn_apps());
    let (stats, set_stats) = create_signal(get_mock_vpn_stats());
    let (selected_app, set_selected_app) = create_signal(None::<String>);
    let (loading, set_loading) = create_signal(false);
    let (error, set_error) = create_signal(None::<String>);
    
    // Load VPN apps on mount
    create_effect(move |_| {
        spawn_local(async move {
            set_loading.set(true);
            set_error.set(None);
            
            match load_vpn_apps().await {
                Ok(loaded_apps) => {
                    set_apps.set(loaded_apps);
                }
                Err(e) => {
                    set_error.set(Some(e));
                }
            }
            
            set_loading.set(false);
        });
    });
    
    // Auto-refresh stats every 15 seconds
    create_effect(move |_| {
        let interval = gloo_timers::callback::Interval::new(15000, move || {
            spawn_local(async move {
                if let Ok(new_stats) = load_vpn_stats().await {
                    set_stats.set(new_stats);
                }
            });
        });
        
        // Keep interval alive
        std::mem::forget(interval);
    });
    
    view! {
        <div class=format!("vpn-apps-manager {}", class.unwrap_or_default())>
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-lg font-semibold text-gray-900">VPN Applications</h2>
                            <p class="mt-1 text-sm text-gray-600">Manage VPN servers and enterprise VPN features</p>
                        </div>
                        <div class="flex space-x-2">
                            <button 
                                class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
                                on:click=move |_| {
                                    spawn_local(async move {
                                        if let Ok(refreshed) = load_vpn_apps().await {
                                            set_apps.set(refreshed);
                                        }
                                    });
                                }
                            >
                                🔄 Refresh
                            </button>
                            <button class="bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700 transition-colors">
                                ➕ Add VPN
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="p-6">
                    {move || {
                        if loading.get() {
                            view! {
                                <div class="flex items-center justify-center py-8">
                                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                    <span class="ml-3 text-gray-600">Loading VPN applications...</span>
                                </div>
                            }.into_view()
                        } else if let Some(err) = error.get() {
                            view! {
                                <div class="bg-red-50 border border-red-200 rounded-md p-4">
                                    <div class="flex">
                                        <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                                        </svg>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-red-800">VPN Apps Loading Error</h3>
                                            <p class="mt-1 text-sm text-red-700">{err}</p>
                                        </div>
                                    </div>
                                </div>
                            }.into_view()
                        } else {
                            view! {
                                <div class="space-y-6">
                                    // VPN overview
                                    <VpnOverview apps=apps stats=stats />
                                    
                                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                        // VPN apps list
                                        <div>
                                            <VpnAppsList 
                                                apps=apps
                                                selected_app=selected_app
                                                on_select=move |id: String| set_selected_app.set(Some(id))
                                                on_app_action=move |action: VpnAppAction| {
                                                    spawn_local(async move {
                                                        if let Ok(updated) = handle_vpn_app_action(action).await {
                                                            set_apps.set(updated);
                                                        }
                                                    });
                                                }
                                            />
                                        </div>
                                        
                                        // VPN connections
                                        <div>
                                            <VpnConnections apps=apps selected_app=selected_app />
                                        </div>
                                    </div>
                                </div>
                            }.into_view()
                        }
                    }}
                </div>
            </div>
        </div>
    }
}

/// VPN app action types
#[derive(Debug, Clone)]
pub enum VpnAppAction {
    Enable(String),
    Disable(String),
    Start(String),
    Stop(String),
    Restart(String),
    Configure(String, HashMap<String, String>),
    DisconnectClient(String, String),
    GenerateCertificate(String),
    RevokeClient(String, String),
}

/// VPN overview component
#[component]
fn VpnOverview(
    apps: ReadSignal<Vec<VpnApp>>,
    stats: ReadSignal<VpnStats>,
) -> impl IntoView {
    view! {
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {move || {
                let app_list = apps.get();
                let current_stats = stats.get();

                let active_servers = app_list.iter().filter(|app| app.running).count();
                let total_connections = app_list.iter().map(|app| app.active_clients).sum::<u32>();
                let total_bandwidth = app_list.iter().map(|app| app.total_bytes_sent + app.total_bytes_received).sum::<u64>();

                view! {
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-blue-600">Active Servers</p>
                                <p class="text-2xl font-semibold text-blue-900">{active_servers}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-green-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-green-600">Active Connections</p>
                                <p class="text-2xl font-semibold text-green-900">{total_connections}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-purple-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-purple-600">Bandwidth</p>
                                <p class="text-2xl font-semibold text-purple-900">{format_bytes(total_bandwidth)}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-yellow-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-yellow-600">Success Rate</p>
                                <p class="text-2xl font-semibold text-yellow-900">{format!("{:.1}%", current_stats.connection_success_rate * 100.0)}</p>
                            </div>
                        </div>
                    </div>
                }
            }}
        </div>
    }
}

/// VPN apps list component
#[component]
fn VpnAppsList(
    apps: ReadSignal<Vec<VpnApp>>,
    selected_app: ReadSignal<Option<String>>,
    on_select: impl Fn(String) + 'static + Copy,
    on_app_action: impl Fn(VpnAppAction) + 'static + Copy,
) -> impl IntoView {
    view! {
        <div class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900">VPN Applications</h3>

            <div class="space-y-2">
                {move || {
                    let app_list = apps.get();

                    if app_list.is_empty() {
                        view! {
                            <div class="text-center py-8 text-gray-500">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                                </svg>
                                <p class="mt-2">No VPN applications configured</p>
                            </div>
                        }.into_view()
                    } else {
                        app_list.into_iter().map(|app| {
                            let app_id = app.id.clone();
                            let is_selected = selected_app.get().as_ref() == Some(&app.id);

                            view! {
                                <VpnAppCard
                                    app=app
                                    is_selected=is_selected
                                    on_select=move || on_select(app_id.clone())
                                    on_action=on_app_action
                                />
                            }
                        }).collect::<Vec<_>>()
                    }
                }}
            </div>
        </div>
    }
}

/// VPN app card component
#[component]
fn VpnAppCard(
    app: VpnApp,
    is_selected: bool,
    on_select: impl Fn() + 'static + Copy,
    on_action: impl Fn(VpnAppAction) + 'static + Copy,
) -> impl IntoView {
    let app_id = app.id.clone();

    view! {
        <div
            class=move || format!(
                "border rounded-lg p-4 cursor-pointer transition-colors {}",
                if is_selected {
                    "border-blue-500 bg-blue-50"
                } else {
                    "border-gray-200 hover:border-gray-300 bg-white"
                }
            )
            on:click=move |_| on_select()
        >
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <span class="text-2xl">{app.app_type.icon()}</span>
                    <div>
                        <h4 class="font-medium text-gray-900">{app.name.clone()}</h4>
                        <p class="text-sm text-gray-500">{app.app_type.protocol()} • Port {app.listen_port}</p>
                    </div>
                </div>

                <div class="flex items-center space-x-2">
                    <div class="flex items-center space-x-1">
                        {if app.enabled {
                            view! {
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-green-600 bg-green-100">
                                    ✓ Enabled
                                </span>
                            }.into_view()
                        } else {
                            view! {
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-gray-600 bg-gray-100">
                                    ○ Disabled
                                </span>
                            }.into_view()
                        }}

                        {if app.running {
                            view! {
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-blue-600 bg-blue-100">
                                    ▶️ Running
                                </span>
                            }.into_view()
                        } else {
                            view! {
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-gray-600 bg-gray-100">
                                    ⏹️ Stopped
                                </span>
                            }.into_view()
                        }}
                    </div>

                    <div class="flex space-x-1">
                        <button
                            class="p-1 text-blue-600 hover:bg-blue-100 rounded"
                            title="Restart VPN server"
                            on:click=move |e| {
                                e.stop_propagation();
                                on_action(VpnAppAction::Restart(app_id.clone()));
                            }
                        >
                            🔄
                        </button>

                        <button
                            class="p-1 text-green-600 hover:bg-green-100 rounded"
                            title="Generate certificate"
                            on:click=move |e| {
                                e.stop_propagation();
                                on_action(VpnAppAction::GenerateCertificate(app_id.clone()));
                            }
                        >
                            📜
                        </button>
                    </div>
                </div>
            </div>

            <div class="mt-3 grid grid-cols-3 gap-4 text-sm text-gray-500">
                <div>
                    <span class="font-medium">Clients:</span> {app.active_clients}/{app.max_clients}
                </div>
                <div>
                    <span class="font-medium">Sent:</span> {format_bytes(app.total_bytes_sent)}
                </div>
                <div>
                    <span class="font-medium">Received:</span> {format_bytes(app.total_bytes_received)}
                </div>
            </div>

            {if let Some(cert_expiry) = app.certificate_expiry {
                let days_until_expiry = (cert_expiry - chrono::Utc::now()).num_days();
                view! {
                    <div class=format!(
                        "mt-2 text-xs {}",
                        if days_until_expiry < 30 {
                            "text-red-500"
                        } else if days_until_expiry < 90 {
                            "text-yellow-500"
                        } else {
                            "text-gray-400"
                        }
                    )>
                        Certificate expires in {days_until_expiry} days
                    </div>
                }.into_view()
            } else {
                view! {
                    <div class="mt-2 text-xs text-gray-400">
                        No certificate configured
                    </div>
                }.into_view()
            }}
        </div>
    }
}

/// VPN connections component
#[component]
fn VpnConnections(
    apps: ReadSignal<Vec<VpnApp>>,
    selected_app: ReadSignal<Option<String>>,
) -> impl IntoView {
    view! {
        <div class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900">Active Connections</h3>

            <div class="space-y-2 max-h-96 overflow-y-auto">
                {move || {
                    let app_list = apps.get();
                    let selected_id = selected_app.get();

                    let connections: Vec<VpnConnection> = if let Some(app_id) = selected_id {
                        app_list.iter()
                            .find(|app| app.id == app_id)
                            .map(|app| app.connections.clone())
                            .unwrap_or_default()
                    } else {
                        app_list.iter()
                            .flat_map(|app| app.connections.clone())
                            .collect()
                    };

                    if connections.is_empty() {
                        view! {
                            <div class="text-center py-8 text-gray-500">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                </svg>
                                <p class="mt-2">No active VPN connections</p>
                            </div>
                        }.into_view()
                    } else {
                        connections.into_iter().map(|connection| {
                            view! {
                                <VpnConnectionCard connection=connection />
                            }
                        }).collect::<Vec<_>>()
                    }
                }}
            </div>
        </div>
    }
}

/// VPN connection card component
#[component]
fn VpnConnectionCard(
    connection: VpnConnection,
) -> impl IntoView {
    view! {
        <div class="border rounded-lg p-3 bg-white hover:bg-gray-50 transition-colors">
            <div class="flex items-start justify-between">
                <div class="flex items-start space-x-3">
                    <span class="text-lg">{connection.status.icon()}</span>
                    <div class="flex-1">
                        <div class="flex items-center space-x-2">
                            <h4 class="font-medium text-gray-900">{connection.client_name.clone()}</h4>
                            <span class=format!(
                                "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {}",
                                connection.status.css_class()
                            )>
                                {connection.status.display_name()}
                            </span>
                        </div>
                        <div class="mt-1 space-y-1">
                            <div class="flex items-center space-x-4 text-sm text-gray-600">
                                <span>Real IP: {connection.client_ip.clone()}</span>
                                <span>VPN IP: {connection.virtual_ip.clone()}</span>
                                <span>Protocol: {connection.protocol.clone()}</span>
                            </div>
                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                <span>↑ {format_bytes(connection.bytes_sent)}</span>
                                <span>↓ {format_bytes(connection.bytes_received)}</span>
                                {if let Some(duration) = connection.duration {
                                    view! {
                                        <span>Duration: {format_duration(duration)}</span>
                                    }.into_view()
                                } else {
                                    view! { <span></span> }.into_view()
                                }}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex space-x-1">
                    <button class="p-1 text-blue-600 hover:bg-blue-100 rounded" title="View details">
                        👁️
                    </button>
                    {if connection.status == VpnConnectionStatus::Connected {
                        view! {
                            <button class="p-1 text-red-600 hover:bg-red-100 rounded" title="Disconnect">
                                ❌
                            </button>
                        }.into_view()
                    } else {
                        view! { <div></div> }.into_view()
                    }}
                </div>
            </div>
        </div>
    }
}

/// Utility function to format bytes
fn format_bytes(bytes: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = bytes as f64;
    let mut unit_index = 0;

    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }

    if unit_index == 0 {
        format!("{} {}", bytes, UNITS[unit_index])
    } else {
        format!("{:.1} {}", size, UNITS[unit_index])
    }
}

/// Utility function to format duration
fn format_duration(duration: std::time::Duration) -> String {
    let total_seconds = duration.as_secs();
    let hours = total_seconds / 3600;
    let minutes = (total_seconds % 3600) / 60;
    let seconds = total_seconds % 60;

    if hours > 0 {
        format!("{}h {}m", hours, minutes)
    } else if minutes > 0 {
        format!("{}m {}s", minutes, seconds)
    } else {
        format!("{}s", seconds)
    }
}

/// Get default VPN applications
fn get_default_vpn_apps() -> Vec<VpnApp> {
    let mut apps = vec![
        VpnApp::new(VpnAppType::OpenVpn),
        VpnApp::new(VpnAppType::WireGuard),
        VpnApp::new(VpnAppType::IpsecVpn),
        VpnApp::new(VpnAppType::L2tpVpn),
        VpnApp::new(VpnAppType::SiteToSite),
        VpnApp::new(VpnAppType::ClientToSite),
        VpnApp::new(VpnAppType::VpnMonitoring),
        VpnApp::new(VpnAppType::VpnAnalytics),
    ];

    // Configure OpenVPN with realistic settings
    if let Some(openvpn) = apps.iter_mut().find(|app| app.app_type == VpnAppType::OpenVpn) {
        openvpn.enabled = true;
        openvpn.running = true;
        openvpn.active_clients = 3;
        openvpn.max_clients = 10;
        openvpn.total_bytes_sent = 1024 * 1024 * 150; // 150 MB
        openvpn.total_bytes_received = 1024 * 1024 * 89; // 89 MB
        openvpn.uptime = Some(std::time::Duration::from_secs(86400 * 2)); // 2 days
        openvpn.last_restart = Some(chrono::Utc::now() - chrono::Duration::days(2));
        openvpn.certificate_expiry = Some(chrono::Utc::now() + chrono::Duration::days(365));
        openvpn.config.insert("cipher".to_string(), "AES-256-GCM".to_string());
        openvpn.config.insert("auth".to_string(), "SHA256".to_string());

        // Add sample connections
        openvpn.connections = vec![
            VpnConnection {
                id: "conn-001".to_string(),
                client_name: "laptop-user1".to_string(),
                client_ip: "************".to_string(),
                virtual_ip: "********".to_string(),
                status: VpnConnectionStatus::Connected,
                connected_at: Some(chrono::Utc::now() - chrono::Duration::hours(2)),
                bytes_sent: 1024 * 1024 * 45, // 45 MB
                bytes_received: 1024 * 1024 * 23, // 23 MB
                duration: Some(std::time::Duration::from_secs(7200)), // 2 hours
                protocol: "OpenVPN".to_string(),
                encryption: "AES-256-GCM".to_string(),
                last_activity: chrono::Utc::now() - chrono::Duration::minutes(5),
            },
            VpnConnection {
                id: "conn-002".to_string(),
                client_name: "mobile-user2".to_string(),
                client_ip: "**************".to_string(),
                virtual_ip: "********".to_string(),
                status: VpnConnectionStatus::Connected,
                connected_at: Some(chrono::Utc::now() - chrono::Duration::minutes(45)),
                bytes_sent: 1024 * 1024 * 12, // 12 MB
                bytes_received: 1024 * 1024 * 8, // 8 MB
                duration: Some(std::time::Duration::from_secs(2700)), // 45 minutes
                protocol: "OpenVPN".to_string(),
                encryption: "AES-256-GCM".to_string(),
                last_activity: chrono::Utc::now() - chrono::Duration::minutes(2),
            },
        ];
    }

    // Configure WireGuard
    if let Some(wireguard) = apps.iter_mut().find(|app| app.app_type == VpnAppType::WireGuard) {
        wireguard.enabled = true;
        wireguard.running = true;
        wireguard.active_clients = 2;
        wireguard.max_clients = 20;
        wireguard.total_bytes_sent = 1024 * 1024 * 78; // 78 MB
        wireguard.total_bytes_received = 1024 * 1024 * 45; // 45 MB
        wireguard.uptime = Some(std::time::Duration::from_secs(86400 * 5)); // 5 days
        wireguard.config.insert("private_key".to_string(), "[REDACTED]".to_string());
        wireguard.config.insert("listen_port".to_string(), "51820".to_string());

        wireguard.connections = vec![
            VpnConnection {
                id: "wg-001".to_string(),
                client_name: "server-backup".to_string(),
                client_ip: "***********".to_string(),
                virtual_ip: "********".to_string(),
                status: VpnConnectionStatus::Connected,
                connected_at: Some(chrono::Utc::now() - chrono::Duration::days(1)),
                bytes_sent: 1024 * 1024 * 34, // 34 MB
                bytes_received: 1024 * 1024 * 21, // 21 MB
                duration: Some(std::time::Duration::from_secs(86400)), // 1 day
                protocol: "WireGuard".to_string(),
                encryption: "ChaCha20Poly1305".to_string(),
                last_activity: chrono::Utc::now() - chrono::Duration::minutes(1),
            },
        ];
    }

    apps
}

/// Get mock VPN statistics
fn get_mock_vpn_stats() -> VpnStats {
    VpnStats {
        timestamp: chrono::Utc::now(),
        total_connections: 156,
        active_connections: 5,
        bytes_per_second_in: 1024.5,
        bytes_per_second_out: 2048.3,
        connection_success_rate: 0.94,
        average_connection_duration: 3600.0, // 1 hour
        top_clients: vec![
            ("laptop-user1".to_string(), 1024 * 1024 * 45),
            ("mobile-user2".to_string(), 1024 * 1024 * 23),
            ("server-backup".to_string(), 1024 * 1024 * 78),
            ("office-gateway".to_string(), 1024 * 1024 * 34),
            ("remote-worker".to_string(), 1024 * 1024 * 12),
        ],
        protocol_distribution: HashMap::from([
            ("OpenVPN".to_string(), 3),
            ("WireGuard".to_string(), 2),
            ("IPsec".to_string(), 0),
            ("L2TP".to_string(), 0),
        ]),
    }
}

/// Load VPN applications from system (mock implementation)
async fn load_vpn_apps() -> Result<Vec<VpnApp>, String> {
    // Simulate network delay
    gloo_timers::future::TimeoutFuture::new(700).await;

    // In a real implementation, this would query the VPN system
    // For now, return mock data
    Ok(get_default_vpn_apps())
}

/// Load VPN statistics (mock implementation)
async fn load_vpn_stats() -> Result<VpnStats, String> {
    // Simulate network delay
    gloo_timers::future::TimeoutFuture::new(400).await;

    // In a real implementation, this would query VPN statistics
    // For now, return mock data with some variation
    let mut stats = get_mock_vpn_stats();

    // Add some random variation to make it look live
    use js_sys::Math;
    stats.bytes_per_second_in = 800.0 + (Math::random() * 400.0);
    stats.bytes_per_second_out = 1500.0 + (Math::random() * 1000.0);
    stats.active_connections = 3 + (Math::random() * 5.0) as u32;

    Ok(stats)
}

/// Handle VPN app actions (mock implementation)
async fn handle_vpn_app_action(action: VpnAppAction) -> Result<Vec<VpnApp>, String> {
    // Simulate processing delay
    gloo_timers::future::TimeoutFuture::new(1100).await;

    match action {
        VpnAppAction::Enable(app_id) => {
            log::info!("Enabling VPN app: {}", app_id);
            // In real implementation, would enable the VPN application
        }
        VpnAppAction::Disable(app_id) => {
            log::info!("Disabling VPN app: {}", app_id);
            // In real implementation, would disable the VPN application
        }
        VpnAppAction::Start(app_id) => {
            log::info!("Starting VPN app: {}", app_id);
            // In real implementation, would start the VPN service
        }
        VpnAppAction::Stop(app_id) => {
            log::info!("Stopping VPN app: {}", app_id);
            // In real implementation, would stop the VPN service
        }
        VpnAppAction::Restart(app_id) => {
            log::info!("Restarting VPN app: {}", app_id);
            // In real implementation, would restart the VPN service
        }
        VpnAppAction::Configure(app_id, config) => {
            log::info!("Configuring VPN app: {} with {:?}", app_id, config);
            // In real implementation, would update VPN app configuration
        }
        VpnAppAction::DisconnectClient(app_id, client_id) => {
            log::info!("Disconnecting client: {} from VPN app: {}", client_id, app_id);
            // In real implementation, would disconnect the specific client
        }
        VpnAppAction::GenerateCertificate(app_id) => {
            log::info!("Generating certificate for VPN app: {}", app_id);
            // In real implementation, would generate new certificates
        }
        VpnAppAction::RevokeClient(app_id, client_id) => {
            log::info!("Revoking client: {} from VPN app: {}", client_id, app_id);
            // In real implementation, would revoke client certificate
        }
    }

    // Return updated apps list
    load_vpn_apps().await
}
