//! Web Terminal Component
//! 
//! Provides a web-based terminal interface with SSH integration and command execution.
//! Features include:
//! - Real-time terminal emulation
//! - SSH connection management
//! - Command history
//! - Customizable terminal settings
//! - WebSocket-based communication

use leptos::*;
use leptos::ev::SubmitEvent;
use web_sys::{HtmlInputElement, HtmlTextAreaElement, KeyboardEvent, HtmlSelectElement};
use wasm_bindgen::JsCast;
use serde::{Deserialize, Serialize};
use std::rc::Rc;

use crate::components::{<PERSON>dal, Button, ButtonVariant, ButtonSize, LoadingSpinner, <PERSON>ert, AlertVariant};
use crate::api::terminal::{get_terminal_api, TerminalSession, SshConnection, TerminalCommand};

/// Terminal connection status
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ConnectionStatus {
    Disconnected,
    Connecting,
    Connected,
    Error(String),
}

/// Terminal theme configuration
#[derive(Debug, <PERSON><PERSON>, PartialEq, Serialize, Deserialize)]
pub struct TerminalTheme {
    pub background: String,
    pub foreground: String,
    pub cursor: String,
    pub selection: String,
    pub font_family: String,
    pub font_size: u32,
}

impl Default for TerminalTheme {
    fn default() -> Self {
        Self {
            background: "#1e1e1e".to_string(),
            foreground: "#d4d4d4".to_string(),
            cursor: "#ffffff".to_string(),
            selection: "#264f78".to_string(),
            font_family: "Monaco, 'Cascadia Code', 'Roboto Mono', monospace".to_string(),
            font_size: 14,
        }
    }
}

/// Terminal component properties
#[derive(Clone, PartialEq)]
pub struct TerminalProps {
    pub session_id: Option<String>,
    pub auto_connect: bool,
    pub theme: TerminalTheme,
    pub max_history: usize,
    pub enable_ssh: bool,
    pub default_host: Option<String>,
    pub on_connect: Option<Rc<dyn Fn(String)>>,
    pub on_disconnect: Option<Rc<dyn Fn()>>,
    pub on_command: Option<Rc<dyn Fn(String)>>,
}

impl Default for TerminalProps {
    fn default() -> Self {
        Self {
            session_id: None,
            auto_connect: false,
            theme: TerminalTheme::default(),
            max_history: 1000,
            enable_ssh: true,
            default_host: None,
            on_connect: None,
            on_disconnect: None,
            on_command: None,
        }
    }
}

/// Main Web Terminal Component
#[component]
pub fn WebTerminal(
    #[prop(optional)] session_id: Option<String>,
    #[prop(default = false)] auto_connect: bool,
    #[prop(default = TerminalTheme::default())] theme: TerminalTheme,
    #[prop(default = 1000)] max_history: usize,
    #[prop(default = true)] enable_ssh: bool,
    #[prop(optional)] default_host: Option<String>,
    #[prop(optional)] on_connect: Option<Rc<dyn Fn(String)>>,
    #[prop(optional)] on_disconnect: Option<Rc<dyn Fn()>>,
    #[prop(optional)] on_command: Option<Rc<dyn Fn(String)>>,
) -> impl IntoView {
    let api = get_terminal_api();
    
    // Terminal state
    let (connection_status, set_connection_status) = create_signal(ConnectionStatus::Disconnected);
    let (terminal_output, set_terminal_output) = create_signal(String::new());
    let (command_history, set_command_history) = create_signal(Vec::<String>::new());
    let (history_index, set_history_index) = create_signal(0usize);
    let (current_command, set_current_command) = create_signal(String::new());
    let (current_session, set_current_session) = create_signal(None::<TerminalSession>);
    
    // SSH connection state
    let (show_ssh_modal, set_show_ssh_modal) = create_signal(false);
    let (ssh_host, set_ssh_host) = create_signal(default_host.unwrap_or_default());
    let (ssh_port, set_ssh_port) = create_signal("22".to_string());
    let (ssh_username, set_ssh_username) = create_signal("root".to_string());
    let (ssh_password, set_ssh_password) = create_signal(String::new());
    let (ssh_key_auth, set_ssh_key_auth) = create_signal(false);
    let (ssh_private_key, set_ssh_private_key) = create_signal(String::new());
    
    // Terminal settings
    let (show_settings_modal, set_show_settings_modal) = create_signal(false);
    let (terminal_theme, set_terminal_theme) = create_signal(theme);
    
    // Loading states
    let (connecting, set_connecting) = create_signal(false);
    let (executing_command, set_executing_command) = create_signal(false);
    
    // Error handling
    let (error_message, set_error_message) = create_signal(None::<String>);
    
    // Terminal input reference
    let terminal_input_ref = create_node_ref::<leptos::html::Input>();
    let terminal_output_ref = create_node_ref::<leptos::html::Div>();
    
    // Connect to SSH server
    let connect_ssh = create_action({
        let api = api.clone();
        move |_: &()| {
            let api = api.clone();
            let host = ssh_host.get();
            let port = ssh_port.get().parse::<u16>().unwrap_or(22);
            let username = ssh_username.get();
            let password = ssh_password.get();
            let use_key = ssh_key_auth.get();
            let private_key = ssh_private_key.get();
            
            async move {
                set_connecting.set(true);
                set_connection_status.set(ConnectionStatus::Connecting);
                set_error_message.set(None);
                
                let connection = SshConnection {
                    host,
                    port,
                    username,
                    password: if use_key { None } else { Some(password) },
                    private_key: if use_key { Some(private_key) } else { None },
                    timeout: 30,
                };
                
                match api.connect_ssh(connection).await {
                    Ok(session) => {
                        set_current_session.set(Some(session.clone()));
                        set_connection_status.set(ConnectionStatus::Connected);
                        set_show_ssh_modal.set(false);
                        set_terminal_output.set(format!("Connected to {}@{}\n", session.username, session.host));
                        
                        if let Some(callback) = &on_connect {
                            callback(session.id.clone());
                        }
                    }
                    Err(error) => {
                        set_connection_status.set(ConnectionStatus::Error(error.clone()));
                        set_error_message.set(Some(error));
                    }
                }
                
                set_connecting.set(false);
            }
        }
    });
    
    // Disconnect from SSH server
    let disconnect_ssh = create_action({
        let api = api.clone();
        move |_: &()| {
            let api = api.clone();
            let session = current_session.get();
            
            async move {
                if let Some(session) = session {
                    match api.disconnect_ssh(&session.id).await {
                        Ok(_) => {
                            set_current_session.set(None);
                            set_connection_status.set(ConnectionStatus::Disconnected);
                            set_terminal_output.update(|output| {
                                output.push_str("\nConnection closed.\n");
                            });
                            
                            if let Some(callback) = &on_disconnect {
                                callback();
                            }
                        }
                        Err(error) => {
                            set_error_message.set(Some(error));
                        }
                    }
                }
            }
        }
    });
    
    // Execute command
    let execute_command = create_action({
        let api = api.clone();
        move |command: &String| {
            let api = api.clone();
            let command = command.clone();
            let session = current_session.get();
            
            async move {
                if let Some(session) = session {
                    set_executing_command.set(true);
                    
                    // Add command to history
                    set_command_history.update(|history| {
                        history.push(command.clone());
                        if history.len() > max_history {
                            history.remove(0);
                        }
                    });
                    set_history_index.set(command_history.get().len());
                    
                    // Add command to output
                    set_terminal_output.update(|output| {
                        output.push_str(&format!("{}@{}:~$ {}\n", session.username, session.host, command));
                    });
                    
                    let terminal_command = TerminalCommand {
                        session_id: session.id.clone(),
                        command: command.clone(),
                        timeout: 30,
                    };
                    
                    match api.execute_command(terminal_command).await {
                        Ok(result) => {
                            set_terminal_output.update(|output| {
                                output.push_str(&result.output);
                                if result.exit_code != 0 {
                                    output.push_str(&format!("\nExit code: {}\n", result.exit_code));
                                }
                            });
                            
                            if let Some(callback) = &on_command {
                                callback(command);
                            }
                        }
                        Err(error) => {
                            set_terminal_output.update(|output| {
                                output.push_str(&format!("Error: {}\n", error));
                            });
                        }
                    }
                    
                    set_executing_command.set(false);
                    
                    // Auto-scroll to bottom
                    if let Some(output_element) = terminal_output_ref.get() {
                        output_element.set_scroll_top(output_element.scroll_height());
                    }
                }
            }
        }
    });
    
    // Handle command input
    let handle_command_input = move |ev: KeyboardEvent| {
        let key = ev.key();
        match key.as_str() {
            "Enter" => {
                ev.prevent_default();
                let command = current_command.get().trim().to_string();
                if !command.is_empty() {
                    execute_command.dispatch(command);
                    set_current_command.set(String::new());
                }
            }
            "ArrowUp" => {
                ev.prevent_default();
                let history = command_history.get();
                let current_index = history_index.get();
                if current_index > 0 {
                    let new_index = current_index - 1;
                    set_history_index.set(new_index);
                    if let Some(cmd) = history.get(new_index) {
                        set_current_command.set(cmd.clone());
                    }
                }
            }
            "ArrowDown" => {
                ev.prevent_default();
                let history = command_history.get();
                let current_index = history_index.get();
                if current_index < history.len() {
                    let new_index = current_index + 1;
                    set_history_index.set(new_index);
                    if new_index < history.len() {
                        if let Some(cmd) = history.get(new_index) {
                            set_current_command.set(cmd.clone());
                        }
                    } else {
                        set_current_command.set(String::new());
                    }
                }
            }
            _ => {}
        }
    };
    
    // Clear terminal
    let clear_terminal = move |_| {
        set_terminal_output.set(String::new());
    };
    
    // Auto-connect on mount if enabled
    create_effect(move |_| {
        if auto_connect && enable_ssh && !ssh_host.get().is_empty() {
            connect_ssh.dispatch(());
        }
    });

    view! {
        <div class="web-terminal bg-gray-900 text-white rounded-lg shadow-lg">
            // Terminal header
            <div class="terminal-header bg-gray-800 px-4 py-2 rounded-t-lg flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <div class="flex space-x-1">
                        <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                        <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                    </div>
                    <span class="text-sm font-medium">
                        {move || match connection_status.get() {
                            ConnectionStatus::Connected => {
                                if let Some(session) = current_session.get() {
                                    format!("{}@{}", session.username, session.host)
                                } else {
                                    "Terminal".to_string()
                                }
                            }
                            ConnectionStatus::Connecting => "Connecting...".to_string(),
                            ConnectionStatus::Disconnected => "Terminal (Disconnected)".to_string(),
                            ConnectionStatus::Error(_) => "Terminal (Error)".to_string(),
                        }}
                    </span>
                </div>

                <div class="flex items-center space-x-2">
                    // Connection status indicator
                    <div class={move || format!("w-2 h-2 rounded-full {}",
                        match connection_status.get() {
                            ConnectionStatus::Connected => "bg-green-500",
                            ConnectionStatus::Connecting => "bg-yellow-500 animate-pulse",
                            ConnectionStatus::Disconnected => "bg-gray-500",
                            ConnectionStatus::Error(_) => "bg-red-500",
                        }
                    )}></div>

                    // Action buttons
                    <div class="flex space-x-1">
                        {move || if enable_ssh {
                            view! {
                                <Button
                                    variant=ButtonVariant::Ghost
                                    size=ButtonSize::Small
                                    on_click=move |_| set_show_ssh_modal.set(true)
                                    disabled=connecting.get()
                                >
                                    {move || if connection_status.get() == ConnectionStatus::Connected {
                                        "Reconnect"
                                    } else {
                                        "Connect"
                                    }}
                                </Button>
                            }.into_view()
                        } else {
                            view! { <span></span> }.into_view()
                        }}

                        {move || if connection_status.get() == ConnectionStatus::Connected {
                            view! {
                                <Button
                                    variant=ButtonVariant::Ghost
                                    size=ButtonSize::Small
                                    on_click=move |_| disconnect_ssh.dispatch(())
                                >
                                    "Disconnect"
                                </Button>
                            }.into_view()
                        } else {
                            view! { <span></span> }.into_view()
                        }}

                        <Button
                            variant=ButtonVariant::Ghost
                            size=ButtonSize::Small
                            on_click=clear_terminal
                        >
                            "Clear"
                        </Button>

                        <Button
                            variant=ButtonVariant::Ghost
                            size=ButtonSize::Small
                            on_click=move |_| set_show_settings_modal.set(true)
                        >
                            "⚙️"
                        </Button>
                    </div>
                </div>
            </div>

            // Error display
            {move || error_message.get().map(|error| {
                view! {
                    <div class="p-2">
                        <Alert variant=AlertVariant::Error>
                            {error}
                        </Alert>
                    </div>
                }
            })}

            // Terminal output area
            <div
                class="terminal-output p-4 h-96 overflow-y-auto font-mono text-sm leading-relaxed"
                style=move || format!(
                    "background-color: {}; color: {}; font-family: {}; font-size: {}px;",
                    terminal_theme.get().background,
                    terminal_theme.get().foreground,
                    terminal_theme.get().font_family,
                    terminal_theme.get().font_size
                )
                node_ref=terminal_output_ref
            >
                <pre class="whitespace-pre-wrap">{move || terminal_output.get()}</pre>

                // Loading indicator for command execution
                {move || if executing_command.get() {
                    view! {
                        <div class="flex items-center space-x-2 mt-2">
                            <LoadingSpinner size="small" />
                            <span class="text-sm opacity-75">Executing command...</span>
                        </div>
                    }
                } else {
                    view! { <span></span> }
                }}
            </div>

            // Command input area
            {move || if connection_status.get() == ConnectionStatus::Connected {
                view! {
                    <div class="terminal-input border-t border-gray-700 p-4">
                        <div class="flex items-center space-x-2">
                            <span class="text-green-400 font-mono text-sm">
                                {move || if let Some(session) = current_session.get() {
                                    format!("{}@{}:~$", session.username, session.host)
                                } else {
                                    "$".to_string()
                                }}
                            </span>
                            <input
                                type="text"
                                class="flex-1 bg-transparent border-none outline-none font-mono text-sm"
                                style=move || format!(
                                    "color: {}; font-family: {};",
                                    terminal_theme.get().foreground,
                                    terminal_theme.get().font_family
                                )
                                placeholder="Enter command..."
                                prop:value=current_command
                                on:input=move |ev| {
                                    let target = ev.target().unwrap();
                                    let input = target.dyn_into::<HtmlInputElement>().unwrap();
                                    set_current_command.set(input.value());
                                }
                                on:keydown=handle_command_input
                                node_ref=terminal_input_ref
                                disabled=executing_command.get()
                            />
                        </div>
                    </div>
                }.into_view()
            } else {
                view! {
                    <div class="terminal-input border-t border-gray-700 p-4 text-center">
                        <span class="text-gray-500 text-sm">
                            {move || match connection_status.get() {
                                ConnectionStatus::Disconnected => "Not connected to any server",
                                ConnectionStatus::Connecting => "Establishing connection...",
                                ConnectionStatus::Error(ref error) => error.as_str(),
                                _ => "Terminal not ready"
                            }}
                        </span>
                    </div>
                }.into_view()
            }}
        </div>

        // SSH Connection Modal
        <Modal
            open=show_ssh_modal
            on_close=move |_| set_show_ssh_modal.set(false)
            title="SSH Connection"
        >
            <form on:submit=move |ev: SubmitEvent| {
                ev.prevent_default();
                connect_ssh.dispatch(());
            }>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            "Host"
                        </label>
                        <input
                            type="text"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="***********"
                            prop:value=ssh_host
                            on:input=move |ev| {
                                let target = ev.target().unwrap();
                                let input = target.dyn_into::<HtmlInputElement>().unwrap();
                                set_ssh_host.set(input.value());
                            }
                            required
                        />
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                "Port"
                            </label>
                            <input
                                type="number"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                prop:value=ssh_port
                                on:input=move |ev| {
                                    let target = ev.target().unwrap();
                                    let input = target.dyn_into::<HtmlInputElement>().unwrap();
                                    set_ssh_port.set(input.value());
                                }
                                min="1"
                                max="65535"
                            />
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                "Username"
                            </label>
                            <input
                                type="text"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                prop:value=ssh_username
                                on:input=move |ev| {
                                    let target = ev.target().unwrap();
                                    let input = target.dyn_into::<HtmlInputElement>().unwrap();
                                    set_ssh_username.set(input.value());
                                }
                                required
                            />
                        </div>
                    </div>

                    <div>
                        <label class="flex items-center space-x-2">
                            <input
                                type="checkbox"
                                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                prop:checked=ssh_key_auth
                                on:change=move |ev| {
                                    let target = ev.target().unwrap();
                                    let input = target.dyn_into::<HtmlInputElement>().unwrap();
                                    set_ssh_key_auth.set(input.checked());
                                }
                            />
                            <span class="text-sm font-medium text-gray-700">
                                "Use SSH key authentication"
                            </span>
                        </label>
                    </div>

                    {move || if ssh_key_auth.get() {
                        view! {
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    "Private Key"
                                </label>
                                <textarea
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    rows="4"
                                    placeholder="-----BEGIN OPENSSH PRIVATE KEY-----"
                                    prop:value=ssh_private_key
                                    on:input=move |ev| {
                                        let target = ev.target().unwrap();
                                        let textarea = target.dyn_into::<HtmlTextAreaElement>().unwrap();
                                        set_ssh_private_key.set(textarea.value());
                                    }
                                ></textarea>
                            </div>
                        }.into_view()
                    } else {
                        view! {
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    "Password"
                                </label>
                                <input
                                    type="password"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    prop:value=ssh_password
                                    on:input=move |ev| {
                                        let target = ev.target().unwrap();
                                        let input = target.dyn_into::<HtmlInputElement>().unwrap();
                                        set_ssh_password.set(input.value());
                                    }
                                />
                            </div>
                        }.into_view()
                    }}
                </div>

                <div class="flex justify-end space-x-2 mt-6">
                    <Button
                        variant=ButtonVariant::Secondary
                        on_click=move |_| set_show_ssh_modal.set(false)
                    >
                        "Cancel"
                    </Button>
                    <Button
                        variant=ButtonVariant::Primary
                        button_type="submit"
                        disabled=connecting.get()
                        loading=connecting.get()
                    >
                        "Connect"
                    </Button>
                </div>
            </form>
        </Modal>

        // Terminal Settings Modal
        <Modal
            open=show_settings_modal
            on_close=move |_| set_show_settings_modal.set(false)
            title="Terminal Settings"
        >
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        "Font Family"
                    </label>
                    <select
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        on:change=move |ev| {
                            let target = ev.target().unwrap();
                            let select = target.dyn_into::<HtmlSelectElement>().unwrap();
                            set_terminal_theme.update(|theme| {
                                theme.font_family = select.value();
                            });
                        }
                    >
                        <option value="Monaco, 'Cascadia Code', 'Roboto Mono', monospace" selected>
                            "Monaco / Cascadia Code"
                        </option>
                        <option value="'Fira Code', 'JetBrains Mono', monospace">
                            "Fira Code / JetBrains Mono"
                        </option>
                        <option value="'Source Code Pro', 'Ubuntu Mono', monospace">
                            "Source Code Pro / Ubuntu Mono"
                        </option>
                        <option value="'Courier New', monospace">
                            "Courier New"
                        </option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        "Font Size"
                    </label>
                    <input
                        type="range"
                        class="w-full"
                        min="10"
                        max="24"
                        prop:value=move || terminal_theme.get().font_size.to_string()
                        on:input=move |ev| {
                            let target = ev.target().unwrap();
                            let input = target.dyn_into::<HtmlInputElement>().unwrap();
                            if let Ok(size) = input.value().parse::<u32>() {
                                set_terminal_theme.update(|theme| {
                                    theme.font_size = size;
                                });
                            }
                        }
                    />
                    <div class="text-sm text-gray-500 mt-1">
                        {move || format!("{}px", terminal_theme.get().font_size)}
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            "Background Color"
                        </label>
                        <input
                            type="color"
                            class="w-full h-10 border border-gray-300 rounded-md"
                            prop:value=move || terminal_theme.get().background
                            on:input=move |ev| {
                                let target = ev.target().unwrap();
                                let input = target.dyn_into::<HtmlInputElement>().unwrap();
                                set_terminal_theme.update(|theme| {
                                    theme.background = input.value();
                                });
                            }
                        />
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            "Text Color"
                        </label>
                        <input
                            type="color"
                            class="w-full h-10 border border-gray-300 rounded-md"
                            prop:value=move || terminal_theme.get().foreground
                            on:input=move |ev| {
                                let target = ev.target().unwrap();
                                let input = target.dyn_into::<HtmlInputElement>().unwrap();
                                set_terminal_theme.update(|theme| {
                                    theme.foreground = input.value();
                                });
                            }
                        />
                    </div>
                </div>

                <div class="flex justify-between items-center pt-4">
                    <Button
                        variant=ButtonVariant::Secondary
                        on_click=move |_| {
                            set_terminal_theme.set(TerminalTheme::default());
                        }
                    >
                        "Reset to Default"
                    </Button>

                    <Button
                        variant=ButtonVariant::Primary
                        on_click=move |_| set_show_settings_modal.set(false)
                    >
                        "Apply"
                    </Button>
                </div>
            </div>
        </Modal>
    }
}
