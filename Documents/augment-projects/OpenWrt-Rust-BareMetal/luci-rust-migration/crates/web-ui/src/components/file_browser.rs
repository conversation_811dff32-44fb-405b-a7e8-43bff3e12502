//! File Browser Component
//!
//! A comprehensive file browser component for OpenWrt LuCI that provides
//! file system navigation, file operations, and file management capabilities.

use leptos::*;
use leptos::ev::SubmitEvent;
use web_sys::{DragEvent, File, FileList, HtmlInputElement, HtmlSelectElement, DataTransfer};
use wasm_bindgen::JsCast;
use crate::types::file_browser::*;
use crate::api::file_browser::*;
use crate::components::{Modal, FormModal, ProgressModal};
use crate::components::buttons::{Button, IconButton, ButtonVariant, ButtonSize};
use crate::components::modals::ProgressState;
use human_bytes::human_bytes;
use std::collections::HashSet;
use std::rc::Rc;

/// File Browser Component
#[component]
pub fn FileBrowser(
    /// Initial directory path
    #[prop(default = "/".to_string())]
    initial_path: String,
    /// Whether to show hidden files by default
    #[prop(default = false)]
    show_hidden_default: bool,
    /// Maximum file upload size in bytes
    #[prop(default = 100 * 1024 * 1024)] // 100MB
    max_upload_size: u64,
    /// Callback when a file is selected
    #[prop(optional)]
    on_file_select: Option<Box<dyn Fn(FileInfo)>>,
) -> impl IntoView {
    // Convert callback to Rc for cloning
    let on_file_select_rc = on_file_select.map(|callback| Rc::new(callback) as Rc<dyn Fn(FileInfo)>);

    // State management
    let (state, set_state) = create_signal(FileBrowserState {
        current_path: initial_path,
        show_hidden: show_hidden_default,
        ..Default::default()
    });
    
    let (selected_files, set_selected_files) = create_signal(HashSet::<String>::new());
    let (upload_progress, set_upload_progress) = create_signal(None::<f32>);
    let (show_upload_modal, set_show_upload_modal) = create_signal(false);
    let (show_create_folder_modal, set_show_create_folder_modal) = create_signal(false);
    let (show_file_operations_modal, set_show_file_operations_modal) = create_signal(false);
    let (is_drag_over, set_is_drag_over) = create_signal(false);
    
    // API client
    let api = get_file_browser_api();
    
    // File input reference for upload
    let file_input_ref = create_node_ref::<leptos::html::Input>();
    
    // Load directory listing
    let load_directory = create_action({
        let api = api.clone();
        move |path: &String| {
            let api = api.clone();
            let path = path.clone();
            async move {
                set_state.update(|s| s.loading = true);

                match api.list_directory(&path).await {
                    Ok(listing) => {
                        set_state.update(|s| {
                            s.listing = Some(listing);
                            s.current_path = path;
                            s.loading = false;
                            s.error = None;
                        });
                    }
                    Err(error) => {
                        set_state.update(|s| {
                            s.loading = false;
                            s.error = Some(error);
                        });
                    }
                }
            }
        }
    });
    
    // Initialize with current path
    create_effect(move |_| {
        let current_path = state.get().current_path;
        load_directory.dispatch(current_path);
    });
    
    // Navigation functions
    let navigate_to = move |path: String| {
        load_directory.dispatch(path);
    };
    
    let navigate_up = move |_| {
        let current = state.get().current_path;
        if let Some(parent) = std::path::Path::new(&current).parent() {
            navigate_to(parent.to_string_lossy().to_string());
        }
    };
    
    let navigate_home = move |_| {
        navigate_to("/".to_string());
    };
    
    // File selection
    let toggle_file_selection = move |file_path: String| {
        set_selected_files.update(|selected| {
            if selected.contains(&file_path) {
                selected.remove(&file_path);
            } else {
                selected.insert(file_path);
            }
        });
    };
    
    let select_all = move |_| {
        if let Some(listing) = state.get().listing {
            let all_paths: HashSet<String> = listing.entries.iter()
                .map(|entry| entry.path.clone())
                .collect();
            set_selected_files.set(all_paths);
        }
    };
    
    let clear_selection = move |_| {
        set_selected_files.set(HashSet::new());
    };
    
    // File operations
    let delete_selected = create_action({
        let api = api.clone();
        move |_: &()| {
            let api = api.clone();
            let selected = selected_files.get();
            async move {
                for file_path in selected {
                    if let Err(error) = api.delete(&file_path).await {
                        log::error!("Failed to delete {}: {}", file_path, error);
                    }
                }
                // Reload directory after deletion
                let current_path = state.get().current_path;
                load_directory.dispatch(current_path);
                set_selected_files.set(HashSet::new());
            }
        }
    });
    
    // File upload handling
    let handle_file_upload = create_action({
        let api = api.clone();
        move |files: &Vec<File>| {
            let api = api.clone();
            let files = files.clone();
            let target_path = state.get().current_path;
            async move {
                set_upload_progress.set(Some(0.0));

                match api.upload_files(files, &target_path, false).await {
                    Ok(_responses) => {
                        set_upload_progress.set(None);
                        // Reload directory after upload
                        load_directory.dispatch(target_path);
                    }
                    Err(error) => {
                        set_upload_progress.set(None);
                        log::error!("Upload failed: {}", error);
                    }
                }
            }
        }
    });

    // File rename operation
    let rename_file = create_action({
        let api = api.clone();
        move |(old_path, new_name): &(String, String)| {
            let api = api.clone();
            let old_path = old_path.clone();
            let new_name = new_name.clone();
            async move {
                match api.rename(&old_path, &new_name).await {
                    Ok(_) => {
                        // Reload directory after rename
                        let current_path = state.get().current_path;
                        load_directory.dispatch(current_path);
                    }
                    Err(error) => {
                        log::error!("Rename failed: {}", error);
                    }
                }
            }
        }
    });

    // File copy operation
    let copy_file = create_action({
        let api = api.clone();
        move |(source_path, dest_path): &(String, String)| {
            let api = api.clone();
            let source_path = source_path.clone();
            let dest_path = dest_path.clone();
            async move {
                match api.copy(&source_path, &dest_path, false).await {
                    Ok(_) => {
                        // Reload directory after copy
                        let current_path = state.get().current_path;
                        load_directory.dispatch(current_path);
                    }
                    Err(error) => {
                        log::error!("Copy failed: {}", error);
                    }
                }
            }
        }
    });

    // File move operation
    let move_file = create_action({
        let api = api.clone();
        move |(source_path, dest_path): &(String, String)| {
            let api = api.clone();
            let source_path = source_path.clone();
            let dest_path = dest_path.clone();
            async move {
                match api.move_file(&source_path, &dest_path, false).await {
                    Ok(_) => {
                        // Reload directory after move
                        let current_path = state.get().current_path;
                        load_directory.dispatch(current_path);
                    }
                    Err(error) => {
                        log::error!("Move failed: {}", error);
                    }
                }
            }
        }
    });

    // Create folder operation
    let create_folder = create_action({
        let api = api.clone();
        move |folder_name: &String| {
            let api = api.clone();
            let folder_name = folder_name.clone();
            let current_path = state.get().current_path;
            async move {
                let folder_path = format!("{}/{}", current_path, folder_name);
                match api.create_directory(&folder_path, None).await {
                    Ok(_) => {
                        // Reload directory after folder creation
                        load_directory.dispatch(current_path);
                        set_show_create_folder_modal.set(false);
                    }
                    Err(error) => {
                        log::error!("Create folder failed: {}", error);
                    }
                }
            }
        }
    });
    
    // Drag and drop handlers
    let on_drag_over = move |ev: DragEvent| {
        ev.prevent_default();
        set_is_drag_over.set(true);
    };

    let on_drag_leave = move |_ev: DragEvent| {
        set_is_drag_over.set(false);
    };

    let on_drop = move |ev: DragEvent| {
        ev.prevent_default();
        set_is_drag_over.set(false);

        if let Some(data_transfer) = ev.data_transfer() {
            if let Some(file_list) = data_transfer.files() {
                let files: Vec<File> = (0..file_list.length())
                    .filter_map(|i| file_list.get(i))
                    .collect();
                handle_file_upload.dispatch(files);
            }
        }
    };
    
    // File input change handler
    let on_file_input_change = move |ev: web_sys::Event| {
        let input = ev.target().unwrap().dyn_into::<HtmlInputElement>().unwrap();
        if let Some(file_list) = input.files() {
            let files: Vec<File> = (0..file_list.length())
                .filter_map(|i| file_list.get(i))
                .collect();
            handle_file_upload.dispatch(files);
        }
    };
    
    view! {
        <div class="file-browser bg-white rounded-lg shadow-lg">
            // Toolbar
            <div class="flex items-center justify-between p-4 border-b border-gray-200">
                <div class="flex items-center space-x-2">
                    <IconButton
                        icon=view! { <span>"🏠"</span> }.into_view()
                        variant=ButtonVariant::Ghost
                        size=ButtonSize::Small
                        on_click=Box::new({
                            let navigate_home = navigate_home.clone();
                            move || navigate_home(web_sys::MouseEvent::new("click").unwrap())
                        })
                    />
                    <IconButton
                        icon=view! { <span>"⬆️"</span> }.into_view()
                        variant=ButtonVariant::Ghost
                        size=ButtonSize::Small
                        on_click=Box::new({
                            let navigate_up = navigate_up.clone();
                            move || navigate_up(web_sys::MouseEvent::new("click").unwrap())
                        })
                        disabled=state.get().current_path == "/"
                    />
                    <span class="text-sm text-gray-600 font-mono">
                        {move || state.get().current_path}
                    </span>
                </div>
                
                <div class="flex items-center space-x-2">
                    <Button
                        variant=ButtonVariant::Secondary
                        size=ButtonSize::Small
                        on_click=Box::new({
                            let set_show_upload_modal = set_show_upload_modal.clone();
                            move || set_show_upload_modal.set(true)
                        })
                    >
                        "Upload"
                    </Button>
                    <Button
                        variant=ButtonVariant::Secondary
                        size=ButtonSize::Small
                        on_click=Box::new({
                            let set_show_create_folder_modal = set_show_create_folder_modal.clone();
                            move || set_show_create_folder_modal.set(true)
                        })
                    >
                        "New Folder"
                    </Button>

                    // File operations buttons (shown when files are selected)
                    {
                        let selected_count = selected_files.get().len();
                        if selected_count > 0 {
                            view! {
                                <Button
                                    variant=ButtonVariant::Secondary
                                    size=ButtonSize::Small
                                    on_click=Box::new({
                                        let set_show_file_operations_modal = set_show_file_operations_modal.clone();
                                        move || set_show_file_operations_modal.set(true)
                                    })
                                >
                                    "Operations"
                                </Button>
                            }.into_view()
                        } else {
                            view! {}.into_view()
                        }
                    }
                    <Button
                        variant=ButtonVariant::Secondary
                        size=ButtonSize::Small
                        on_click=Box::new({
                            let select_all = select_all.clone();
                            move || select_all(web_sys::MouseEvent::new("click").unwrap())
                        })
                    >
                        "Select All"
                    </Button>
                    <Button
                        variant=ButtonVariant::Secondary
                        size=ButtonSize::Small
                        on_click=Box::new({
                            let clear_selection = clear_selection.clone();
                            move || clear_selection(web_sys::MouseEvent::new("click").unwrap())
                        })
                    >
                        "Clear"
                    </Button>
                    {move || {
                        let selected_count = selected_files.get().len();
                        if selected_count > 0 {
                            view! {
                                <Button
                                    variant=ButtonVariant::Error
                                    size=ButtonSize::Small
                                    on_click=Box::new({
                                        let delete_selected = delete_selected.clone();
                                        move || delete_selected.dispatch(())
                                    })
                                >
                                    {format!("Delete ({})", selected_count)}
                                </Button>
                            }.into_view()
                        } else {
                            view! {}.into_view()
                        }
                    }}
                </div>
            </div>
            
            // Drop zone and file list
            <div
                class=move || format!(
                    "min-h-96 p-4 {}",
                    if is_drag_over.get() {
                        "bg-blue-50 border-2 border-dashed border-blue-300"
                    } else {
                        "bg-white"
                    }
                )
                on:dragover=on_drag_over
                on:dragleave=on_drag_leave
                on:drop=on_drop
            >
                {
                    let on_file_select_clone = on_file_select_rc.clone();
                    let navigate_to_clone = navigate_to.clone();
                    let toggle_file_selection_clone = toggle_file_selection.clone();

                    move || {
                        let state_val = state.get();

                        if state_val.loading {
                            view! {
                                <div class="flex items-center justify-center h-32">
                                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                    <span class="ml-2 text-gray-600">"Loading..."</span>
                                </div>
                            }.into_view()
                        } else if let Some(error) = state_val.error {
                            view! {
                                <div class="text-center text-red-600 p-8">
                                    <p class="text-lg font-semibold">"Error loading directory"</p>
                                    <p class="text-sm mt-2">{error}</p>
                                </div>
                            }.into_view()
                        } else if let Some(listing) = state_val.listing {
                            if listing.entries.is_empty() {
                                view! {
                                    <div class="text-center text-gray-500 p-8">
                                        <p class="text-lg">"This directory is empty"</p>
                                        <p class="text-sm mt-2">"Drop files here to upload"</p>
                                    </div>
                                }.into_view()
                            } else {
                                let on_file_select_inner = on_file_select_clone.clone();
                                let navigate_to_inner = navigate_to_clone.clone();
                                let toggle_file_selection_inner = toggle_file_selection_clone.clone();

                                view! {
                                    <FileList
                                        entries=listing.entries
                                        selected_files=selected_files
                                        on_file_click=Rc::new(move |file_info: FileInfo| {
                                            if file_info.file_type == FileType::Directory {
                                                navigate_to_inner(file_info.path);
                                            } else if let Some(callback) = &on_file_select_inner {
                                                callback(file_info);
                                            }
                                        })
                                        on_file_select=Rc::new(toggle_file_selection_inner)
                                    />
                                }.into_view()
                            }
                        } else {
                            view! {
                                <div class="text-center text-gray-500 p-8">
                                    <p>"No directory loaded"</p>
                                </div>
                            }.into_view()
                        }
                    }
                }
            </div>
            
            // Hidden file input for upload
            <input
                node_ref=file_input_ref
                type="file"
                multiple
                class="hidden"
                on:change=on_file_input_change
            />
            
            // Upload progress modal
            {
                let (show_progress, set_show_progress) = create_signal(false);
                let progress_state = create_signal(ProgressState::InProgress {
                    message: "Uploading files...".to_string(),
                    percentage: Some(0.0)
                });

                create_effect(move |_| {
                    set_show_progress.set(upload_progress.get().is_some());
                });

                view! {
                    <ProgressModal
                        open=show_progress
                        on_close=set_show_progress
                        title="Uploading Files".to_string()
                        progress=progress_state.0
                    />
                }
            }

            // Create folder modal
            {
                let (folder_name, set_folder_name) = create_signal(String::new());

                view! {
                    <Modal
                        open=show_create_folder_modal
                        on_close=set_show_create_folder_modal
                        title="Create New Folder".to_string()
                    >
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    "Folder Name"
                                </label>
                                <input
                                    type="text"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="Enter folder name"
                                    prop:value=folder_name
                                    on:input=move |ev| {
                                        let target = ev.target().unwrap();
                                        let input = target.dyn_into::<HtmlInputElement>().unwrap();
                                        set_folder_name.set(input.value());
                                    }
                                />
                            </div>
                            <div class="flex justify-end space-x-2">
                                <Button
                                    variant=ButtonVariant::Secondary
                                    on_click=Box::new({
                                        let set_show_create_folder_modal = set_show_create_folder_modal.clone();
                                        move || set_show_create_folder_modal.set(false)
                                    })
                                >
                                    "Cancel"
                                </Button>
                                <Button
                                    variant=ButtonVariant::Primary
                                    disabled=folder_name.get().trim().is_empty()
                                    on_click=Box::new({
                                        let create_folder = create_folder.clone();
                                        let folder_name = folder_name.clone();
                                        let set_folder_name = set_folder_name.clone();
                                        move || {
                                            let name = folder_name.get().trim().to_string();
                                            if !name.is_empty() {
                                                create_folder.dispatch(name);
                                                set_folder_name.set(String::new());
                                            }
                                        }
                                    })
                                >
                                    "Create"
                                </Button>
                            </div>
                        </div>
                    </Modal>
                }
            }

            // File operations modal
            {
                let (operation_type, set_operation_type) = create_signal("rename".to_string());
                let (new_name, set_new_name) = create_signal(String::new());
                let (dest_path, set_dest_path) = create_signal(String::new());

                view! {
                    <Modal
                        open=show_file_operations_modal
                        on_close=set_show_file_operations_modal
                        title="File Operations".to_string()
                    >
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    "Operation"
                                </label>
                                <select
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    on:change=move |ev| {
                                        let target = ev.target().unwrap();
                                        let select = target.dyn_into::<web_sys::HtmlSelectElement>().unwrap();
                                        set_operation_type.set(select.value());
                                    }
                                >
                                    <option value="rename">"Rename"</option>
                                    <option value="copy">"Copy"</option>
                                    <option value="move">"Move"</option>
                                </select>
                            </div>

                            {
                                let op_type = operation_type.get();
                                if op_type == "rename" {
                                    view! {
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                                "New Name"
                                            </label>
                                            <input
                                                type="text"
                                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                placeholder="Enter new name"
                                                prop:value=new_name
                                                on:input=move |ev| {
                                                    let target = ev.target().unwrap();
                                                    let input = target.dyn_into::<HtmlInputElement>().unwrap();
                                                    set_new_name.set(input.value());
                                                }
                                            />
                                        </div>
                                    }.into_view()
                                } else {
                                    view! {
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                                "Destination Path"
                                            </label>
                                            <input
                                                type="text"
                                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                placeholder="Enter destination path"
                                                prop:value=dest_path
                                                on:input=move |ev| {
                                                    let target = ev.target().unwrap();
                                                    let input = target.dyn_into::<HtmlInputElement>().unwrap();
                                                    set_dest_path.set(input.value());
                                                }
                                            />
                                        </div>
                                    }.into_view()
                                }
                            }

                            <div class="flex justify-end space-x-2">
                                <Button
                                    variant=ButtonVariant::Secondary
                                    on_click=Box::new({
                                        let set_show_file_operations_modal = set_show_file_operations_modal.clone();
                                        move || set_show_file_operations_modal.set(false)
                                    })
                                >
                                    "Cancel"
                                </Button>
                                <Button
                                    variant=ButtonVariant::Primary
                                    on_click=Box::new({
                                        let operation_type = operation_type.clone();
                                        let new_name = new_name.clone();
                                        let dest_path = dest_path.clone();
                                        let selected_files = selected_files.clone();
                                        let rename_file = rename_file.clone();
                                        let copy_file = copy_file.clone();
                                        let move_file = move_file.clone();
                                        let set_show_file_operations_modal = set_show_file_operations_modal.clone();
                                        move || {
                                            let selected = selected_files.get();
                                            if let Some(first_file) = selected.iter().next() {
                                                let op = operation_type.get();
                                                match op.as_str() {
                                                    "rename" => {
                                                        let name = new_name.get().trim().to_string();
                                                        if !name.is_empty() {
                                                            rename_file.dispatch((first_file.clone(), name));
                                                        }
                                                    }
                                                    "copy" => {
                                                        let dest = dest_path.get().trim().to_string();
                                                        if !dest.is_empty() {
                                                            copy_file.dispatch((first_file.clone(), dest));
                                                        }
                                                    }
                                                    "move" => {
                                                        let dest = dest_path.get().trim().to_string();
                                                        if !dest.is_empty() {
                                                            move_file.dispatch((first_file.clone(), dest));
                                                        }
                                                    }
                                                    _ => {}
                                                }
                                                set_show_file_operations_modal.set(false);
                                            }
                                        }
                                    })
                                >
                                    "Execute"
                                </Button>
                            </div>
                        </div>
                    </Modal>
                }
            }
        </div>
    }
}

/// File List Component
#[component]
pub fn FileList(
    /// List of file entries to display
    entries: Vec<FileInfo>,
    /// Currently selected files
    selected_files: ReadSignal<HashSet<String>>,
    /// Callback when a file is clicked
    on_file_click: Rc<dyn Fn(FileInfo)>,
    /// Callback when a file is selected/deselected
    on_file_select: Rc<dyn Fn(String)>,
) -> impl IntoView {
    let on_file_click = Rc::clone(&on_file_click);
    let on_file_select = Rc::clone(&on_file_select);

    view! {
        <div class="file-list">
            <div class="grid grid-cols-1 gap-1">
                {entries.into_iter().map(|entry| {
                    let file_path = entry.path.clone();
                    let file_path_select_check = entry.path.clone();
                    let file_path_select_change = entry.path.clone();
                    let entry_clone = entry.clone();
                    let entry_select = entry.clone();
                    let on_file_click = Rc::clone(&on_file_click);
                    let on_file_select = Rc::clone(&on_file_select);

                    view! {
                        <div class=move || format!(
                            "flex items-center p-2 rounded hover:bg-gray-50 cursor-pointer {}",
                            if selected_files.get().contains(&file_path) {
                                "bg-blue-50 border border-blue-200"
                            } else {
                                "border border-transparent"
                            }
                        )>
                            // Selection checkbox
                            <input
                                type="checkbox"
                                class="mr-3"
                                checked=move || selected_files.get().contains(&file_path_select_check)
                                on:change=move |_| on_file_select(file_path_select_change.clone())
                            />

                            // File icon
                            <div class="mr-3 text-lg">
                                {match entry.file_type {
                                    FileType::Directory => "📁",
                                    FileType::File => {
                                        match entry.mime_type.as_deref() {
                                            Some(mime) if mime.starts_with("image/") => "🖼️",
                                            Some(mime) if mime.starts_with("text/") => "📄",
                                            Some(mime) if mime.starts_with("video/") => "🎥",
                                            Some(mime) if mime.starts_with("audio/") => "🎵",
                                            _ => "📄",
                                        }
                                    },
                                    FileType::Symlink => "🔗",
                                    _ => "❓",
                                }}
                            </div>

                            // File info
                            <div
                                class="flex-1 min-w-0"
                                on:click=move |_| on_file_click(entry_clone.clone())
                            >
                                <div class="flex items-center justify-between">
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900 truncate">
                                            {entry.name.clone()}
                                        </p>
                                        <p class="text-xs text-gray-500">
                                            {format!(
                                                "{} • {}",
                                                if entry.file_type == FileType::Directory {
                                                    "Directory".to_string()
                                                } else {
                                                    human_bytes(entry.size as f64)
                                                },
                                                entry.modified.format("%Y-%m-%d %H:%M")
                                            )}
                                        </p>
                                    </div>

                                    <div class="text-xs text-gray-400 font-mono">
                                        {entry.permissions.readable.clone()}
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                }).collect::<Vec<_>>()}
            </div>
        </div>
    }
}
