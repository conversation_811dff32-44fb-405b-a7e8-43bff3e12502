//! File Browser Component
//!
//! A comprehensive file browser component for OpenWrt LuCI that provides
//! file system navigation, file operations, and file management capabilities.

use leptos::*;
use web_sys::{DragEvent, File, FileList, HtmlInputElement, DataTransfer};
use wasm_bindgen::JsCast;
use crate::types::file_browser::*;
use crate::api::file_browser::*;
use crate::components::{Modal, FormModal, ProgressModal};
use crate::components::buttons::{Button, IconButton, ButtonVariant, ButtonSize};
use human_bytes::human_bytes;
use std::collections::HashSet;

/// File Browser Component
#[component]
pub fn FileBrowser(
    /// Initial directory path
    #[prop(default = "/".to_string())]
    initial_path: String,
    /// Whether to show hidden files by default
    #[prop(default = false)]
    show_hidden_default: bool,
    /// Maximum file upload size in bytes
    #[prop(default = 100 * 1024 * 1024)] // 100MB
    max_upload_size: u64,
    /// Callback when a file is selected
    #[prop(optional)]
    on_file_select: Option<Box<dyn Fn(FileInfo)>>,
) -> impl IntoView {
    // State management
    let (state, set_state) = create_signal(FileBrowserState {
        current_path: initial_path,
        show_hidden: show_hidden_default,
        ..Default::default()
    });
    
    let (selected_files, set_selected_files) = create_signal(HashSet::<String>::new());
    let (upload_progress, set_upload_progress) = create_signal(None::<f32>);
    let (show_upload_modal, set_show_upload_modal) = create_signal(false);
    let (show_create_folder_modal, set_show_create_folder_modal) = create_signal(false);
    let (show_file_operations_modal, set_show_file_operations_modal) = create_signal(false);
    let (is_drag_over, set_is_drag_over) = create_signal(false);
    
    // API client
    let api = get_file_browser_api();
    
    // File input reference for upload
    let file_input_ref = create_node_ref::<leptos::html::Input>();
    
    // Load directory listing
    let load_directory = create_action(move |path: &String| {
        let api = api.clone();
        let path = path.clone();
        async move {
            set_state.update(|s| s.loading = true);
            
            match api.list_directory(&path).await {
                Ok(listing) => {
                    set_state.update(|s| {
                        s.listing = Some(listing);
                        s.current_path = path;
                        s.loading = false;
                        s.error = None;
                    });
                }
                Err(error) => {
                    set_state.update(|s| {
                        s.loading = false;
                        s.error = Some(error);
                    });
                }
            }
        }
    });
    
    // Initialize with current path
    create_effect(move |_| {
        let current_path = state.get().current_path;
        load_directory.dispatch(current_path);
    });
    
    // Navigation functions
    let navigate_to = move |path: String| {
        load_directory.dispatch(path);
    };
    
    let navigate_up = move |_| {
        let current = state.get().current_path;
        if let Some(parent) = std::path::Path::new(&current).parent() {
            navigate_to(parent.to_string_lossy().to_string());
        }
    };
    
    let navigate_home = move |_| {
        navigate_to("/".to_string());
    };
    
    // File selection
    let toggle_file_selection = move |file_path: String| {
        set_selected_files.update(|selected| {
            if selected.contains(&file_path) {
                selected.remove(&file_path);
            } else {
                selected.insert(file_path);
            }
        });
    };
    
    let select_all = move |_| {
        if let Some(listing) = state.get().listing {
            let all_paths: HashSet<String> = listing.entries.iter()
                .map(|entry| entry.path.clone())
                .collect();
            set_selected_files.set(all_paths);
        }
    };
    
    let clear_selection = move |_| {
        set_selected_files.set(HashSet::new());
    };
    
    // File operations
    let delete_selected = create_action(move |_: &()| {
        let api = api.clone();
        let selected = selected_files.get();
        async move {
            for file_path in selected {
                if let Err(error) = api.delete(&file_path).await {
                    log::error!("Failed to delete {}: {}", file_path, error);
                }
            }
            // Reload directory after deletion
            let current_path = state.get().current_path;
            load_directory.dispatch(current_path);
            set_selected_files.set(HashSet::new());
        }
    });
    
    // File upload handling
    let handle_file_upload = create_action(move |files: &Vec<File>| {
        let api = api.clone();
        let files = files.clone();
        let target_path = state.get().current_path;
        async move {
            set_upload_progress.set(Some(0.0));
            
            match api.upload_files(files, &target_path, false).await {
                Ok(_responses) => {
                    set_upload_progress.set(None);
                    // Reload directory after upload
                    load_directory.dispatch(target_path);
                }
                Err(error) => {
                    set_upload_progress.set(None);
                    log::error!("Upload failed: {}", error);
                }
            }
        }
    });
    
    // Drag and drop handlers
    let on_drag_over = move |ev: DragEvent| {
        ev.prevent_default();
        set_is_drag_over.set(true);
    };

    let on_drag_leave = move |_ev: DragEvent| {
        set_is_drag_over.set(false);
    };

    let on_drop = move |ev: DragEvent| {
        ev.prevent_default();
        set_is_drag_over.set(false);

        if let Some(data_transfer) = ev.data_transfer() {
            if let Some(file_list) = data_transfer.files() {
                let files: Vec<File> = (0..file_list.length())
                    .filter_map(|i| file_list.get(i))
                    .collect();
                handle_file_upload.dispatch(files);
            }
        }
    };
    
    // File input change handler
    let on_file_input_change = move |ev: web_sys::Event| {
        let input = ev.target().unwrap().dyn_into::<HtmlInputElement>().unwrap();
        if let Some(file_list) = input.files() {
            let files: Vec<File> = (0..file_list.length())
                .filter_map(|i| file_list.get(i))
                .collect();
            handle_file_upload.dispatch(files);
        }
    };
    
    view! {
        <div class="file-browser bg-white rounded-lg shadow-lg">
            // Toolbar
            <div class="flex items-center justify-between p-4 border-b border-gray-200">
                <div class="flex items-center space-x-2">
                    <IconButton
                        icon="home"
                        variant="ghost"
                        size="sm"
                        on_click=navigate_home
                        title="Home"
                    />
                    <IconButton
                        icon="arrow-up"
                        variant="ghost"
                        size="sm"
                        on_click=navigate_up
                        title="Up"
                        disabled=move || state.get().current_path == "/"
                    />
                    <span class="text-sm text-gray-600 font-mono">
                        {move || state.get().current_path}
                    </span>
                </div>
                
                <div class="flex items-center space-x-2">
                    <Button
                        variant="outline"
                        size="sm"
                        on_click=move |_| set_show_upload_modal.set(true)
                    >
                        "Upload"
                    </Button>
                    <Button
                        variant="outline"
                        size="sm"
                        on_click=move |_| set_show_create_folder_modal.set(true)
                    >
                        "New Folder"
                    </Button>
                    <Button
                        variant="outline"
                        size="sm"
                        on_click=select_all
                    >
                        "Select All"
                    </Button>
                    <Button
                        variant="outline"
                        size="sm"
                        on_click=clear_selection
                    >
                        "Clear"
                    </Button>
                    {move || {
                        let selected_count = selected_files.get().len();
                        if selected_count > 0 {
                            view! {
                                <Button
                                    variant="destructive"
                                    size="sm"
                                    on_click=move |_| delete_selected.dispatch(())
                                >
                                    {format!("Delete ({})", selected_count)}
                                </Button>
                            }.into_view()
                        } else {
                            view! {}.into_view()
                        }
                    }}
                </div>
            </div>
            
            // Drop zone and file list
            <div
                class=move || format!(
                    "min-h-96 p-4 {}",
                    if is_drag_over.get() {
                        "bg-blue-50 border-2 border-dashed border-blue-300"
                    } else {
                        "bg-white"
                    }
                )
                on:dragover=on_drag_over
                on:dragleave=on_drag_leave
                on:drop=on_drop
            >
                {move || {
                    let state_val = state.get();
                    
                    if state_val.loading {
                        view! {
                            <div class="flex items-center justify-center h-32">
                                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                <span class="ml-2 text-gray-600">"Loading..."</span>
                            </div>
                        }.into_view()
                    } else if let Some(error) = state_val.error {
                        view! {
                            <div class="text-center text-red-600 p-8">
                                <p class="text-lg font-semibold">"Error loading directory"</p>
                                <p class="text-sm mt-2">{error}</p>
                            </div>
                        }.into_view()
                    } else if let Some(listing) = state_val.listing {
                        if listing.entries.is_empty() {
                            view! {
                                <div class="text-center text-gray-500 p-8">
                                    <p class="text-lg">"This directory is empty"</p>
                                    <p class="text-sm mt-2">"Drop files here to upload"</p>
                                </div>
                            }.into_view()
                        } else {
                            view! {
                                <FileList
                                    entries=listing.entries
                                    selected_files=selected_files
                                    on_file_click=move |file_info: FileInfo| {
                                        if file_info.file_type == FileType::Directory {
                                            navigate_to(file_info.path);
                                        } else if let Some(callback) = &on_file_select {
                                            callback(file_info);
                                        }
                                    }
                                    on_file_select=toggle_file_selection
                                />
                            }.into_view()
                        }
                    } else {
                        view! {
                            <div class="text-center text-gray-500 p-8">
                                <p>"No directory loaded"</p>
                            </div>
                        }.into_view()
                    }
                }}
            </div>
            
            // Hidden file input for upload
            <input
                node_ref=file_input_ref
                type="file"
                multiple
                class="hidden"
                on:change=on_file_input_change
            />
            
            // Upload progress modal
            {move || {
                if let Some(progress) = upload_progress.get() {
                    view! {
                        <ProgressModal
                            show=true
                            title="Uploading Files"
                            progress=progress
                            on_close=move || set_upload_progress.set(None)
                        />
                    }.into_view()
                } else {
                    view! {}.into_view()
                }
            }}
        </div>
    }
}

/// File List Component
#[component]
pub fn FileList(
    /// List of file entries to display
    entries: Vec<FileInfo>,
    /// Currently selected files
    selected_files: ReadSignal<HashSet<String>>,
    /// Callback when a file is clicked
    on_file_click: Box<dyn Fn(FileInfo)>,
    /// Callback when a file is selected/deselected
    on_file_select: Box<dyn Fn(String)>,
) -> impl IntoView {
    view! {
        <div class="file-list">
            <div class="grid grid-cols-1 gap-1">
                {entries.into_iter().map(|entry| {
                    let file_path = entry.path.clone();
                    let file_path_select = entry.path.clone();
                    let entry_clone = entry.clone();
                    let entry_select = entry.clone();

                    view! {
                        <div class=move || format!(
                            "flex items-center p-2 rounded hover:bg-gray-50 cursor-pointer {}",
                            if selected_files.get().contains(&file_path) {
                                "bg-blue-50 border border-blue-200"
                            } else {
                                "border border-transparent"
                            }
                        )>
                            // Selection checkbox
                            <input
                                type="checkbox"
                                class="mr-3"
                                checked=move || selected_files.get().contains(&file_path_select)
                                on:change=move |_| on_file_select(file_path_select.clone())
                            />

                            // File icon
                            <div class="mr-3 text-lg">
                                {match entry.file_type {
                                    FileType::Directory => "📁",
                                    FileType::File => {
                                        match entry.mime_type.as_deref() {
                                            Some(mime) if mime.starts_with("image/") => "🖼️",
                                            Some(mime) if mime.starts_with("text/") => "📄",
                                            Some(mime) if mime.starts_with("video/") => "🎥",
                                            Some(mime) if mime.starts_with("audio/") => "🎵",
                                            _ => "📄",
                                        }
                                    },
                                    FileType::Symlink => "🔗",
                                    _ => "❓",
                                }}
                            </div>

                            // File info
                            <div
                                class="flex-1 min-w-0"
                                on:click=move |_| on_file_click(entry_clone.clone())
                            >
                                <div class="flex items-center justify-between">
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900 truncate">
                                            {entry.name.clone()}
                                        </p>
                                        <p class="text-xs text-gray-500">
                                            {format!(
                                                "{} • {}",
                                                if entry.file_type == FileType::Directory {
                                                    "Directory".to_string()
                                                } else {
                                                    human_bytes(entry.size as f64)
                                                },
                                                entry.modified.format("%Y-%m-%d %H:%M")
                                            )}
                                        </p>
                                    </div>

                                    <div class="text-xs text-gray-400 font-mono">
                                        {entry.permissions.readable.clone()}
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                }).collect::<Vec<_>>()}
            </div>
        </div>
    }
}
