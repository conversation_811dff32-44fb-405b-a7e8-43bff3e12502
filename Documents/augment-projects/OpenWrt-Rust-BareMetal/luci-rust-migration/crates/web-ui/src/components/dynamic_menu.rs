use leptos::*;
use leptos_router::*;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use crate::components::navigation::{NavItem, NavigationState};

/// Service availability status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceStatus {
    pub name: String,
    pub available: bool,
    pub version: Option<String>,
    pub description: Option<String>,
    pub config_path: Option<String>,
}

/// User permission level
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum PermissionLevel {
    Guest,
    User,
    Admin,
    Root,
}

/// User context for menu generation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserContext {
    pub username: String,
    pub permissions: Vec<String>,
    pub permission_level: PermissionLevel,
    pub groups: Vec<String>,
}

/// Menu configuration for dynamic loading
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MenuConfig {
    pub id: String,
    pub label: String,
    pub path: String,
    pub icon: Option<String>,
    pub required_services: Vec<String>,
    pub required_permissions: Vec<String>,
    pub min_permission_level: PermissionLevel,
    pub children: Vec<MenuConfig>,
    pub order: i32,
    pub category: String,
}

impl MenuConfig {
    pub fn new(id: &str, label: &str, path: &str) -> Self {
        Self {
            id: id.to_string(),
            label: label.to_string(),
            path: path.to_string(),
            icon: None,
            required_services: Vec::new(),
            required_permissions: Vec::new(),
            min_permission_level: PermissionLevel::Guest,
            children: Vec::new(),
            order: 0,
            category: "general".to_string(),
        }
    }
    
    pub fn with_icon(mut self, icon: &str) -> Self {
        self.icon = Some(icon.to_string());
        self
    }
    
    pub fn with_services(mut self, services: Vec<String>) -> Self {
        self.required_services = services;
        self
    }
    
    pub fn with_permissions(mut self, permissions: Vec<String>) -> Self {
        self.required_permissions = permissions;
        self
    }
    
    pub fn with_min_level(mut self, level: PermissionLevel) -> Self {
        self.min_permission_level = level;
        self
    }
    
    pub fn with_children(mut self, children: Vec<MenuConfig>) -> Self {
        self.children = children;
        self
    }
    
    pub fn with_order(mut self, order: i32) -> Self {
        self.order = order;
        self
    }
    
    pub fn with_category(mut self, category: &str) -> Self {
        self.category = category.to_string();
        self
    }
}

/// Dynamic menu loader component
#[component]
pub fn DynamicMenuLoader(
    #[prop(optional)] class: Option<String>,
    #[prop(optional)] on_menu_loaded: Option<Box<dyn Fn(Vec<NavItem>)>>,
) -> impl IntoView {
    let (menu_items, set_menu_items) = create_signal(Vec::<NavItem>::new());
    let (loading, set_loading) = create_signal(true);
    let (error, set_error) = create_signal(None::<String>);
    
    // Load menu configuration on mount
    create_effect(move |_| {
        spawn_local(async move {
            set_loading.set(true);
            set_error.set(None);
            
            match load_dynamic_menu().await {
                Ok(items) => {
                    set_menu_items.set(items.clone());
                    if let Some(handler) = &on_menu_loaded {
                        handler(items);
                    }
                }
                Err(e) => {
                    set_error.set(Some(e));
                }
            }
            
            set_loading.set(false);
        });
    });
    
    view! {
        <div class=format!("dynamic-menu-loader {}", class.unwrap_or_default())>
            {move || {
                if loading.get() {
                    view! {
                        <div class="flex items-center justify-center p-4">
                            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                            <span class="ml-2 text-sm text-gray-600">Loading menu...</span>
                        </div>
                    }.into_view()
                } else if let Some(err) = error.get() {
                    view! {
                        <div class="p-4 bg-red-50 border border-red-200 rounded-md">
                            <div class="flex">
                                <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                                </svg>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-red-800">Menu Loading Error</h3>
                                    <p class="mt-1 text-sm text-red-700">{err}</p>
                                </div>
                            </div>
                        </div>
                    }.into_view()
                } else {
                    view! {
                        <div class="menu-loaded">
                            <span class="text-xs text-gray-500">
                                {format!("{} menu items loaded", menu_items.get().len())}
                            </span>
                        </div>
                    }.into_view()
                }
            }}
        </div>
    }
}

/// Service-aware menu component
#[component]
pub fn ServiceAwareMenu(
    services: ReadSignal<Vec<ServiceStatus>>,
    user_context: ReadSignal<UserContext>,
    #[prop(optional)] class: Option<String>,
) -> impl IntoView {
    let (filtered_menu, set_filtered_menu) = create_signal(Vec::<NavItem>::new());
    
    // Update menu when services or user context changes
    create_effect(move |_| {
        let available_services = services.get();
        let user = user_context.get();
        
        spawn_local(async move {
            match generate_filtered_menu(&available_services, &user).await {
                Ok(menu) => set_filtered_menu.set(menu),
                Err(e) => {
                    log::error!("Failed to generate filtered menu: {}", e);
                }
            }
        });
    });
    
    view! {
        <div class=format!("service-aware-menu {}", class.unwrap_or_default())>
            <crate::components::navigation::HierarchicalNavigation 
                nav_items=filtered_menu
            />
        </div>
    }
}

/// Permission-based menu filter component
#[component]
pub fn PermissionBasedMenu(
    base_menu: ReadSignal<Vec<NavItem>>,
    user_permissions: ReadSignal<Vec<String>>,
    #[prop(optional)] class: Option<String>,
) -> impl IntoView {
    let filtered_menu = create_memo(move |_| {
        filter_menu_by_permissions(&base_menu.get(), &user_permissions.get())
    });
    
    view! {
        <div class=format!("permission-based-menu {}", class.unwrap_or_default())>
            <crate::components::navigation::HierarchicalNavigation 
                nav_items=filtered_menu
            />
        </div>
    }
}

/// Load dynamic menu configuration
async fn load_dynamic_menu() -> Result<Vec<NavItem>, String> {
    // In a real implementation, this would fetch from the server
    // For now, we'll return a default menu structure
    
    let menu_configs = get_default_menu_configs();
    let services = get_available_services().await?;
    let user_context = get_current_user_context().await?;
    
    generate_filtered_menu(&services, &user_context).await
}

/// Generate filtered menu based on services and user context
async fn generate_filtered_menu(
    services: &[ServiceStatus],
    user_context: &UserContext,
) -> Result<Vec<NavItem>, String> {
    let menu_configs = get_default_menu_configs();
    let mut nav_items = Vec::new();
    
    for config in menu_configs {
        if should_include_menu_item(&config, services, user_context) {
            let nav_item = convert_menu_config_to_nav_item(&config, services, user_context);
            nav_items.push(nav_item);
        }
    }
    
    // Sort by order and category
    nav_items.sort_by(|a, b| {
        // Extract order from description or use default
        let order_a = extract_order_from_nav_item(a);
        let order_b = extract_order_from_nav_item(b);
        order_a.cmp(&order_b)
    });
    
    Ok(nav_items)
}

/// Check if menu item should be included based on services and permissions
fn should_include_menu_item(
    config: &MenuConfig,
    services: &[ServiceStatus],
    user_context: &UserContext,
) -> bool {
    // Check permission level
    if !has_required_permission_level(&user_context.permission_level, &config.min_permission_level) {
        return false;
    }
    
    // Check specific permissions
    if !config.required_permissions.is_empty() {
        let has_permission = config.required_permissions.iter()
            .any(|perm| user_context.permissions.contains(perm));
        if !has_permission {
            return false;
        }
    }
    
    // Check required services
    if !config.required_services.is_empty() {
        let has_services = config.required_services.iter()
            .all(|service| services.iter().any(|s| s.name == *service && s.available));
        if !has_services {
            return false;
        }
    }
    
    true
}

/// Convert menu config to navigation item
fn convert_menu_config_to_nav_item(
    config: &MenuConfig,
    services: &[ServiceStatus],
    user_context: &UserContext,
) -> NavItem {
    let mut nav_item = NavItem::new(&config.id, &config.label, &config.path);
    
    if let Some(icon) = &config.icon {
        nav_item = nav_item.with_icon(icon);
    }
    
    // Add children recursively
    let mut children = Vec::new();
    for child_config in &config.children {
        if should_include_menu_item(child_config, services, user_context) {
            children.push(convert_menu_config_to_nav_item(child_config, services, user_context));
        }
    }
    
    if !children.is_empty() {
        nav_item = nav_item.with_children(children);
    }
    
    nav_item
}

/// Filter menu by user permissions
fn filter_menu_by_permissions(menu: &[NavItem], permissions: &[String]) -> Vec<NavItem> {
    menu.iter()
        .filter_map(|item| {
            // Check if user has required permissions for this item
            if item.permissions.is_empty() ||
               item.permissions.iter().any(|perm| permissions.contains(perm)) {

                let mut filtered_item = item.clone();

                // Recursively filter children
                if !item.children.is_empty() {
                    filtered_item.children = filter_menu_by_permissions(&item.children, permissions);
                }

                Some(filtered_item)
            } else {
                None
            }
        })
        .collect()
}

/// Check if user has required permission level
fn has_required_permission_level(user_level: &PermissionLevel, required_level: &PermissionLevel) -> bool {
    match (user_level, required_level) {
        (PermissionLevel::Root, _) => true,
        (PermissionLevel::Admin, PermissionLevel::Root) => false,
        (PermissionLevel::Admin, _) => true,
        (PermissionLevel::User, PermissionLevel::Admin | PermissionLevel::Root) => false,
        (PermissionLevel::User, _) => true,
        (PermissionLevel::Guest, PermissionLevel::Guest) => true,
        (PermissionLevel::Guest, _) => false,
    }
}

/// Extract order from navigation item (from description or default)
fn extract_order_from_nav_item(item: &NavItem) -> i32 {
    // In a real implementation, this could parse order from description
    // For now, return default order based on path
    match item.path.as_str() {
        "/" => 0,
        "/status" => 10,
        "/network" => 20,
        "/system" => 30,
        "/services" => 40,
        "/administration" => 50,
        _ => 100,
    }
}

/// Get default menu configurations
fn get_default_menu_configs() -> Vec<MenuConfig> {
    vec![
        MenuConfig::new("status", "Status", "/status")
            .with_icon("📊")
            .with_order(10)
            .with_category("monitoring")
            .with_children(vec![
                MenuConfig::new("overview", "Overview", "/status/overview")
                    .with_icon("🏠")
                    .with_order(1),
                MenuConfig::new("processes", "Processes", "/status/processes")
                    .with_icon("⚙️")
                    .with_order(2),
                MenuConfig::new("realtime", "Realtime Graphs", "/status/realtime")
                    .with_icon("📈")
                    .with_order(3),
            ]),

        MenuConfig::new("network", "Network", "/network")
            .with_icon("🌐")
            .with_order(20)
            .with_category("network")
            .with_children(vec![
                MenuConfig::new("interfaces", "Interfaces", "/network/interfaces")
                    .with_icon("🔌")
                    .with_order(1),
                MenuConfig::new("wireless", "Wireless", "/network/wireless")
                    .with_icon("📶")
                    .with_order(2)
                    .with_services(vec!["hostapd".to_string(), "wpa_supplicant".to_string()]),
                MenuConfig::new("dhcp", "DHCP and DNS", "/network/dhcp")
                    .with_icon("🏷️")
                    .with_order(3)
                    .with_services(vec!["dnsmasq".to_string()]),
                MenuConfig::new("firewall", "Firewall", "/network/firewall")
                    .with_icon("🛡️")
                    .with_order(4)
                    .with_services(vec!["iptables".to_string()])
                    .with_min_level(PermissionLevel::Admin),
            ]),

        MenuConfig::new("system", "System", "/system")
            .with_icon("⚙️")
            .with_order(30)
            .with_category("system")
            .with_min_level(PermissionLevel::Admin)
            .with_children(vec![
                MenuConfig::new("system_general", "General", "/system/general")
                    .with_icon("🔧")
                    .with_order(1),
                MenuConfig::new("administration", "Administration", "/system/admin")
                    .with_icon("👤")
                    .with_order(2),
                MenuConfig::new("software", "Software", "/system/software")
                    .with_icon("📦")
                    .with_order(3),
                MenuConfig::new("startup", "Startup", "/system/startup")
                    .with_icon("🚀")
                    .with_order(4),
                MenuConfig::new("cron", "Scheduled Tasks", "/system/cron")
                    .with_icon("⏰")
                    .with_order(5)
                    .with_services(vec!["cron".to_string()]),
            ]),

        MenuConfig::new("services", "Services", "/services")
            .with_icon("🔧")
            .with_order(40)
            .with_category("services")
            .with_min_level(PermissionLevel::User)
            .with_children(vec![
                MenuConfig::new("openvpn", "OpenVPN", "/services/openvpn")
                    .with_icon("🔒")
                    .with_order(1)
                    .with_services(vec!["openvpn".to_string()]),
                MenuConfig::new("ddns", "Dynamic DNS", "/services/ddns")
                    .with_icon("🌍")
                    .with_order(2)
                    .with_services(vec!["ddns-scripts".to_string()]),
                MenuConfig::new("upnp", "UPnP", "/services/upnp")
                    .with_icon("🔄")
                    .with_order(3)
                    .with_services(vec!["miniupnpd".to_string()]),
            ]),
    ]
}

/// Get available services (mock implementation)
async fn get_available_services() -> Result<Vec<ServiceStatus>, String> {
    // In a real implementation, this would query the system for available services
    Ok(vec![
        ServiceStatus {
            name: "hostapd".to_string(),
            available: true,
            version: Some("2.9".to_string()),
            description: Some("IEEE 802.11 AP, IEEE 802.1X/WPA/WPA2/EAP/RADIUS Authenticator".to_string()),
            config_path: Some("/etc/hostapd/hostapd.conf".to_string()),
        },
        ServiceStatus {
            name: "dnsmasq".to_string(),
            available: true,
            version: Some("2.85".to_string()),
            description: Some("DNS forwarder and DHCP server".to_string()),
            config_path: Some("/etc/dnsmasq.conf".to_string()),
        },
        ServiceStatus {
            name: "iptables".to_string(),
            available: true,
            version: Some("1.8.7".to_string()),
            description: Some("Administration tool for IPv4/IPv6 packet filtering and NAT".to_string()),
            config_path: None,
        },
        ServiceStatus {
            name: "openvpn".to_string(),
            available: false,
            version: None,
            description: Some("Open source VPN daemon".to_string()),
            config_path: Some("/etc/openvpn/".to_string()),
        },
    ])
}

/// Get current user context (mock implementation)
async fn get_current_user_context() -> Result<UserContext, String> {
    // In a real implementation, this would get the current user's context from session/auth
    Ok(UserContext {
        username: "admin".to_string(),
        permissions: vec![
            "network.read".to_string(),
            "network.write".to_string(),
            "system.read".to_string(),
            "system.write".to_string(),
        ],
        permission_level: PermissionLevel::Admin,
        groups: vec!["administrators".to_string()],
    })
}
