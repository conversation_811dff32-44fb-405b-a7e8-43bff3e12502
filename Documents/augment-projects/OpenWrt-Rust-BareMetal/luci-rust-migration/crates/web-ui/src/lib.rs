//! LuCI Web UI - Leptos-based web interface for OpenWrt
//! 
//! This crate provides the main web interface using Leptos framework
//! with server-side rendering (SSR) only for embedded compatibility.

use leptos::*;
use leptos_meta::*;
use leptos_router::*;
use luci_shared_types::*;
use serde::{Serialize, Deserialize};
// use luci_utilities::*; // TODO: Create utilities crate
use log::{info, error, debug};

pub mod components;
pub mod pages;
pub mod api;
pub mod auth;
pub mod utils;
pub mod types;
pub mod plugins;

use components::*;
use pages::*;
use auth::AuthProvider;

/// Main application component
#[component]
pub fn App() -> impl IntoView {
    // Provides context that manages stylesheets, titles, meta tags, etc.
    provide_meta_context();

    view! {
        <Stylesheet id="leptos" href="/pkg/luci-web-ui.css"/>
        <Stylesheet id="tailwind" href="/main.css"/>
        <Title text="OpenWrt - LuCI"/>
        <Meta name="viewport" content="width=device-width, initial-scale=1.0"/>
        <Meta name="description" content="OpenWrt LuCI Web Interface"/>

        <Router>
            <AuthProvider>
                <div class="min-h-screen bg-gray-50">
                    <Header/>
                    <main class="container mx-auto px-4 py-6">
                        <Routes>
                            <Route path="" view=HomePage/>
                            <Route path="/login" view=LoginPage/>
                            <Route path="/profile" view=ProfilePage/>
                            <Route path="/system" view=SystemPage/>
                            <Route path="/network" view=NetworkPage/>
                            <Route path="/wireless" view=WirelessPage/>
                            <Route path="/firewall" view=FirewallPage/>
                            <Route path="/services" view=ServicesPage/>
                            <Route path="/vpn" view=VpnPage/>
                            <Route path="/packages" view=PackagesPage/>
                            <Route path="/packages/enhanced" view=EnhancedPackagesPage/>
                            <Route path="/administration" view=AdministrationPage/>
                            <Route path="/status" view=StatusPage/>
                            <Route path="/system/dashboard" view=SystemDashboardPage/>
                            <Route path="/processes" view=ProcessesPage/>
                            <Route path="/logout" view=LogoutPage/>
                            <Route path="/*any" view=Not_foundPage/>
                        </Routes>
                    </main>
                    <Footer/>
                </div>
            </AuthProvider>
        </Router>
    }
}

/// Application configuration
#[derive(Debug, Clone)]
pub struct AppConfig {
    pub server_addr: String,
    pub server_port: u16,
    pub session_timeout: u64,
    pub csrf_protection: bool,
    pub debug_mode: bool,
    pub embedded_mode: bool,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            server_addr: "0.0.0.0".to_string(),
            server_port: 3000,
            session_timeout: 3600, // 1 hour
            csrf_protection: true,
            debug_mode: false,
            embedded_mode: std::env::var("EMBEDDED_MODE").unwrap_or_default() == "true",
        }
    }
}

/// Initialize the application with configuration
pub fn init_app(config: AppConfig) -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging
    if config.debug_mode {
        env_logger::Builder::from_env(env_logger::Env::default().default_filter_or("debug")).init();
    } else {
        env_logger::Builder::from_env(env_logger::Env::default().default_filter_or("info")).init();
    }

    info!("Initializing LuCI Web UI");
    info!("Server address: {}:{}", config.server_addr, config.server_port);
    info!("Embedded mode: {}", config.embedded_mode);
    info!("CSRF protection: {}", config.csrf_protection);

    // Validate configuration
    if config.server_port == 0 {
        return Err("Invalid server port".into());
    }

    if config.session_timeout == 0 {
        return Err("Invalid session timeout".into());
    }

    debug!("Application configuration validated");
    Ok(())
}

/// Get application version information
pub fn get_version_info() -> VersionInfo {
    VersionInfo {
        version: env!("CARGO_PKG_VERSION").to_string(),
        build_date: std::env::var("BUILD_DATE").unwrap_or_else(|_| "unknown".to_string()),
        git_hash: std::env::var("GIT_HASH").unwrap_or_else(|_| "unknown".to_string()),
        target: std::env::var("TARGET").unwrap_or_else(|_| "unknown".to_string()),
        features: get_enabled_features(),
    }
}

/// Get list of enabled features
fn get_enabled_features() -> Vec<String> {
    let mut features = Vec::new();
    
    #[cfg(feature = "embedded")]
    features.push("embedded".to_string());
    
    #[cfg(feature = "async")]
    features.push("async".to_string());
    
    #[cfg(feature = "tls")]
    features.push("tls".to_string());
    
    #[cfg(debug_assertions)]
    features.push("debug".to_string());
    
    features
}

/// Version information structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VersionInfo {
    pub version: String,
    pub build_date: String,
    pub git_hash: String,
    pub target: String,
    pub features: Vec<String>,
}

/// Application state management
#[derive(Debug, Clone)]
pub struct AppState {
    pub config: AppConfig,
    pub version: VersionInfo,
    pub start_time: std::time::SystemTime,
}

impl AppState {
    pub fn new(config: AppConfig) -> Self {
        Self {
            version: get_version_info(),
            start_time: std::time::SystemTime::now(),
            config,
        }
    }

    pub fn uptime(&self) -> std::time::Duration {
        self.start_time.elapsed().unwrap_or_default()
    }
}

/// Error handling for the web application
#[derive(Debug, Clone, thiserror::Error)]
pub enum WebError {
    #[error("Authentication failed: {message}")]
    Authentication { message: String },
    
    #[error("Authorization failed: {message}")]
    Authorization { message: String },
    
    #[error("Configuration error: {message}")]
    Configuration { message: String },
    
    #[error("System error: {message}")]
    System { message: String },
    
    #[error("Network error: {message}")]
    Network { message: String },
    
    #[error("Validation error: {message}")]
    Validation { message: String },
    
    #[error("Internal server error: {message}")]
    Internal { message: String },
}

// TODO: Implement From<LuciError> for WebError when LuciError is defined in shared-types

/// Result type for web operations
pub type WebResult<T> = Result<T, WebError>;

/// Utility functions for the web interface
pub mod web_utils {
    use super::*;
    use leptos::*;

    /// Format bytes for display in the web interface
    pub fn format_bytes(bytes: u64) -> String {
        const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
        let mut size = bytes as f64;
        let mut unit_index = 0;

        while size >= 1024.0 && unit_index < UNITS.len() - 1 {
            size /= 1024.0;
            unit_index += 1;
        }

        if unit_index == 0 {
            format!("{} {}", bytes, UNITS[unit_index])
        } else {
            format!("{:.1} {}", size, UNITS[unit_index])
        }
    }

    /// Format duration for display
    pub fn format_duration(duration: std::time::Duration) -> String {
        let total_seconds = duration.as_secs();
        let days = total_seconds / 86400;
        let hours = (total_seconds % 86400) / 3600;
        let minutes = (total_seconds % 3600) / 60;
        let seconds = total_seconds % 60;

        if days > 0 {
            format!("{}d {}h {}m {}s", days, hours, minutes, seconds)
        } else if hours > 0 {
            format!("{}h {}m {}s", hours, minutes, seconds)
        } else if minutes > 0 {
            format!("{}m {}s", minutes, seconds)
        } else {
            format!("{}s", seconds)
        }
    }

    /// Format percentage for display
    pub fn format_percentage(value: f32) -> String {
        format!("{:.1}%", value)
    }

    /// Get CSS class for status indicators
    pub fn status_class(status: &str) -> &'static str {
        match status.to_lowercase().as_str() {
            "up" | "online" | "connected" | "active" | "running" => "text-green-600",
            "down" | "offline" | "disconnected" | "inactive" | "stopped" => "text-red-600",
            "warning" | "degraded" | "limited" => "text-yellow-600",
            "unknown" | "pending" | "connecting" => "text-gray-600",
            _ => "text-gray-500",
        }
    }

    /// Get CSS class for progress bars
    pub fn progress_class(percentage: f32) -> &'static str {
        if percentage < 50.0 {
            "bg-green-500"
        } else if percentage < 80.0 {
            "bg-yellow-500"
        } else {
            "bg-red-500"
        }
    }
}

// Re-export commonly used items
pub use web_utils::*;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_app_config_default() {
        let config = AppConfig::default();
        assert_eq!(config.server_addr, "0.0.0.0");
        assert_eq!(config.server_port, 3000);
        assert_eq!(config.session_timeout, 3600);
        assert!(config.csrf_protection);
    }

    #[test]
    fn test_format_bytes() {
        assert_eq!(web_utils::format_bytes(1024), "1.0 KB");
        assert_eq!(web_utils::format_bytes(1048576), "1.0 MB");
        assert_eq!(web_utils::format_bytes(512), "512 B");
    }

    #[test]
    fn test_status_class() {
        assert_eq!(web_utils::status_class("up"), "text-green-600");
        assert_eq!(web_utils::status_class("down"), "text-red-600");
        assert_eq!(web_utils::status_class("warning"), "text-yellow-600");
        assert_eq!(web_utils::status_class("unknown"), "text-gray-600");
    }

    #[test]
    fn test_progress_class() {
        assert_eq!(web_utils::progress_class(30.0), "bg-green-500");
        assert_eq!(web_utils::progress_class(60.0), "bg-yellow-500");
        assert_eq!(web_utils::progress_class(90.0), "bg-red-500");
    }
}
