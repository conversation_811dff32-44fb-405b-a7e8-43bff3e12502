use leptos::*;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::rc::Rc;

/// Plugin trait that all plugins must implement
pub trait Plugin: Send + Sync + std::fmt::Debug {
    /// Get plugin metadata
    fn metadata(&self) -> &PluginMetadata;
    
    /// Initialize the plugin
    fn initialize(&mut self) -> Result<(), PluginError>;
    
    /// Shutdown the plugin
    fn shutdown(&mut self) -> Result<(), PluginError>;
    
    /// Get plugin configuration schema
    fn config_schema(&self) -> Option<serde_json::Value> {
        None
    }
    
    /// Configure the plugin
    fn configure(&mut self, config: serde_json::Value) -> Result<(), PluginError> {
        let _ = config;
        Ok(())
    }
    
    /// Get plugin status
    fn status(&self) -> PluginStatus;
    
    /// Handle plugin-specific actions
    fn handle_action(&mut self, action: &str, params: serde_json::Value) -> Result<serde_json::Value, PluginError> {
        let _ = (action, params);
        Err(PluginError::ActionNotSupported(action.to_string()))
    }
}

/// Plugin metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginMetadata {
    pub id: String,
    pub name: String,
    pub version: String,
    pub description: String,
    pub author: String,
    pub license: String,
    pub homepage: Option<String>,
    pub repository: Option<String>,
    pub dependencies: Vec<String>,
    pub permissions: Vec<String>,
    pub categories: Vec<String>,
    pub min_luci_version: String,
    pub max_luci_version: Option<String>,
}

/// Plugin status
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum PluginStatus {
    Unloaded,
    Loading,
    Active,
    Inactive,
    Error(String),
}

/// Plugin error types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PluginError {
    InitializationFailed(String),
    ConfigurationError(String),
    DependencyMissing(String),
    PermissionDenied(String),
    ActionNotSupported(String),
    InvalidConfiguration(String),
    RuntimeError(String),
}

impl std::fmt::Display for PluginError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            PluginError::InitializationFailed(msg) => write!(f, "Initialization failed: {}", msg),
            PluginError::ConfigurationError(msg) => write!(f, "Configuration error: {}", msg),
            PluginError::DependencyMissing(dep) => write!(f, "Missing dependency: {}", dep),
            PluginError::PermissionDenied(perm) => write!(f, "Permission denied: {}", perm),
            PluginError::ActionNotSupported(action) => write!(f, "Action not supported: {}", action),
            PluginError::InvalidConfiguration(msg) => write!(f, "Invalid configuration: {}", msg),
            PluginError::RuntimeError(msg) => write!(f, "Runtime error: {}", msg),
        }
    }
}

impl std::error::Error for PluginError {}

/// Plugin registry for managing loaded plugins
#[derive(Debug, Clone)]
pub struct PluginRegistry {
    plugins: HashMap<String, Rc<dyn Plugin>>,
    plugin_configs: HashMap<String, serde_json::Value>,
    load_order: Vec<String>,
}

impl PluginRegistry {
    pub fn new() -> Self {
        Self {
            plugins: HashMap::new(),
            plugin_configs: HashMap::new(),
            load_order: Vec::new(),
        }
    }
    
    /// Register a plugin
    pub fn register_plugin(&mut self, plugin: Rc<dyn Plugin>) -> Result<(), PluginError> {
        let metadata = plugin.metadata();
        let plugin_id = metadata.id.clone();
        
        // Check dependencies
        for dep in &metadata.dependencies {
            if !self.plugins.contains_key(dep) {
                return Err(PluginError::DependencyMissing(dep.clone()));
            }
        }
        
        self.plugins.insert(plugin_id.clone(), plugin);
        self.load_order.push(plugin_id);
        
        Ok(())
    }
    
    /// Get plugin by ID
    pub fn get_plugin(&self, id: &str) -> Option<&Rc<dyn Plugin>> {
        self.plugins.get(id)
    }
    
    /// Get all plugins
    pub fn get_all_plugins(&self) -> &HashMap<String, Rc<dyn Plugin>> {
        &self.plugins
    }
    
    /// Get plugin metadata
    pub fn get_plugin_metadata(&self, id: &str) -> Option<&PluginMetadata> {
        self.plugins.get(id).map(|p| p.metadata())
    }
    
    /// Configure plugin
    pub fn configure_plugin(&mut self, id: &str, config: serde_json::Value) -> Result<(), PluginError> {
        if let Some(plugin) = self.plugins.get_mut(id) {
            // Note: This is a simplified approach. In a real implementation,
            // we'd need interior mutability or a different architecture
            self.plugin_configs.insert(id.to_string(), config);
            Ok(())
        } else {
            Err(PluginError::RuntimeError(format!("Plugin not found: {}", id)))
        }
    }
    
    /// Get plugin configuration
    pub fn get_plugin_config(&self, id: &str) -> Option<&serde_json::Value> {
        self.plugin_configs.get(id)
    }
    
    /// List all plugin IDs
    pub fn list_plugins(&self) -> Vec<String> {
        self.load_order.clone()
    }
}

/// Plugin manager component
#[component]
pub fn PluginManager(
    #[prop(optional)] class: Option<String>,
) -> impl IntoView {
    let (registry, set_registry) = create_signal(PluginRegistry::new());
    let (selected_plugin, set_selected_plugin) = create_signal(None::<String>);
    let (loading, set_loading) = create_signal(false);
    let (error, set_error) = create_signal(None::<String>);
    
    // Load plugins on mount
    create_effect(move |_| {
        spawn_local(async move {
            set_loading.set(true);
            set_error.set(None);
            
            match load_available_plugins().await {
                Ok(mut new_registry) => {
                    // Initialize plugins
                    for plugin_id in new_registry.list_plugins() {
                        if let Some(plugin) = new_registry.get_plugin(&plugin_id) {
                            // In a real implementation, we'd handle initialization properly
                            log::info!("Plugin loaded: {}", plugin.metadata().name);
                        }
                    }
                    set_registry.set(new_registry);
                }
                Err(e) => {
                    set_error.set(Some(e));
                }
            }
            
            set_loading.set(false);
        });
    });
    
    view! {
        <div class=format!("plugin-manager {}", class.unwrap_or_default())>
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Plugin Manager</h2>
                    <p class="mt-1 text-sm text-gray-600">Manage and configure system plugins</p>
                </div>
                
                <div class="p-6">
                    {move || {
                        if loading.get() {
                            view! {
                                <div class="flex items-center justify-center py-8">
                                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                    <span class="ml-3 text-gray-600">Loading plugins...</span>
                                </div>
                            }.into_view()
                        } else if let Some(err) = error.get() {
                            view! {
                                <div class="bg-red-50 border border-red-200 rounded-md p-4">
                                    <div class="flex">
                                        <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                                        </svg>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-red-800">Plugin Loading Error</h3>
                                            <p class="mt-1 text-sm text-red-700">{err}</p>
                                        </div>
                                    </div>
                                </div>
                            }.into_view()
                        } else {
                            view! {
                                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                                    // Plugin list
                                    <div class="lg:col-span-2">
                                        <PluginList 
                                            registry=registry
                                            selected_plugin=selected_plugin
                                            on_select=move |id: String| set_selected_plugin.set(Some(id))
                                        />
                                    </div>
                                    
                                    // Plugin details
                                    <div>
                                        <PluginDetails 
                                            registry=registry
                                            selected_plugin=selected_plugin
                                        />
                                    </div>
                                </div>
                            }.into_view()
                        }
                    }}
                </div>
            </div>
        </div>
    }
}

/// Plugin list component
#[component]
fn PluginList(
    registry: ReadSignal<PluginRegistry>,
    selected_plugin: ReadSignal<Option<String>>,
    on_select: impl Fn(String) + 'static + Copy,
) -> impl IntoView {
    view! {
        <div class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900">Installed Plugins</h3>
            
            <div class="space-y-2">
                {move || {
                    let reg = registry.get();
                    let plugins = reg.list_plugins();
                    
                    if plugins.is_empty() {
                        view! {
                            <div class="text-center py-8 text-gray-500">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
                                </svg>
                                <p class="mt-2">No plugins installed</p>
                            </div>
                        }.into_view()
                    } else {
                        plugins.into_iter().map(|plugin_id| {
                            let plugin_id_clone = plugin_id.clone();
                            let metadata = reg.get_plugin_metadata(&plugin_id).unwrap();
                            let is_selected = selected_plugin.get().as_ref() == Some(&plugin_id);
                            
                            view! {
                                <div
                                    class=move || format!("p-4 border rounded-lg cursor-pointer transition-colors {}",
                                        if is_selected {
                                            "border-blue-500 bg-blue-50"
                                        } else {
                                            "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                                        }
                                    )
                                    on:click=move |_| on_select(plugin_id_clone.clone())
                                >
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1">
                                            <h4 class="text-sm font-medium text-gray-900">{metadata.name.clone()}</h4>
                                            <p class="mt-1 text-sm text-gray-600">{metadata.description.clone()}</p>
                                            <div class="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                                                <span>v{metadata.version.clone()}</span>
                                                <span>by {metadata.author.clone()}</span>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                Active
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            }
                        }).collect_view()
                    }
                }}
            </div>
        </div>
    }
}

/// Plugin details component
#[component]
fn PluginDetails(
    registry: ReadSignal<PluginRegistry>,
    selected_plugin: ReadSignal<Option<String>>,
) -> impl IntoView {
    view! {
        <div class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900">Plugin Details</h3>

            {move || {
                if let Some(plugin_id) = selected_plugin.get() {
                    let reg = registry.get();
                    if let Some(metadata) = reg.get_plugin_metadata(&plugin_id) {
                        view! {
                            <div class="bg-gray-50 rounded-lg p-4 space-y-4">
                                <div>
                                    <h4 class="text-sm font-medium text-gray-900">{metadata.name.clone()}</h4>
                                    <p class="mt-1 text-sm text-gray-600">{metadata.description.clone()}</p>
                                </div>

                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span class="font-medium text-gray-700">Version:</span>
                                        <span class="ml-2 text-gray-600">{metadata.version.clone()}</span>
                                    </div>
                                    <div>
                                        <span class="font-medium text-gray-700">Author:</span>
                                        <span class="ml-2 text-gray-600">{metadata.author.clone()}</span>
                                    </div>
                                    <div>
                                        <span class="font-medium text-gray-700">License:</span>
                                        <span class="ml-2 text-gray-600">{metadata.license.clone()}</span>
                                    </div>
                                    <div>
                                        <span class="font-medium text-gray-700">Status:</span>
                                        <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Active
                                        </span>
                                    </div>
                                </div>

                                {if !metadata.categories.is_empty() {
                                    view! {
                                        <div>
                                            <span class="text-sm font-medium text-gray-700">Categories:</span>
                                            <div class="mt-1 flex flex-wrap gap-1">
                                                {metadata.categories.iter().map(|cat| {
                                                    view! {
                                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                            {cat.clone()}
                                                        </span>
                                                    }
                                                }).collect_view()}
                                            </div>
                                        </div>
                                    }.into_view()
                                } else {
                                    view! { <div></div> }.into_view()
                                }}

                                {if !metadata.dependencies.is_empty() {
                                    view! {
                                        <div>
                                            <span class="text-sm font-medium text-gray-700">Dependencies:</span>
                                            <ul class="mt-1 text-sm text-gray-600 space-y-1">
                                                {metadata.dependencies.iter().map(|dep| {
                                                    view! {
                                                        <li class="flex items-center">
                                                            <span class="w-2 h-2 bg-gray-400 rounded-full mr-2"></span>
                                                            {dep.clone()}
                                                        </li>
                                                    }
                                                }).collect_view()}
                                            </ul>
                                        </div>
                                    }.into_view()
                                } else {
                                    view! { <div></div> }.into_view()
                                }}

                                <div class="pt-4 border-t border-gray-200">
                                    <div class="flex space-x-2">
                                        <button class="flex-1 bg-blue-600 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors">
                                            Configure
                                        </button>
                                        <button class="flex-1 bg-gray-600 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-700 transition-colors">
                                            Disable
                                        </button>
                                    </div>
                                </div>

                                {if let Some(homepage) = &metadata.homepage {
                                    view! {
                                        <div class="pt-2">
                                            <a
                                                href=homepage.clone()
                                                target="_blank"
                                                class="text-sm text-blue-600 hover:text-blue-800 flex items-center"
                                            >
                                                <span>Visit Homepage</span>
                                                <svg class="ml-1 w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                                </svg>
                                            </a>
                                        </div>
                                    }.into_view()
                                } else {
                                    view! { <div></div> }.into_view()
                                }}
                            </div>
                        }.into_view()
                    } else {
                        view! {
                            <div class="text-center py-8 text-gray-500">
                                <p>Plugin not found</p>
                            </div>
                        }.into_view()
                    }
                } else {
                    view! {
                        <div class="text-center py-8 text-gray-500">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            <p class="mt-2">Select a plugin to view details</p>
                        </div>
                    }.into_view()
                }
            }}
        </div>
    }
}

/// Load available plugins (mock implementation)
async fn load_available_plugins() -> Result<PluginRegistry, String> {
    // In a real implementation, this would scan for plugin files and load them
    let mut registry = PluginRegistry::new();

    // Add some example plugins
    let example_plugins = get_example_plugins();

    for plugin in example_plugins {
        registry.register_plugin(plugin)
            .map_err(|e| format!("Failed to register plugin: {}", e))?;
    }

    Ok(registry)
}

/// Get example plugins for demonstration
fn get_example_plugins() -> Vec<Rc<dyn Plugin>> {
    vec![
        Rc::new(ExampleNetworkPlugin::new()),
        Rc::new(ExampleMonitoringPlugin::new()),
        Rc::new(ExampleSecurityPlugin::new()),
    ]
}

/// Example network plugin
#[derive(Debug)]
struct ExampleNetworkPlugin {
    metadata: PluginMetadata,
    status: PluginStatus,
}

impl ExampleNetworkPlugin {
    fn new() -> Self {
        Self {
            metadata: PluginMetadata {
                id: "network-tools".to_string(),
                name: "Network Tools".to_string(),
                version: "1.0.0".to_string(),
                description: "Advanced network diagnostic and configuration tools".to_string(),
                author: "OpenWrt Team".to_string(),
                license: "GPL-2.0".to_string(),
                homepage: Some("https://openwrt.org/plugins/network-tools".to_string()),
                repository: Some("https://github.com/openwrt/luci-plugin-network-tools".to_string()),
                dependencies: vec!["network-base".to_string()],
                permissions: vec!["network.read".to_string(), "network.write".to_string()],
                categories: vec!["network".to_string(), "diagnostics".to_string()],
                min_luci_version: "1.0.0".to_string(),
                max_luci_version: None,
            },
            status: PluginStatus::Active,
        }
    }
}

impl Plugin for ExampleNetworkPlugin {
    fn metadata(&self) -> &PluginMetadata {
        &self.metadata
    }

    fn initialize(&mut self) -> Result<(), PluginError> {
        self.status = PluginStatus::Active;
        Ok(())
    }

    fn shutdown(&mut self) -> Result<(), PluginError> {
        self.status = PluginStatus::Inactive;
        Ok(())
    }

    fn status(&self) -> PluginStatus {
        self.status.clone()
    }
}

/// Example monitoring plugin
#[derive(Debug)]
struct ExampleMonitoringPlugin {
    metadata: PluginMetadata,
    status: PluginStatus,
}

impl ExampleMonitoringPlugin {
    fn new() -> Self {
        Self {
            metadata: PluginMetadata {
                id: "system-monitor".to_string(),
                name: "System Monitor".to_string(),
                version: "2.1.0".to_string(),
                description: "Real-time system monitoring and alerting".to_string(),
                author: "Community".to_string(),
                license: "MIT".to_string(),
                homepage: Some("https://github.com/community/system-monitor".to_string()),
                repository: Some("https://github.com/community/system-monitor".to_string()),
                dependencies: vec![],
                permissions: vec!["system.read".to_string()],
                categories: vec!["monitoring".to_string(), "system".to_string()],
                min_luci_version: "1.0.0".to_string(),
                max_luci_version: None,
            },
            status: PluginStatus::Active,
        }
    }
}

impl Plugin for ExampleMonitoringPlugin {
    fn metadata(&self) -> &PluginMetadata {
        &self.metadata
    }

    fn initialize(&mut self) -> Result<(), PluginError> {
        self.status = PluginStatus::Active;
        Ok(())
    }

    fn shutdown(&mut self) -> Result<(), PluginError> {
        self.status = PluginStatus::Inactive;
        Ok(())
    }

    fn status(&self) -> PluginStatus {
        self.status.clone()
    }
}

/// Example security plugin
#[derive(Debug)]
struct ExampleSecurityPlugin {
    metadata: PluginMetadata,
    status: PluginStatus,
}

impl ExampleSecurityPlugin {
    fn new() -> Self {
        Self {
            metadata: PluginMetadata {
                id: "security-suite".to_string(),
                name: "Security Suite".to_string(),
                version: "1.5.2".to_string(),
                description: "Comprehensive security tools and intrusion detection".to_string(),
                author: "Security Team".to_string(),
                license: "GPL-3.0".to_string(),
                homepage: None,
                repository: Some("https://github.com/security/luci-security-suite".to_string()),
                dependencies: vec!["firewall".to_string(), "network-base".to_string()],
                permissions: vec!["security.read".to_string(), "security.write".to_string(), "firewall.write".to_string()],
                categories: vec!["security".to_string(), "firewall".to_string()],
                min_luci_version: "1.2.0".to_string(),
                max_luci_version: Some("2.0.0".to_string()),
            },
            status: PluginStatus::Active,
        }
    }
}

impl Plugin for ExampleSecurityPlugin {
    fn metadata(&self) -> &PluginMetadata {
        &self.metadata
    }

    fn initialize(&mut self) -> Result<(), PluginError> {
        self.status = PluginStatus::Active;
        Ok(())
    }

    fn shutdown(&mut self) -> Result<(), PluginError> {
        self.status = PluginStatus::Inactive;
        Ok(())
    }

    fn status(&self) -> PluginStatus {
        self.status.clone()
    }
}
