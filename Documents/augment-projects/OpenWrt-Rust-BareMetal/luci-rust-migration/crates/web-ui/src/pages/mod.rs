//! Page components for LuCI Web Interface
//!
//! This module contains all page-level components that correspond
//! to different routes in the application.

use leptos::*;
use leptos_meta::*;
use leptos_router::*;

pub mod home;
pub mod login;
pub mod profile;
pub mod system;
pub mod network;
pub mod wireless;
pub mod services;
pub mod packages;
pub mod administration;
pub mod status;
pub mod system_dashboard;
pub mod processes;
pub mod network_monitor;
pub mod logs;
pub mod device_info;
pub mod logout;
pub mod not_found;
pub mod firewall;
pub mod enhanced_packages;
pub mod vpn;

// Re-export page components
pub use home::*;
pub use login::*;
pub use profile::*;
pub use system::*;
pub use network::*;
pub use wireless::*;
pub use services::*;
pub use packages::*;
pub use administration::*;
pub use status::*;
pub use system_dashboard::*;
pub use processes::*;
pub use network_monitor::*;
pub use logs::*;
pub use device_info::*;
pub use logout::*;
pub use not_found::*;
pub use firewall::*;
pub use enhanced_packages::*;
pub use vpn::*;