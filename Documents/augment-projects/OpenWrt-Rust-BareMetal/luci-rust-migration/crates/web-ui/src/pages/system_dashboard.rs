//! System Statistics Dashboard - Comprehensive system monitoring with historical data

use leptos::*;
use leptos_meta::*;
use serde::{Deserialize, Serialize};
use std::time::{Duration, SystemTime};

use crate::components::*;

/// Historical metrics data point
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct MetricsDataPoint {
    pub timestamp: SystemTime,
    pub cpu_usage: f32,
    pub memory_usage: f32,
    pub storage_usage: f32,
    pub network_rx_rate: f64,
    pub network_tx_rate: f64,
    pub load_average: f32,
    pub process_count: u32,
    pub temperature: Option<f32>,
}

/// Historical metrics response
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct HistoricalMetricsResponse {
    pub time_range: String,
    pub interval: String,
    pub data_points: Vec<MetricsDataPoint>,
    pub summary: MetricsSummary,
}

/// Metrics summary statistics
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct MetricsSummary {
    pub cpu_avg: f32,
    pub cpu_max: f32,
    pub cpu_min: f32,
    pub memory_avg: f32,
    pub memory_max: f32,
    pub memory_min: f32,
    pub network_peak_rx: f64,
    pub network_peak_tx: f64,
    pub uptime_percentage: f32,
}

/// Performance analysis data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceAnalysis {
    pub overall_score: u8,
    pub cpu_analysis: CpuPerformanceAnalysis,
    pub memory_analysis: MemoryPerformanceAnalysis,
    pub network_analysis: NetworkPerformanceAnalysis,
    pub storage_analysis: StoragePerformanceAnalysis,
    pub recommendations: Vec<String>,
    pub bottlenecks: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CpuPerformanceAnalysis {
    pub average_usage: f32,
    pub peak_usage: f32,
    pub idle_time_percentage: f32,
    pub load_balance_score: u8,
    pub thermal_status: String,
    pub frequency_efficiency: f32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemoryPerformanceAnalysis {
    pub average_usage: f32,
    pub peak_usage: f32,
    pub swap_usage: f32,
    pub cache_efficiency: f32,
    pub memory_pressure: f32,
    pub fragmentation_level: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkPerformanceAnalysis {
    pub total_throughput: f64,
    pub peak_rx_rate: f64,
    pub peak_tx_rate: f64,
    pub error_rate: f32,
    pub packet_loss: f32,
    pub latency_score: u8,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StoragePerformanceAnalysis {
    pub usage_percentage: f32,
    pub io_wait_time: f32,
    pub read_write_ratio: f32,
    pub fragmentation: f32,
    pub health_status: String,
}

/// System alert
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemAlert {
    pub id: String,
    pub severity: AlertSeverity,
    pub title: String,
    pub message: String,
    pub metric: String,
    pub current_value: f64,
    pub threshold: f64,
    pub timestamp: SystemTime,
    pub acknowledged: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AlertSeverity {
    Critical,
    Warning,
    Info,
}

/// System status from existing API
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemStatus {
    pub cpu_usage: f32,
    pub memory_usage: f32,
    pub storage_usage: f32,
    pub network_interfaces: Vec<NetworkInterfaceStatus>,
    pub running_processes: u32,
    pub active_connections: u32,
    pub temperature: Option<f32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkInterfaceStatus {
    pub name: String,
    pub status: String,
    pub rx_bytes: u64,
    pub tx_bytes: u64,
    pub rx_packets: u64,
    pub tx_packets: u64,
}

/// Main System Dashboard Page
#[component]
pub fn SystemDashboardPage() -> impl IntoView {
    // Page metadata
    provide_meta_context();
    
    view! {
        <Title text="System Statistics Dashboard"/>
        <Meta name="description" content="Comprehensive system statistics dashboard with historical data and performance analysis"/>
        
        <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
            <div class="container mx-auto px-4 py-6">
                <div class="mb-6">
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                        "System Statistics Dashboard"
                    </h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-2">
                        "Comprehensive system monitoring with historical data and performance analysis"
                    </p>
                </div>
                
                <SystemDashboard />
            </div>
        </div>
    }
}

/// Main dashboard component
#[component]
pub fn SystemDashboard() -> impl IntoView {
    // State management
    let (current_status, set_current_status) = create_signal(None::<SystemStatus>);
    let (historical_data, set_historical_data) = create_signal(None::<HistoricalMetricsResponse>);
    let (performance_analysis, set_performance_analysis) = create_signal(None::<PerformanceAnalysis>);
    let (system_alerts, set_system_alerts) = create_signal(Vec::<SystemAlert>::new());
    let (selected_time_range, set_selected_time_range) = create_signal("24h".to_string());
    let (loading, set_loading) = create_signal(false);
    let (error, set_error) = create_signal(None::<String>);
    
    // Auto-refresh current status every 5 seconds
    create_effect(move |_| {
        set_timeout_interval(
            move || {
                spawn_local(async move {
                    match fetch_system_status().await {
                        Ok(status) => set_current_status.set(Some(status)),
                        Err(e) => set_error.set(Some(format!("Failed to fetch status: {}", e))),
                    }
                });
            },
            Duration::from_secs(5),
        );

        // Cleanup would go here in a real implementation
    });
    
    // Load historical data when time range changes
    create_effect(move |_| {
        let time_range = selected_time_range.get();
        spawn_local(async move {
            set_loading.set(true);
            match fetch_historical_metrics(&time_range).await {
                Ok(data) => set_historical_data.set(Some(data)),
                Err(e) => set_error.set(Some(format!("Failed to fetch historical data: {}", e))),
            }
            set_loading.set(false);
        });
    });
    
    // Load performance analysis
    create_effect(move |_| {
        spawn_local(async move {
            match fetch_performance_analysis().await {
                Ok(analysis) => set_performance_analysis.set(Some(analysis)),
                Err(e) => set_error.set(Some(format!("Failed to fetch performance analysis: {}", e))),
            }
        });
    });
    
    // Load system alerts
    create_effect(move |_| {
        spawn_local(async move {
            match fetch_system_alerts().await {
                Ok(alerts) => set_system_alerts.set(alerts),
                Err(e) => set_error.set(Some(format!("Failed to fetch alerts: {}", e))),
            }
        });
    });
    
    view! {
        <div class="space-y-6">
            // Error display
            {move || error.get().map(|err| view! {
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    <strong>"Error: "</strong> {err}
                </div>
            })}
            
            // Time range selector
            <TimeRangeSelector 
                selected=selected_time_range
                on_change=set_selected_time_range
            />
            
            // Dashboard grid
            <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                // Current status overview
                <div class="xl:col-span-1">
                    <CurrentStatusWidget status=current_status />
                </div>
                
                // System alerts
                <div class="xl:col-span-1">
                    <SystemAlertsWidget alerts=system_alerts />
                </div>
                
                // Performance score
                <div class="xl:col-span-1">
                    <PerformanceScoreWidget analysis=performance_analysis />
                </div>
            </div>
            
            // Historical charts
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <HistoricalChartsWidget 
                    data=historical_data 
                    loading=loading
                    metric="cpu"
                    title="CPU Usage History"
                />
                <HistoricalChartsWidget 
                    data=historical_data 
                    loading=loading
                    metric="memory"
                    title="Memory Usage History"
                />
                <HistoricalChartsWidget 
                    data=historical_data 
                    loading=loading
                    metric="network"
                    title="Network Traffic History"
                />
                <HistoricalChartsWidget 
                    data=historical_data 
                    loading=loading
                    metric="storage"
                    title="Storage Usage History"
                />
            </div>
            
            // Performance analysis details
            <PerformanceAnalysisWidget analysis=performance_analysis />
        </div>
    }
}

/// Time range selector component
#[component]
pub fn TimeRangeSelector(
    selected: ReadSignal<String>,
    on_change: WriteSignal<String>,
) -> impl IntoView {
    let time_ranges = vec![
        ("1h", "Last Hour"),
        ("6h", "Last 6 Hours"),
        ("24h", "Last 24 Hours"),
        ("7d", "Last 7 Days"),
        ("30d", "Last 30 Days"),
    ];

    view! {
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <div class="flex items-center space-x-4">
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                    "Time Range:"
                </label>
                <select
                    class="form-select rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                    on:change=move |ev| {
                        let value = event_target_value(&ev);
                        on_change.set(value);
                    }
                >
                    {time_ranges.into_iter().map(|(value, label)| {
                        let is_selected = move || selected.get() == value;
                        view! {
                            <option value=value selected=is_selected>
                                {label}
                            </option>
                        }
                    }).collect::<Vec<_>>()}
                </select>
            </div>
        </div>
    }
}

/// Current status widget
#[component]
pub fn CurrentStatusWidget(
    status: ReadSignal<Option<SystemStatus>>,
) -> impl IntoView {
    view! {
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                "Current System Status"
            </h3>

            {move || match status.get() {
                Some(status) => view! {
                    <div class="space-y-4">
                        // CPU Usage
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">"CPU Usage"</span>
                            <div class="flex items-center space-x-2">
                                <div class="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div
                                        class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                        style=format!("width: {}%", status.cpu_usage)
                                    ></div>
                                </div>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">
                                    {format!("{:.1}%", status.cpu_usage)}
                                </span>
                            </div>
                        </div>

                        // Memory Usage
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">"Memory Usage"</span>
                            <div class="flex items-center space-x-2">
                                <div class="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div
                                        class="bg-green-600 h-2 rounded-full transition-all duration-300"
                                        style=format!("width: {}%", status.memory_usage)
                                    ></div>
                                </div>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">
                                    {format!("{:.1}%", status.memory_usage)}
                                </span>
                            </div>
                        </div>

                        // Storage Usage
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">"Storage Usage"</span>
                            <div class="flex items-center space-x-2">
                                <div class="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div
                                        class="bg-yellow-600 h-2 rounded-full transition-all duration-300"
                                        style=format!("width: {}%", status.storage_usage)
                                    ></div>
                                </div>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">
                                    {format!("{:.1}%", status.storage_usage)}
                                </span>
                            </div>
                        </div>

                        // Additional metrics
                        <div class="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-gray-900 dark:text-white">
                                    {status.running_processes}
                                </div>
                                <div class="text-xs text-gray-600 dark:text-gray-400">
                                    "Processes"
                                </div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-gray-900 dark:text-white">
                                    {status.active_connections}
                                </div>
                                <div class="text-xs text-gray-600 dark:text-gray-400">
                                    "Connections"
                                </div>
                            </div>
                        </div>

                        // Temperature if available
                        {status.temperature.map(|temp| view! {
                            <div class="pt-2 border-t border-gray-200 dark:border-gray-700">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-600 dark:text-gray-400">"Temperature"</span>
                                    <span class="text-sm font-medium text-gray-900 dark:text-white">
                                        {format!("{:.1}°C", temp)}
                                    </span>
                                </div>
                            </div>
                        })}
                    </div>
                }.into_view(),
                None => view! {
                    <div class="flex items-center justify-center h-32">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    </div>
                }.into_view()
            }}
        </div>
    }
}

/// System alerts widget
#[component]
pub fn SystemAlertsWidget(
    alerts: ReadSignal<Vec<SystemAlert>>,
) -> impl IntoView {
    view! {
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                "System Alerts"
            </h3>

            {move || {
                let alerts_list = alerts.get();
                if alerts_list.is_empty() {
                    view! {
                        <div class="text-center py-8">
                            <div class="text-green-600 dark:text-green-400 text-4xl mb-2">
                                "✓"
                            </div>
                            <p class="text-gray-600 dark:text-gray-400">
                                "No active alerts"
                            </p>
                        </div>
                    }.into_view()
                } else {
                    view! {
                        <div class="space-y-3 max-h-64 overflow-y-auto">
                            {alerts_list.into_iter().map(|alert| {
                                let severity_class = match alert.severity {
                                    AlertSeverity::Critical => "border-red-500 bg-red-50 dark:bg-red-900/20",
                                    AlertSeverity::Warning => "border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20",
                                    AlertSeverity::Info => "border-blue-500 bg-blue-50 dark:bg-blue-900/20",
                                };

                                let severity_text_class = match alert.severity {
                                    AlertSeverity::Critical => "text-red-700 dark:text-red-300",
                                    AlertSeverity::Warning => "text-yellow-700 dark:text-yellow-300",
                                    AlertSeverity::Info => "text-blue-700 dark:text-blue-300",
                                };

                                view! {
                                    <div class=format!("border-l-4 p-3 rounded {}", severity_class)>
                                        <div class="flex items-start justify-between">
                                            <div class="flex-1">
                                                <h4 class=format!("font-medium {}", severity_text_class)>
                                                    {alert.title}
                                                </h4>
                                                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                                    {alert.message}
                                                </p>
                                                <div class="text-xs text-gray-500 dark:text-gray-500 mt-2">
                                                    {format!("{}: {:.2} (threshold: {:.2})",
                                                        alert.metric, alert.current_value, alert.threshold)}
                                                </div>
                                            </div>
                                            <div class="ml-4">
                                                <span class=format!("inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {}",
                                                    match alert.severity {
                                                        AlertSeverity::Critical => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
                                                        AlertSeverity::Warning => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
                                                        AlertSeverity::Info => "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
                                                    })>
                                                    {format!("{:?}", alert.severity)}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                }
                            }).collect::<Vec<_>>()}
                        </div>
                    }.into_view()
                }
            }}
        </div>
    }
}

/// Performance score widget
#[component]
pub fn PerformanceScoreWidget(
    analysis: ReadSignal<Option<PerformanceAnalysis>>,
) -> impl IntoView {
    view! {
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                "Performance Score"
            </h3>

            {move || match analysis.get() {
                Some(analysis) => {
                    let score_color = if analysis.overall_score >= 80 {
                        "text-green-600 dark:text-green-400"
                    } else if analysis.overall_score >= 60 {
                        "text-yellow-600 dark:text-yellow-400"
                    } else {
                        "text-red-600 dark:text-red-400"
                    };

                    view! {
                        <div class="text-center">
                            <div class=format!("text-4xl font-bold mb-2 {}", score_color)>
                                {analysis.overall_score}
                                <span class="text-lg text-gray-500 dark:text-gray-400">"/100"</span>
                            </div>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">
                                "Overall System Performance"
                            </p>

                            // Performance breakdown
                            <div class="space-y-2 text-left">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600 dark:text-gray-400">"CPU Efficiency"</span>
                                    <span class="text-sm font-medium">
                                        {format!("{:.1}%", analysis.cpu_analysis.frequency_efficiency * 100.0)}
                                    </span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600 dark:text-gray-400">"Memory Efficiency"</span>
                                    <span class="text-sm font-medium">
                                        {format!("{:.1}%", analysis.memory_analysis.cache_efficiency * 100.0)}
                                    </span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600 dark:text-gray-400">"Network Latency"</span>
                                    <span class="text-sm font-medium">
                                        {format!("{}/10", analysis.network_analysis.latency_score)}
                                    </span>
                                </div>
                            </div>

                            // Top recommendations
                            {if !analysis.recommendations.is_empty() {
                                view! {
                                    <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                                        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">
                                            "Top Recommendations"
                                        </h4>
                                        <ul class="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                                            {analysis.recommendations.into_iter().take(3).map(|rec| view! {
                                                <li class="flex items-start">
                                                    <span class="text-blue-500 mr-1">"•"</span>
                                                    <span>{rec}</span>
                                                </li>
                                            }).collect::<Vec<_>>()}
                                        </ul>
                                    </div>
                                }.into_view()
                            } else {
                                view! { <div></div> }.into_view()
                            }}
                        </div>
                    }.into_view()
                },
                None => view! {
                    <div class="flex items-center justify-center h-32">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    </div>
                }.into_view()
            }}
        </div>
    }
}

/// Historical charts widget
#[component]
pub fn HistoricalChartsWidget(
    data: ReadSignal<Option<HistoricalMetricsResponse>>,
    loading: ReadSignal<bool>,
    metric: &'static str,
    title: &'static str,
) -> impl IntoView {
    view! {
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                {title}
            </h3>

            {move || {
                if loading.get() {
                    view! {
                        <div class="flex items-center justify-center h-64">
                            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                        </div>
                    }.into_view()
                } else {
                    match data.get() {
                        Some(metrics_data) => {
                            // Create simple SVG chart
                            let chart_data = match metric {
                                "cpu" => metrics_data.data_points.iter()
                                    .map(|p| p.cpu_usage as f64)
                                    .collect::<Vec<_>>(),
                                "memory" => metrics_data.data_points.iter()
                                    .map(|p| p.memory_usage as f64)
                                    .collect::<Vec<_>>(),
                                "storage" => metrics_data.data_points.iter()
                                    .map(|p| p.storage_usage as f64)
                                    .collect::<Vec<_>>(),
                                "network" => metrics_data.data_points.iter()
                                    .map(|p| (p.network_rx_rate + p.network_tx_rate) / 1024.0 / 1024.0) // Convert to MB/s
                                    .collect::<Vec<_>>(),
                                _ => vec![],
                            };

                            view! {
                                <div class="h-64">
                                    <SimpleLineChart
                                        data=chart_data
                                        metric=metric
                                        color=match metric {
                                            "cpu" => "#3B82F6",
                                            "memory" => "#10B981",
                                            "storage" => "#F59E0B",
                                            "network" => "#8B5CF6",
                                            _ => "#6B7280"
                                        }
                                    />
                                </div>

                                // Summary stats
                                <div class="mt-4 grid grid-cols-3 gap-4 text-center">
                                    <div>
                                        <div class="text-lg font-semibold text-gray-900 dark:text-white">
                                            {match metric {
                                                "cpu" => format!("{:.1}%", metrics_data.summary.cpu_avg),
                                                "memory" => format!("{:.1}%", metrics_data.summary.memory_avg),
                                                "storage" => format!("{:.1}%", metrics_data.summary.memory_avg), // Using memory avg as placeholder
                                                "network" => format!("{:.1} MB/s", metrics_data.summary.network_peak_rx / 1024.0 / 1024.0),
                                                _ => "N/A".to_string(),
                                            }}
                                        </div>
                                        <div class="text-xs text-gray-600 dark:text-gray-400">"Average"</div>
                                    </div>
                                    <div>
                                        <div class="text-lg font-semibold text-gray-900 dark:text-white">
                                            {match metric {
                                                "cpu" => format!("{:.1}%", metrics_data.summary.cpu_max),
                                                "memory" => format!("{:.1}%", metrics_data.summary.memory_max),
                                                "storage" => format!("{:.1}%", metrics_data.summary.memory_max), // Using memory max as placeholder
                                                "network" => format!("{:.1} MB/s", metrics_data.summary.network_peak_tx / 1024.0 / 1024.0),
                                                _ => "N/A".to_string(),
                                            }}
                                        </div>
                                        <div class="text-xs text-gray-600 dark:text-gray-400">"Peak"</div>
                                    </div>
                                    <div>
                                        <div class="text-lg font-semibold text-gray-900 dark:text-white">
                                            {match metric {
                                                "cpu" => format!("{:.1}%", metrics_data.summary.cpu_min),
                                                "memory" => format!("{:.1}%", metrics_data.summary.memory_min),
                                                "storage" => format!("{:.1}%", metrics_data.summary.memory_min), // Using memory min as placeholder
                                                "network" => format!("{:.1}%", metrics_data.summary.uptime_percentage),
                                                _ => "N/A".to_string(),
                                            }}
                                        </div>
                                        <div class="text-xs text-gray-600 dark:text-gray-400">
                                            {match metric {
                                                "network" => "Uptime",
                                                _ => "Minimum",
                                            }}
                                        </div>
                                    </div>
                                </div>
                            }.into_view()
                        },
                        None => view! {
                            <div class="flex items-center justify-center h-64 text-gray-500 dark:text-gray-400">
                                "No data available"
                            </div>
                        }.into_view()
                    }
                }
            }}
        </div>
    }
}

/// Simple line chart component using SVG
#[component]
pub fn SimpleLineChart(
    data: Vec<f64>,
    metric: &'static str,
    color: &'static str,
) -> impl IntoView {
    if data.is_empty() {
        return view! {
            <div class="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
                "No data points available"
            </div>
        };
    }

    let width = 400.0;
    let height = 200.0;
    let padding = 20.0;

    let max_value = data.iter().fold(0.0f64, |a, &b| a.max(b));
    let min_value = data.iter().fold(f64::INFINITY, |a, &b| a.min(b));
    let value_range = (max_value - min_value).max(1.0); // Avoid division by zero

    let points: Vec<String> = data.iter().enumerate().map(|(i, &value)| {
        let x = padding + (i as f64 / (data.len() - 1).max(1) as f64) * (width - 2.0 * padding);
        let y = height - padding - ((value - min_value) / value_range) * (height - 2.0 * padding);
        format!("{:.1},{:.1}", x, y)
    }).collect();

    let path_data = format!("M {}", points.join(" L "));

    view! {
        <div class="w-full h-full">
            <svg
                viewBox=format!("0 0 {} {}", width, height)
                class="w-full h-full"
            >
                // Grid lines
                <defs>
                    <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                        <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#e5e7eb" stroke-width="1" opacity="0.3"/>
                    </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#grid)" />

                // Chart line
                <path
                    d=path_data
                    fill="none"
                    stroke=color
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                />

                // Data points
                {points.iter().enumerate().map(|(i, point)| {
                    let coords: Vec<&str> = point.split(',').collect();
                    if coords.len() == 2 {
                        let x: f64 = coords[0].parse().unwrap_or(0.0);
                        let y: f64 = coords[1].parse().unwrap_or(0.0);
                        view! {
                            <circle
                                cx=x
                                cy=y
                                r="3"
                                fill=color
                                stroke="white"
                                stroke-width="2"
                            />
                        }.into_view()
                    } else {
                        view! { <g></g> }.into_view()
                    }
                }).collect::<Vec<_>>()}

                // Y-axis labels
                <text x="5" y="25" class="text-xs fill-gray-600 dark:fill-gray-400">
                    {format!("{:.1}", max_value)}
                </text>
                <text x="5" y=format!("{}", height - 10.0) class="text-xs fill-gray-600 dark:fill-gray-400">
                    {format!("{:.1}", min_value)}
                </text>
            </svg>
        </div>
    }
}

/// Performance analysis detailed widget
#[component]
pub fn PerformanceAnalysisWidget(
    analysis: ReadSignal<Option<PerformanceAnalysis>>,
) -> impl IntoView {
    view! {
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">
                "Detailed Performance Analysis"
            </h3>

            {move || match analysis.get() {
                Some(analysis) => view! {
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        // CPU Analysis
                        <div class="space-y-4">
                            <h4 class="font-medium text-gray-900 dark:text-white">
                                "CPU Performance"
                            </h4>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600 dark:text-gray-400">"Average Usage"</span>
                                    <span class="text-sm font-medium">{format!("{:.1}%", analysis.cpu_analysis.average_usage)}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600 dark:text-gray-400">"Peak Usage"</span>
                                    <span class="text-sm font-medium">{format!("{:.1}%", analysis.cpu_analysis.peak_usage)}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600 dark:text-gray-400">"Idle Time"</span>
                                    <span class="text-sm font-medium">{format!("{:.1}%", analysis.cpu_analysis.idle_time_percentage)}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600 dark:text-gray-400">"Load Balance Score"</span>
                                    <span class="text-sm font-medium">{format!("{}/10", analysis.cpu_analysis.load_balance_score)}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600 dark:text-gray-400">"Thermal Status"</span>
                                    <span class="text-sm font-medium">{analysis.cpu_analysis.thermal_status}</span>
                                </div>
                            </div>
                        </div>

                        // Memory Analysis
                        <div class="space-y-4">
                            <h4 class="font-medium text-gray-900 dark:text-white">
                                "Memory Performance"
                            </h4>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600 dark:text-gray-400">"Average Usage"</span>
                                    <span class="text-sm font-medium">{format!("{:.1}%", analysis.memory_analysis.average_usage)}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600 dark:text-gray-400">"Peak Usage"</span>
                                    <span class="text-sm font-medium">{format!("{:.1}%", analysis.memory_analysis.peak_usage)}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600 dark:text-gray-400">"Swap Usage"</span>
                                    <span class="text-sm font-medium">{format!("{:.1}%", analysis.memory_analysis.swap_usage)}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600 dark:text-gray-400">"Cache Efficiency"</span>
                                    <span class="text-sm font-medium">{format!("{:.1}%", analysis.memory_analysis.cache_efficiency * 100.0)}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600 dark:text-gray-400">"Fragmentation"</span>
                                    <span class="text-sm font-medium">{analysis.memory_analysis.fragmentation_level}</span>
                                </div>
                            </div>
                        </div>

                        // Network Analysis
                        <div class="space-y-4">
                            <h4 class="font-medium text-gray-900 dark:text-white">
                                "Network Performance"
                            </h4>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600 dark:text-gray-400">"Total Throughput"</span>
                                    <span class="text-sm font-medium">{format!("{:.1} MB/s", analysis.network_analysis.total_throughput / 1024.0 / 1024.0)}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600 dark:text-gray-400">"Peak RX Rate"</span>
                                    <span class="text-sm font-medium">{format!("{:.1} MB/s", analysis.network_analysis.peak_rx_rate / 1024.0 / 1024.0)}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600 dark:text-gray-400">"Peak TX Rate"</span>
                                    <span class="text-sm font-medium">{format!("{:.1} MB/s", analysis.network_analysis.peak_tx_rate / 1024.0 / 1024.0)}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600 dark:text-gray-400">"Error Rate"</span>
                                    <span class="text-sm font-medium">{format!("{:.3}%", analysis.network_analysis.error_rate)}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600 dark:text-gray-400">"Latency Score"</span>
                                    <span class="text-sm font-medium">{format!("{}/10", analysis.network_analysis.latency_score)}</span>
                                </div>
                            </div>
                        </div>

                        // Storage Analysis
                        <div class="space-y-4">
                            <h4 class="font-medium text-gray-900 dark:text-white">
                                "Storage Performance"
                            </h4>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600 dark:text-gray-400">"Usage"</span>
                                    <span class="text-sm font-medium">{format!("{:.1}%", analysis.storage_analysis.usage_percentage)}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600 dark:text-gray-400">"I/O Wait Time"</span>
                                    <span class="text-sm font-medium">{format!("{:.1}%", analysis.storage_analysis.io_wait_time)}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600 dark:text-gray-400">"Read/Write Ratio"</span>
                                    <span class="text-sm font-medium">{format!("{:.2}:1", analysis.storage_analysis.read_write_ratio)}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600 dark:text-gray-400">"Fragmentation"</span>
                                    <span class="text-sm font-medium">{format!("{:.1}%", analysis.storage_analysis.fragmentation)}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600 dark:text-gray-400">"Health Status"</span>
                                    <span class="text-sm font-medium">{analysis.storage_analysis.health_status}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    // Recommendations and Bottlenecks
                    <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
                        // Recommendations
                        {if !analysis.recommendations.is_empty() {
                            view! {
                                <div>
                                    <h4 class="font-medium text-gray-900 dark:text-white mb-3">
                                        "Recommendations"
                                    </h4>
                                    <ul class="space-y-2">
                                        {analysis.recommendations.into_iter().map(|rec| view! {
                                            <li class="flex items-start text-sm">
                                                <span class="text-green-500 mr-2 mt-0.5">"✓"</span>
                                                <span class="text-gray-700 dark:text-gray-300">{rec}</span>
                                            </li>
                                        }).collect::<Vec<_>>()}
                                    </ul>
                                </div>
                            }.into_view()
                        } else {
                            view! { <div></div> }.into_view()
                        }}

                        // Bottlenecks
                        {if !analysis.bottlenecks.is_empty() {
                            view! {
                                <div>
                                    <h4 class="font-medium text-gray-900 dark:text-white mb-3">
                                        "Identified Bottlenecks"
                                    </h4>
                                    <ul class="space-y-2">
                                        {analysis.bottlenecks.into_iter().map(|bottleneck| view! {
                                            <li class="flex items-start text-sm">
                                                <span class="text-red-500 mr-2 mt-0.5">"⚠"</span>
                                                <span class="text-gray-700 dark:text-gray-300">{bottleneck}</span>
                                            </li>
                                        }).collect::<Vec<_>>()}
                                    </ul>
                                </div>
                            }.into_view()
                        } else {
                            view! { <div></div> }.into_view()
                        }}
                    </div>
                }.into_view(),
                None => view! {
                    <div class="flex items-center justify-center h-32">
                        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                    </div>
                }.into_view()
            }}
        </div>
    }
}

// API Response wrapper
#[derive(Debug, Serialize, Deserialize)]
struct ApiResponse<T> {
    data: T,
    success: bool,
    message: Option<String>,
}

// API Functions

/// Fetch current system status
async fn fetch_system_status() -> Result<SystemStatus, String> {
    let response = reqwest::get("/api/system/status")
        .await
        .map_err(|e| format!("Request failed: {}", e))?;

    if response.status().is_success() {
        let api_response: ApiResponse<SystemStatus> = response
            .json()
            .await
            .map_err(|e| format!("Failed to parse response: {}", e))?;

        Ok(api_response.data)
    } else {
        Err(format!("API error: {}", response.status()))
    }
}

/// Fetch historical metrics data
async fn fetch_historical_metrics(time_range: &str) -> Result<HistoricalMetricsResponse, String> {
    let url = format!("/api/system/metrics/history?time_range={}&interval=auto", time_range);
    let response = reqwest::get(&url)
        .await
        .map_err(|e| format!("Request failed: {}", e))?;

    if response.status().is_success() {
        let api_response: ApiResponse<HistoricalMetricsResponse> = response
            .json()
            .await
            .map_err(|e| format!("Failed to parse response: {}", e))?;

        Ok(api_response.data)
    } else {
        Err(format!("API error: {}", response.status()))
    }
}

/// Fetch performance analysis
async fn fetch_performance_analysis() -> Result<PerformanceAnalysis, String> {
    let response = reqwest::get("/api/system/performance/analysis")
        .await
        .map_err(|e| format!("Request failed: {}", e))?;

    if response.status().is_success() {
        let api_response: ApiResponse<PerformanceAnalysis> = response
            .json()
            .await
            .map_err(|e| format!("Failed to parse response: {}", e))?;

        Ok(api_response.data)
    } else {
        Err(format!("API error: {}", response.status()))
    }
}

/// Fetch system alerts
async fn fetch_system_alerts() -> Result<Vec<SystemAlert>, String> {
    let response = reqwest::get("/api/system/alerts")
        .await
        .map_err(|e| format!("Request failed: {}", e))?;

    if response.status().is_success() {
        let api_response: ApiResponse<Vec<SystemAlert>> = response
            .json()
            .await
            .map_err(|e| format!("Failed to parse response: {}", e))?;

        Ok(api_response.data)
    } else {
        Err(format!("API error: {}", response.status()))
    }
}

// Utility Functions

/// Simple timeout for periodic execution (simplified version)
fn set_timeout_interval<F>(f: F, _duration: Duration)
where
    F: Fn() + 'static,
{
    // Simplified version - just call once for now
    // In a real implementation, this would use proper WASM bindings
    f();
}
