//! VPN Configuration Interface
//!
//! Comprehensive VPN management interface supporting OpenVPN, WireGuard, and IPSec
//! with certificate management and connection monitoring.

use leptos::*;
use leptos_router::*;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::rc::Rc;
use crate::components::{
    navigation::{Button, ButtonVariant, ButtonSize},
    loading::LoadingSpinner,
    tables::{AdvancedDataTable, AdvancedTableColumn, AdvancedTableRow, AdvancedTableCell, TableAction, BulkAction, FilterType, ColumnDataType, TableAlign},
    modals::{Modal, ModalSize},
    forms::{FormInput, FormSelect, FormTextarea, FormCheckbox, SelectOption},
    alerts::{Alert, AlertVariant},
};

/// VPN protocol types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
#[serde(rename_all = "lowercase")]
pub enum VpnProtocol {
    OpenVPN,
    WireGuard,
    IPSec,
}

impl std::fmt::Display for VpnProtocol {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            VpnProtocol::OpenVPN => write!(f, "OpenVPN"),
            VpnProtocol::WireGuard => write!(f, "WireGuard"),
            VpnProtocol::IPSec => write!(f, "IPSec"),
        }
    }
}

/// VPN authentication methods
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum VpnAuthMethod {
    Certificate,
    UsernamePassword,
    PublicKey,
    PreSharedKey,
}

impl std::fmt::Display for VpnAuthMethod {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            VpnAuthMethod::Certificate => write!(f, "Certificate"),
            VpnAuthMethod::UsernamePassword => write!(f, "Username/Password"),
            VpnAuthMethod::PublicKey => write!(f, "Public Key"),
            VpnAuthMethod::PreSharedKey => write!(f, "Pre-shared Key"),
        }
    }
}

/// VPN encryption types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum VpnEncryption {
    AES256,
    AES128,
    ChaCha20Poly1305,
    Blowfish,
}

impl std::fmt::Display for VpnEncryption {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            VpnEncryption::AES256 => write!(f, "AES-256"),
            VpnEncryption::AES128 => write!(f, "AES-128"),
            VpnEncryption::ChaCha20Poly1305 => write!(f, "ChaCha20-Poly1305"),
            VpnEncryption::Blowfish => write!(f, "Blowfish"),
        }
    }
}

/// VPN connection status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum VpnStatus {
    Connected,
    Disconnected,
    Connecting,
    Error,
}

impl std::fmt::Display for VpnStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            VpnStatus::Connected => write!(f, "Connected"),
            VpnStatus::Disconnected => write!(f, "Disconnected"),
            VpnStatus::Connecting => write!(f, "Connecting"),
            VpnStatus::Error => write!(f, "Error"),
        }
    }
}

/// VPN route configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VpnRoute {
    pub destination: String,
    pub gateway: Option<String>,
    pub metric: Option<u32>,
}

/// VPN configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VpnConfig {
    pub id: String,
    pub name: String,
    pub protocol: VpnProtocol,
    pub enabled: bool,
    pub server_address: String,
    pub server_port: u16,
    pub auth_method: VpnAuthMethod,
    pub encryption: VpnEncryption,
    pub local_ip: Option<String>,
    pub remote_ip: Option<String>,
    pub dns_servers: Vec<String>,
    pub routes: Vec<VpnRoute>,
    pub certificate_id: Option<String>,
    pub username: Option<String>,
    pub auto_connect: bool,
    pub keep_alive: bool,
    pub compression: bool,
    pub mtu: Option<u16>,
    pub status: VpnStatus,
    pub last_connected: Option<String>,
    pub bytes_sent: u64,
    pub bytes_received: u64,
}

/// VPN certificate information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VpnCertificate {
    pub id: String,
    pub name: String,
    pub certificate_type: VpnCertificateType,
    pub subject: String,
    pub issuer: String,
    pub valid_from: String,
    pub valid_to: String,
    pub fingerprint: String,
    pub key_size: u32,
    pub is_ca: bool,
}

/// VPN certificate type
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum VpnCertificateType {
    Server,
    Client,
    CA,
}

impl std::fmt::Display for VpnCertificateType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            VpnCertificateType::Server => write!(f, "Server"),
            VpnCertificateType::Client => write!(f, "Client"),
            VpnCertificateType::CA => write!(f, "Certificate Authority"),
        }
    }
}

/// Main VPN page with tabbed interface
#[component]
pub fn VpnPage() -> impl IntoView {
    let (active_tab, set_active_tab) = create_signal("connections".to_string());

    view! {
        <div class="space-y-6">
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h1 class="text-2xl font-bold text-gray-900">"VPN Configuration"</h1>
                    <p class="text-sm text-gray-600 mt-1">
                        "Manage VPN connections, certificates, and monitoring"
                    </p>
                </div>

                // Tab navigation
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8 px-6">
                        <button
                            class={move || if active_tab.get() == "connections" {
                                "border-blue-500 text-blue-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                            } else {
                                "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                            }}
                            on:click=move |_| set_active_tab.set("connections".to_string())
                        >
                            "Connections"
                        </button>
                        <button
                            class={move || if active_tab.get() == "certificates" {
                                "border-blue-500 text-blue-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                            } else {
                                "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                            }}
                            on:click=move |_| set_active_tab.set("certificates".to_string())
                        >
                            "Certificates"
                        </button>
                        <button
                            class={move || if active_tab.get() == "monitoring" {
                                "border-blue-500 text-blue-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                            } else {
                                "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                            }}
                            on:click=move |_| set_active_tab.set("monitoring".to_string())
                        >
                            "Monitoring"
                        </button>
                    </nav>
                </div>

                // Tab content
                <div class="p-6">
                    {move || match active_tab.get().as_str() {
                        "connections" => view! { <VpnConnectionsTab /> }.into_view(),
                        "certificates" => view! { <VpnCertificatesTab /> }.into_view(),
                        "monitoring" => view! { <VpnMonitoringTab /> }.into_view(),
                        _ => view! { <VpnConnectionsTab /> }.into_view(),
                    }}
                </div>
            </div>
        </div>
    }
}

/// VPN connections management tab
#[component]
fn VpnConnectionsTab() -> impl IntoView {
    let (vpn_configs, set_vpn_configs) = create_signal(Vec::<VpnConfig>::new());
    let (loading, set_loading) = create_signal(false);
    let (show_create_modal, set_show_create_modal) = create_signal(false);
    let (selected_config, set_selected_config) = create_signal(None::<VpnConfig>);

    // Load VPN configurations on mount
    create_effect(move |_| {
        spawn_local(async move {
            set_loading.set(true);
            // TODO: Replace with actual API call
            let mock_configs = get_mock_vpn_configs();
            set_vpn_configs.set(mock_configs);
            set_loading.set(false);
        });
    });

    view! {
        <div class="space-y-6">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-lg font-medium text-gray-900">"VPN Connections"</h2>
                    <p class="text-sm text-gray-600">
                        "Manage OpenVPN, WireGuard, and IPSec connections"
                    </p>
                </div>
                <Button
                    variant=ButtonVariant::Primary
                    size=ButtonSize::Medium
                    on_click=Box::new(move || set_show_create_modal.set(true))
                >
                    "Add Connection"
                </Button>
            </div>

            <VpnConnectionsTable 
                configs=vpn_configs.get()
                loading=loading.get()
                on_edit=move |config| set_selected_config.set(Some(config))
            />

            // Create/Edit Modal
            <Modal
                open=show_create_modal
                on_close=set_show_create_modal
                title="Add VPN Connection".to_string()
                size=ModalSize::Large
            >
                <VpnConfigForm 
                    config=None
                    on_save=move |_config| {
                        set_show_create_modal.set(false);
                        // TODO: Refresh configs
                    }
                    on_cancel=move || set_show_create_modal.set(false)
                />
            </Modal>
        </div>
    }
}

/// VPN connections table component
#[component]
fn VpnConnectionsTable(
    configs: Vec<VpnConfig>,
    loading: bool,
    on_edit: impl Fn(VpnConfig) + 'static + Copy,
) -> impl IntoView {
    let columns = vec![
        AdvancedTableColumn::new("name", "Name")
            .sortable(true)
            .filterable(true)
            .width("200px"),
        AdvancedTableColumn::new("protocol", "Protocol")
            .sortable(true)
            .filterable(true)
            .filter_type(FilterType::Select(vec![
                "OpenVPN".to_string(),
                "WireGuard".to_string(),
                "IPSec".to_string(),
            ]))
            .width("120px"),
        AdvancedTableColumn::new("status", "Status")
            .sortable(true)
            .filterable(true)
            .filter_type(FilterType::Select(vec![
                "Connected".to_string(),
                "Disconnected".to_string(),
                "Connecting".to_string(),
                "Error".to_string(),
            ]))
            .width("120px"),
        AdvancedTableColumn::new("server", "Server")
            .sortable(true)
            .filterable(true)
            .width("200px"),
        AdvancedTableColumn::new("enabled", "Enabled")
            .sortable(true)
            .filterable(true)
            .filter_type(FilterType::Select(vec![
                "Yes".to_string(),
                "No".to_string(),
            ]))
            .width("100px"),
        AdvancedTableColumn::new("last_connected", "Last Connected")
            .sortable(true)
            .width("150px"),
    ];

    let rows: Vec<AdvancedTableRow> = configs.into_iter().map(|config| {
        let config_id = config.id.clone();
        let config_clone = config.clone();

        let mut cells = HashMap::new();

        cells.insert("name".to_string(), AdvancedTableCell {
            key: "name".to_string(),
            content: view! {
                <div class="flex items-center space-x-2">
                    <span class="font-medium text-gray-900">{config.name.clone()}</span>
                    {if config.auto_connect {
                        view! {
                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                "Auto"
                            </span>
                        }.into_view()
                    } else {
                        view! { <span></span> }.into_view()
                    }}
                </div>
            }.into_view(),
            raw_value: config.name.clone(),
        });

        cells.insert("protocol".to_string(), AdvancedTableCell {
            key: "protocol".to_string(),
            content: view! {
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    {config.protocol.to_string()}
                </span>
            }.into_view(),
            raw_value: config.protocol.to_string(),
        });

        let status_class = match config.status {
            VpnStatus::Connected => "bg-green-100 text-green-800",
            VpnStatus::Disconnected => "bg-gray-100 text-gray-800",
            VpnStatus::Connecting => "bg-yellow-100 text-yellow-800",
            VpnStatus::Error => "bg-red-100 text-red-800",
        };

        cells.insert("status".to_string(), AdvancedTableCell {
            key: "status".to_string(),
            content: view! {
                <span class={format!("inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {}", status_class)}>
                    {config.status.to_string()}
                </span>
            }.into_view(),
            raw_value: config.status.to_string(),
        });

        cells.insert("server".to_string(), AdvancedTableCell {
            key: "server".to_string(),
            content: view! {
                <div class="text-sm">
                    <div class="text-gray-900">{config.server_address.clone()}</div>
                    <div class="text-gray-500">{"Port "}{config.server_port}</div>
                </div>
            }.into_view(),
            raw_value: format!("{}:{}", config.server_address, config.server_port),
        });

        cells.insert("enabled".to_string(), AdvancedTableCell {
            key: "enabled".to_string(),
            content: view! {
                <span class={if config.enabled { "text-green-600" } else { "text-gray-400" }}>
                    {if config.enabled { "Yes" } else { "No" }}
                </span>
            }.into_view(),
            raw_value: if config.enabled { "Yes" } else { "No" }.to_string(),
        });

        cells.insert("last_connected".to_string(), AdvancedTableCell {
            key: "last_connected".to_string(),
            content: view! {
                <span class="text-sm text-gray-600">
                    {config.last_connected.clone().unwrap_or_else(|| "Never".to_string())}
                </span>
            }.into_view(),
            raw_value: config.last_connected.clone().unwrap_or_else(|| "Never".to_string()),
        });

        let actions = vec![
            TableAction {
                label: if config.status == VpnStatus::Connected { "Disconnect" } else { "Connect" }.to_string(),
                icon: None,
                variant: if config.status == VpnStatus::Connected {
                    ButtonVariant::Warning
                } else {
                    ButtonVariant::Success
                },
                on_click: Some(Box::new(move |_id| {
                    // TODO: Implement connect/disconnect
                })),
            },
            TableAction {
                label: "Edit".to_string(),
                icon: None,
                variant: ButtonVariant::Secondary,
                on_click: Some(Box::new(move |_id| {
                    on_edit(config_clone.clone());
                })),
            },
            TableAction {
                label: "Delete".to_string(),
                icon: None,
                variant: ButtonVariant::Danger,
                on_click: Some(Box::new(move |_id| {
                    // TODO: Implement delete with confirmation
                })),
            },
        ];

        AdvancedTableRow {
            id: config_id,
            cells,
            actions,
            selectable: true,
            expandable: false,
            expanded_content: None,
        }
    }).collect();

    let bulk_actions = vec![
        BulkAction {
            id: "enable".to_string(),
            label: "Enable Selected".to_string(),
            icon: None,
            variant: ButtonVariant::Success,
            requires_confirmation: false,
            confirmation_message: None,
        },
        BulkAction {
            id: "disable".to_string(),
            label: "Disable Selected".to_string(),
            icon: None,
            variant: ButtonVariant::Warning,
            requires_confirmation: false,
            confirmation_message: None,
        },
        BulkAction {
            id: "delete".to_string(),
            label: "Delete Selected".to_string(),
            icon: None,
            variant: ButtonVariant::Danger,
            requires_confirmation: true,
            confirmation_message: Some("Are you sure you want to delete the selected VPN connections?".to_string()),
        },
    ];

    view! {
        <AdvancedDataTable
            columns=columns
            rows=rows
            loading=loading
            empty_message="No VPN connections configured".to_string()
            selectable=true
            bulk_actions=bulk_actions
            searchable=true
            paginated=true
            exportable=true
            on_bulk_action=Rc::new(move |action_id, selected_ids| {
                // TODO: Implement bulk actions
                log::info!("Bulk action {} on {:?}", action_id, selected_ids);
            })
        />
    }
}

/// VPN configuration form component
#[component]
fn VpnConfigForm(
    config: Option<VpnConfig>,
    on_save: impl Fn(VpnConfig) + 'static + Copy,
    on_cancel: impl Fn() + 'static + Copy,
) -> impl IntoView {
    let (name, set_name) = create_signal(config.as_ref().map(|c| c.name.clone()).unwrap_or_default());
    let (protocol, set_protocol) = create_signal(config.as_ref().map(|c| c.protocol.clone()).unwrap_or(VpnProtocol::OpenVPN));
    let (server_address, set_server_address) = create_signal(config.as_ref().map(|c| c.server_address.clone()).unwrap_or_default());
    let (server_port, set_server_port) = create_signal(config.as_ref().map(|c| c.server_port.to_string()).unwrap_or_else(|| "1194".to_string()));
    let (auth_method, set_auth_method) = create_signal(config.as_ref().map(|c| c.auth_method.clone()).unwrap_or(VpnAuthMethod::Certificate));
    let (encryption, set_encryption) = create_signal(config.as_ref().map(|c| c.encryption.clone()).unwrap_or(VpnEncryption::AES256));
    let (username, set_username) = create_signal(config.as_ref().and_then(|c| c.username.clone()).unwrap_or_default());
    let (auto_connect, set_auto_connect) = create_signal(config.as_ref().map(|c| c.auto_connect).unwrap_or(false));
    let (keep_alive, set_keep_alive) = create_signal(config.as_ref().map(|c| c.keep_alive).unwrap_or(true));
    let (compression, set_compression) = create_signal(config.as_ref().map(|c| c.compression).unwrap_or(false));

    let protocol_options = vec![
        ("openvpn".to_string(), "OpenVPN".to_string()),
        ("wireguard".to_string(), "WireGuard".to_string()),
        ("ipsec".to_string(), "IPSec".to_string()),
    ];

    let auth_method_options = vec![
        ("certificate".to_string(), "Certificate".to_string()),
        ("username_password".to_string(), "Username/Password".to_string()),
        ("public_key".to_string(), "Public Key".to_string()),
        ("pre_shared_key".to_string(), "Pre-shared Key".to_string()),
    ];

    let encryption_options = vec![
        ("aes256".to_string(), "AES-256".to_string()),
        ("aes128".to_string(), "AES-128".to_string()),
        ("chacha20poly1305".to_string(), "ChaCha20-Poly1305".to_string()),
        ("blowfish".to_string(), "Blowfish".to_string()),
    ];

    let config_clone = config.clone();
    let handle_save = move || {
        let new_config = VpnConfig {
            id: config_clone.as_ref().map(|c| c.id.clone()).unwrap_or_else(|| uuid::Uuid::new_v4().to_string()),
            name: name.get(),
            protocol: protocol.get(),
            enabled: false,
            server_address: server_address.get(),
            server_port: server_port.get().parse().unwrap_or(1194),
            auth_method: auth_method.get(),
            encryption: encryption.get(),
            local_ip: None,
            remote_ip: None,
            dns_servers: vec![],
            routes: vec![],
            certificate_id: None,
            username: if username.get().is_empty() { None } else { Some(username.get()) },
            auto_connect: auto_connect.get(),
            keep_alive: keep_alive.get(),
            compression: compression.get(),
            mtu: None,
            status: VpnStatus::Disconnected,
            last_connected: None,
            bytes_sent: 0,
            bytes_received: 0,
        };
        on_save(new_config);
    };

    // Create protocol options
    let protocol_options = vec![
        SelectOption {
            value: "openvpn".to_string(),
            label: "OpenVPN".to_string(),
            disabled: false,
        },
        SelectOption {
            value: "wireguard".to_string(),
            label: "WireGuard".to_string(),
            disabled: false,
        },
        SelectOption {
            value: "ipsec".to_string(),
            label: "IPSec".to_string(),
            disabled: false,
        },
    ];

    let (protocol_string, set_protocol_string) = create_signal(match protocol.get() {
        VpnProtocol::OpenVPN => "openvpn".to_string(),
        VpnProtocol::WireGuard => "wireguard".to_string(),
        VpnProtocol::IPSec => "ipsec".to_string(),
    });

    // Update protocol_string when protocol changes
    create_effect(move |_| {
        let new_value = match protocol.get() {
            VpnProtocol::OpenVPN => "openvpn".to_string(),
            VpnProtocol::WireGuard => "wireguard".to_string(),
            VpnProtocol::IPSec => "ipsec".to_string(),
        };
        set_protocol_string.set(new_value);
    });

    // Update protocol when protocol_string changes
    create_effect(move |_| {
        let proto = match protocol_string.get().as_str() {
            "wireguard" => VpnProtocol::WireGuard,
            "ipsec" => VpnProtocol::IPSec,
            _ => VpnProtocol::OpenVPN,
        };
        set_protocol.set(proto);
    });

    view! {
        <form class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormInput
                    label="Connection Name".to_string()
                    input_type="text".to_string()
                    value=name
                    on_input=set_name
                    placeholder="Enter connection name".to_string()
                    required=true
                />

                <FormSelect
                    label="Protocol".to_string()
                    value=protocol_string
                    on_change=set_protocol_string
                    options=protocol_options
                    required=true
                />

                <FormInput
                    label="Server Address".to_string()
                    input_type="text".to_string()
                    value=server_address
                    on_input=set_server_address
                    placeholder="Enter server address or hostname".to_string()
                    required=true
                />

                <FormInput
                    label="Server Port".to_string()
                    input_type="number".to_string()
                    value=server_port
                    on_input=set_server_port
                    placeholder="1194".to_string()
                    required=true
                />

                // Create auth method options
                {
                    let auth_method_options = vec![
                        SelectOption {
                            value: "certificate".to_string(),
                            label: "Certificate".to_string(),
                            disabled: false,
                        },
                        SelectOption {
                            value: "username_password".to_string(),
                            label: "Username/Password".to_string(),
                            disabled: false,
                        },
                        SelectOption {
                            value: "public_key".to_string(),
                            label: "Public Key".to_string(),
                            disabled: false,
                        },
                        SelectOption {
                            value: "pre_shared_key".to_string(),
                            label: "Pre-shared Key".to_string(),
                            disabled: false,
                        },
                    ];

                    let (auth_method_string, set_auth_method_string) = create_signal(match auth_method.get() {
                        VpnAuthMethod::Certificate => "certificate".to_string(),
                        VpnAuthMethod::UsernamePassword => "username_password".to_string(),
                        VpnAuthMethod::PublicKey => "public_key".to_string(),
                        VpnAuthMethod::PreSharedKey => "pre_shared_key".to_string(),
                    });

                    // Update auth_method_string when auth_method changes
                    create_effect(move |_| {
                        let new_value = match auth_method.get() {
                            VpnAuthMethod::Certificate => "certificate".to_string(),
                            VpnAuthMethod::UsernamePassword => "username_password".to_string(),
                            VpnAuthMethod::PublicKey => "public_key".to_string(),
                            VpnAuthMethod::PreSharedKey => "pre_shared_key".to_string(),
                        };
                        set_auth_method_string.set(new_value);
                    });

                    // Update auth_method when auth_method_string changes
                    create_effect(move |_| {
                        let method = match auth_method_string.get().as_str() {
                            "username_password" => VpnAuthMethod::UsernamePassword,
                            "public_key" => VpnAuthMethod::PublicKey,
                            "pre_shared_key" => VpnAuthMethod::PreSharedKey,
                            _ => VpnAuthMethod::Certificate,
                        };
                        set_auth_method.set(method);
                    });

                    view! {
                        <FormSelect
                            label="Authentication Method".to_string()
                            value=auth_method_string
                            on_change=set_auth_method_string
                            options=auth_method_options
                            required=true
                        />
                    }
                }

                // Create encryption options
                {
                    let encryption_options = vec![
                        SelectOption {
                            value: "aes256".to_string(),
                            label: "AES-256".to_string(),
                            disabled: false,
                        },
                        SelectOption {
                            value: "aes128".to_string(),
                            label: "AES-128".to_string(),
                            disabled: false,
                        },
                        SelectOption {
                            value: "chacha20poly1305".to_string(),
                            label: "ChaCha20-Poly1305".to_string(),
                            disabled: false,
                        },
                        SelectOption {
                            value: "blowfish".to_string(),
                            label: "Blowfish".to_string(),
                            disabled: false,
                        },
                    ];

                    let (encryption_string, set_encryption_string) = create_signal(match encryption.get() {
                        VpnEncryption::AES256 => "aes256".to_string(),
                        VpnEncryption::AES128 => "aes128".to_string(),
                        VpnEncryption::ChaCha20Poly1305 => "chacha20poly1305".to_string(),
                        VpnEncryption::Blowfish => "blowfish".to_string(),
                    });

                    // Update encryption_string when encryption changes
                    create_effect(move |_| {
                        let new_value = match encryption.get() {
                            VpnEncryption::AES256 => "aes256".to_string(),
                            VpnEncryption::AES128 => "aes128".to_string(),
                            VpnEncryption::ChaCha20Poly1305 => "chacha20poly1305".to_string(),
                            VpnEncryption::Blowfish => "blowfish".to_string(),
                        };
                        set_encryption_string.set(new_value);
                    });

                    // Update encryption when encryption_string changes
                    create_effect(move |_| {
                        let enc = match encryption_string.get().as_str() {
                            "aes128" => VpnEncryption::AES128,
                            "chacha20poly1305" => VpnEncryption::ChaCha20Poly1305,
                            "blowfish" => VpnEncryption::Blowfish,
                            _ => VpnEncryption::AES256,
                        };
                        set_encryption.set(enc);
                    });

                    view! {
                        <FormSelect
                            label="Encryption".to_string()
                            value=encryption_string
                            on_change=set_encryption_string
                            options=encryption_options
                            required=true
                        />
                    }
                }
            </div>

            // Username field (conditional)
            {move || if auth_method.get() == VpnAuthMethod::UsernamePassword {
                view! {
                    <FormInput
                        label="Username".to_string()
                        input_type="text".to_string()
                        value=username
                        on_input=set_username
                        placeholder="Enter username".to_string()
                    />
                }.into_view()
            } else {
                view! { <div></div> }.into_view()
            }}

            // Options
            <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900">"Connection Options"</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormCheckbox
                        label="Auto-connect on startup".to_string()
                        checked=auto_connect
                        on_change=set_auto_connect
                    />
                    <FormCheckbox
                        label="Keep connection alive".to_string()
                        checked=keep_alive
                        on_change=set_keep_alive
                    />
                    <FormCheckbox
                        label="Enable compression".to_string()
                        checked=compression
                        on_change=set_compression
                    />
                </div>
            </div>

            // Action buttons
            <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                <Button
                    variant=ButtonVariant::Secondary
                    size=ButtonSize::Medium
                    on_click=Box::new(move || on_cancel())
                >
                    "Cancel"
                </Button>
                <Button
                    variant=ButtonVariant::Primary
                    size=ButtonSize::Medium
                    on_click=Box::new(handle_save)
                >
                    {if config.is_some() { "Update Connection" } else { "Create Connection" }}
                </Button>
            </div>
        </form>
    }
}

/// VPN certificates management tab
#[component]
fn VpnCertificatesTab() -> impl IntoView {
    let (certificates, set_certificates) = create_signal(Vec::<VpnCertificate>::new());
    let (loading, set_loading) = create_signal(false);
    let (show_upload_modal, set_show_upload_modal) = create_signal(false);

    // Load certificates on mount
    create_effect(move |_| {
        spawn_local(async move {
            set_loading.set(true);
            // TODO: Replace with actual API call
            let mock_certs = get_mock_certificates();
            set_certificates.set(mock_certs);
            set_loading.set(false);
        });
    });

    view! {
        <div class="space-y-6">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-lg font-medium text-gray-900">"VPN Certificates"</h2>
                    <p class="text-sm text-gray-600">
                        "Manage SSL/TLS certificates for VPN connections"
                    </p>
                </div>
                <div class="flex space-x-3">
                    <Button
                        variant=ButtonVariant::Secondary
                        size=ButtonSize::Medium
                        on_click=Box::new(move || {
                            // TODO: Generate certificate
                        })
                    >
                        "Generate Certificate"
                    </Button>
                    <Button
                        variant=ButtonVariant::Primary
                        size=ButtonSize::Medium
                        on_click=Box::new(move || set_show_upload_modal.set(true))
                    >
                        "Upload Certificate"
                    </Button>
                </div>
            </div>

            <VpnCertificatesTable
                certificates=certificates.get()
                loading=loading.get()
            />

            // Upload Modal
            <Modal
                open=show_upload_modal
                on_close=set_show_upload_modal
                title="Upload Certificate".to_string()
                size=ModalSize::Medium
            >
                <VpnCertificateUploadForm
                    on_upload=move |_cert| {
                        set_show_upload_modal.set(false);
                        // TODO: Refresh certificates
                    }
                    on_cancel=move || set_show_upload_modal.set(false)
                />
            </Modal>
        </div>
    }
}

/// VPN certificates table component
#[component]
fn VpnCertificatesTable(
    certificates: Vec<VpnCertificate>,
    loading: bool,
) -> impl IntoView {
    let columns = vec![
        AdvancedTableColumn::new("name", "Name")
            .sortable(true)
            .filterable(true)
            .width("200px"),
        AdvancedTableColumn::new("type", "Type")
            .sortable(true)
            .filterable(true)
            .filter_type(FilterType::Select(vec![
                "Server".to_string(),
                "Client".to_string(),
                "CA".to_string(),
            ]))
            .width("120px"),
        AdvancedTableColumn::new("subject", "Subject")
            .sortable(true)
            .filterable(true)
            .width("250px"),
        AdvancedTableColumn::new("issuer", "Issuer")
            .sortable(true)
            .filterable(true)
            .width("200px"),
        AdvancedTableColumn::new("valid_to", "Expires")
            .sortable(true)
            .width("150px"),
        AdvancedTableColumn::new("key_size", "Key Size")
            .sortable(true)
            .width("100px"),
    ];

    let rows: Vec<AdvancedTableRow> = certificates.into_iter().map(|cert| {
        let cert_id = cert.id.clone();
        let mut cells = HashMap::new();

        cells.insert("name".to_string(), AdvancedTableCell {
            key: "name".to_string(),
            content: view! {
                <div class="flex items-center space-x-2">
                    <span class="font-medium text-gray-900">{cert.name.clone()}</span>
                    {if cert.is_ca {
                        view! {
                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                "CA"
                            </span>
                        }.into_view()
                    } else {
                        view! { <span></span> }.into_view()
                    }}
                </div>
            }.into_view(),
            raw_value: cert.name.clone(),
        });

        let type_class = match cert.certificate_type {
            VpnCertificateType::Server => "bg-blue-100 text-blue-800",
            VpnCertificateType::Client => "bg-green-100 text-green-800",
            VpnCertificateType::CA => "bg-purple-100 text-purple-800",
        };

        cells.insert("type".to_string(), AdvancedTableCell {
            key: "type".to_string(),
            content: view! {
                <span class={format!("inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {}", type_class)}>
                    {cert.certificate_type.to_string()}
                </span>
            }.into_view(),
            raw_value: cert.certificate_type.to_string(),
        });

        cells.insert("subject".to_string(), AdvancedTableCell {
            key: "subject".to_string(),
            content: view! {
                <span class="text-sm text-gray-900 truncate" title={cert.subject.clone()}>
                    {cert.subject.clone()}
                </span>
            }.into_view(),
            raw_value: cert.subject.clone(),
        });

        cells.insert("issuer".to_string(), AdvancedTableCell {
            key: "issuer".to_string(),
            content: view! {
                <span class="text-sm text-gray-600 truncate" title={cert.issuer.clone()}>
                    {cert.issuer.clone()}
                </span>
            }.into_view(),
            raw_value: cert.issuer.clone(),
        });

        // Check if certificate is expiring soon (within 30 days)
        let expires_soon = false; // TODO: Implement date parsing and comparison
        let expires_class = if expires_soon { "text-red-600" } else { "text-gray-600" };

        cells.insert("valid_to".to_string(), AdvancedTableCell {
            key: "valid_to".to_string(),
            content: view! {
                <span class={format!("text-sm {}", expires_class)}>
                    {cert.valid_to.clone()}
                </span>
            }.into_view(),
            raw_value: cert.valid_to.clone(),
        });

        cells.insert("key_size".to_string(), AdvancedTableCell {
            key: "key_size".to_string(),
            content: view! {
                <span class="text-sm text-gray-600">
                    {cert.key_size.to_string()}" bits"
                </span>
            }.into_view(),
            raw_value: cert.key_size.to_string(),
        });

        let actions = vec![
            TableAction {
                label: "Download".to_string(),
                icon: None,
                variant: ButtonVariant::Secondary,
                on_click: Some(Box::new(move |_id| {
                    // TODO: Implement certificate download
                })),
            },
            TableAction {
                label: "View Details".to_string(),
                icon: None,
                variant: ButtonVariant::Secondary,
                on_click: Some(Box::new(move |_id| {
                    // TODO: Show certificate details modal
                })),
            },
            TableAction {
                label: "Delete".to_string(),
                icon: None,
                variant: ButtonVariant::Danger,
                on_click: Some(Box::new(move |_id| {
                    // TODO: Implement delete with confirmation
                })),
            },
        ];

        AdvancedTableRow {
            id: cert_id,
            cells,
            actions,
            selectable: true,
            expandable: false,
            expanded_content: None,
        }
    }).collect();

    view! {
        <AdvancedDataTable
            columns=columns
            rows=rows
            loading=loading
            empty_message="No certificates found".to_string()
            selectable=true
            bulk_actions=vec![]
            searchable=true
            paginated=true
            exportable=false
            on_bulk_action=Rc::new(move |action_id, selected_ids| {
                // TODO: Implement bulk actions
                log::info!("Bulk action {} on {:?}", action_id, selected_ids);
            })
        />
    }
}

/// VPN certificate upload form
#[component]
fn VpnCertificateUploadForm(
    on_upload: impl Fn(VpnCertificate) + 'static + Copy,
    on_cancel: impl Fn() + 'static + Copy,
) -> impl IntoView {
    let (name, set_name) = create_signal(String::new());
    let (certificate_file, set_certificate_file) = create_signal(String::new());
    let (private_key_file, set_private_key_file) = create_signal(String::new());
    let (ca_file, set_ca_file) = create_signal(String::new());

    view! {
        <form class="space-y-6">
            <FormInput
                label="Certificate Name".to_string()
                input_type="text".to_string()
                value=name
                on_input=set_name
                placeholder="Enter certificate name".to_string()
                required=true
            />

            <FormInput
                label="Certificate File".to_string()
                input_type="file".to_string()
                value=certificate_file
                on_input=set_certificate_file
                placeholder="Select certificate file (.crt, .pem)".to_string()
                required=true
            />

            <FormInput
                label="Private Key File".to_string()
                input_type="file".to_string()
                value=private_key_file
                on_input=set_private_key_file
                placeholder="Select private key file (.key, .pem)".to_string()
                required=true
            />

            <FormInput
                label="CA Certificate File".to_string()
                input_type="file".to_string()
                value=ca_file
                on_input=set_ca_file
                placeholder="Select CA certificate file (optional)".to_string()
            />

            <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                <Button
                    variant=ButtonVariant::Secondary
                    size=ButtonSize::Medium
                    on_click=Box::new(move || on_cancel())
                >
                    "Cancel"
                </Button>
                <Button
                    variant=ButtonVariant::Primary
                    size=ButtonSize::Medium
                    on_click=Box::new(move || {
                        // TODO: Implement file upload and certificate parsing
                        let mock_cert = VpnCertificate {
                            id: uuid::Uuid::new_v4().to_string(),
                            name: name.get(),
                            certificate_type: VpnCertificateType::Client,
                            subject: "CN=client".to_string(),
                            issuer: "CN=CA".to_string(),
                            valid_from: "2024-01-01".to_string(),
                            valid_to: "2025-01-01".to_string(),
                            fingerprint: "aa:bb:cc:dd".to_string(),
                            key_size: 2048,
                            is_ca: false,
                        };
                        on_upload(mock_cert);
                    })
                >
                    "Upload Certificate"
                </Button>
            </div>
        </form>
    }
}

/// VPN monitoring tab
#[component]
fn VpnMonitoringTab() -> impl IntoView {
    let (active_connections, set_active_connections) = create_signal(Vec::<VpnConnectionStatus>::new());
    let (loading, set_loading) = create_signal(false);

    // Load active connections on mount and refresh periodically
    create_effect(move |_| {
        spawn_local(async move {
            set_loading.set(true);
            // TODO: Replace with actual API call
            let mock_status = get_mock_connection_status();
            set_active_connections.set(mock_status);
            set_loading.set(false);
        });
    });

    view! {
        <div class="space-y-6">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-lg font-medium text-gray-900">"VPN Monitoring"</h2>
                    <p class="text-sm text-gray-600">
                        "Monitor active VPN connections and traffic statistics"
                    </p>
                </div>
                <Button
                    variant=ButtonVariant::Secondary
                    size=ButtonSize::Medium
                    on_click=Box::new(move || {
                        // TODO: Refresh monitoring data
                    })
                >
                    "Refresh"
                </Button>
            </div>

            // Connection status cards
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {move || active_connections.get().into_iter().map(|conn| {
                    view! {
                        <VpnConnectionCard connection=conn />
                    }
                }).collect::<Vec<_>>()}
            </div>

            // Traffic statistics
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">"Traffic Statistics"</h3>
                </div>
                <div class="p-6">
                    <VpnTrafficChart connections=active_connections.get() />
                </div>
            </div>
        </div>
    }
}

/// VPN connection status structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VpnConnectionStatus {
    pub config_id: String,
    pub name: String,
    pub protocol: VpnProtocol,
    pub status: VpnStatus,
    pub connected_since: Option<String>,
    pub client_ip: Option<String>,
    pub server_ip: Option<String>,
    pub bytes_sent: u64,
    pub bytes_received: u64,
    pub packets_sent: u64,
    pub packets_received: u64,
    pub last_activity: String,
}

/// VPN connection status card
#[component]
fn VpnConnectionCard(connection: VpnConnectionStatus) -> impl IntoView {
    let status_color = match connection.status {
        VpnStatus::Connected => "border-green-200 bg-green-50",
        VpnStatus::Disconnected => "border-gray-200 bg-gray-50",
        VpnStatus::Connecting => "border-yellow-200 bg-yellow-50",
        VpnStatus::Error => "border-red-200 bg-red-50",
    };

    let status_icon_color = match connection.status {
        VpnStatus::Connected => "text-green-500",
        VpnStatus::Disconnected => "text-gray-400",
        VpnStatus::Connecting => "text-yellow-500",
        VpnStatus::Error => "text-red-500",
    };

    view! {
        <div class={format!("border rounded-lg p-4 {}", status_color)}>
            <div class="flex items-center justify-between mb-3">
                <h4 class="font-medium text-gray-900">{connection.name}</h4>
                <div class="flex items-center space-x-2">
                    <div class={format!("w-2 h-2 rounded-full {}",
                        if connection.status == VpnStatus::Connected { "bg-green-500" } else { "bg-gray-400" }
                    )}></div>
                    <span class="text-sm text-gray-600">{connection.status.to_string()}</span>
                </div>
            </div>

            <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                    <span class="text-gray-600">"Protocol:"</span>
                    <span class="font-medium">{connection.protocol.to_string()}</span>
                </div>

                {if let Some(client_ip) = connection.client_ip {
                    view! {
                        <div class="flex justify-between">
                            <span class="text-gray-600">"Client IP:"</span>
                            <span class="font-mono text-sm">{client_ip}</span>
                        </div>
                    }.into_view()
                } else {
                    view! { <div></div> }.into_view()
                }}

                {if let Some(connected_since) = connection.connected_since {
                    view! {
                        <div class="flex justify-between">
                            <span class="text-gray-600">"Connected:"</span>
                            <span class="text-sm">{connected_since}</span>
                        </div>
                    }.into_view()
                } else {
                    view! { <div></div> }.into_view()
                }}

                <div class="flex justify-between">
                    <span class="text-gray-600">"Sent:"</span>
                    <span class="font-mono text-sm">{crate::web_utils::format_bytes(connection.bytes_sent)}</span>
                </div>

                <div class="flex justify-between">
                    <span class="text-gray-600">"Received:"</span>
                    <span class="font-mono text-sm">{crate::web_utils::format_bytes(connection.bytes_received)}</span>
                </div>
            </div>
        </div>
    }
}

/// VPN traffic chart component
#[component]
fn VpnTrafficChart(connections: Vec<VpnConnectionStatus>) -> impl IntoView {
    let total_sent: u64 = connections.iter().map(|c| c.bytes_sent).sum();
    let total_received: u64 = connections.iter().map(|c| c.bytes_received).sum();

    view! {
        <div class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">
                        {crate::web_utils::format_bytes(total_sent)}
                    </div>
                    <div class="text-sm text-gray-600">"Total Sent"</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">
                        {crate::web_utils::format_bytes(total_received)}
                    </div>
                    <div class="text-sm text-gray-600">"Total Received"</div>
                </div>
            </div>

            // Connection breakdown
            <div class="space-y-3">
                <h4 class="font-medium text-gray-900">"Per Connection"</h4>
                {connections.into_iter().map(|conn| {
                    let total_traffic = conn.bytes_sent + conn.bytes_received;
                    let sent_percentage = if total_traffic > 0 {
                        (conn.bytes_sent as f64 / total_traffic as f64) * 100.0
                    } else {
                        0.0
                    };

                    view! {
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                            <div class="flex-1">
                                <div class="font-medium text-sm">{conn.name}</div>
                                <div class="flex items-center space-x-4 text-xs text-gray-600 mt-1">
                                    <span>"↑ "{crate::web_utils::format_bytes(conn.bytes_sent)}</span>
                                    <span>"↓ "{crate::web_utils::format_bytes(conn.bytes_received)}</span>
                                </div>
                            </div>
                            <div class="w-24 bg-gray-200 rounded-full h-2">
                                <div
                                    class="bg-blue-500 h-2 rounded-full"
                                    style={format!("width: {}%", sent_percentage)}
                                ></div>
                            </div>
                        </div>
                    }
                }).collect::<Vec<_>>()}
            </div>
        </div>
    }
}

// Mock data functions for development
fn get_mock_vpn_configs() -> Vec<VpnConfig> {
    vec![
        VpnConfig {
            id: "vpn-1".to_string(),
            name: "Office VPN".to_string(),
            protocol: VpnProtocol::OpenVPN,
            enabled: true,
            server_address: "vpn.company.com".to_string(),
            server_port: 1194,
            auth_method: VpnAuthMethod::Certificate,
            encryption: VpnEncryption::AES256,
            local_ip: Some("********".to_string()),
            remote_ip: Some("********".to_string()),
            dns_servers: vec!["*******".to_string(), "*******".to_string()],
            routes: vec![
                VpnRoute {
                    destination: "***********/24".to_string(),
                    gateway: Some("********".to_string()),
                    metric: Some(1),
                }
            ],
            certificate_id: Some("cert-1".to_string()),
            username: None,
            auto_connect: true,
            keep_alive: true,
            compression: false,
            mtu: Some(1500),
            status: VpnStatus::Connected,
            last_connected: Some("2024-01-15 10:30:00".to_string()),
            bytes_sent: 1024 * 1024 * 50, // 50 MB
            bytes_received: 1024 * 1024 * 120, // 120 MB
        },
        VpnConfig {
            id: "vpn-2".to_string(),
            name: "Home WireGuard".to_string(),
            protocol: VpnProtocol::WireGuard,
            enabled: false,
            server_address: "home.example.com".to_string(),
            server_port: 51820,
            auth_method: VpnAuthMethod::PublicKey,
            encryption: VpnEncryption::ChaCha20Poly1305,
            local_ip: Some("********".to_string()),
            remote_ip: Some("********".to_string()),
            dns_servers: vec!["*******".to_string()],
            routes: vec![],
            certificate_id: None,
            username: None,
            auto_connect: false,
            keep_alive: true,
            compression: false,
            mtu: Some(1420),
            status: VpnStatus::Disconnected,
            last_connected: Some("2024-01-10 15:45:00".to_string()),
            bytes_sent: 1024 * 1024 * 25, // 25 MB
            bytes_received: 1024 * 1024 * 80, // 80 MB
        },
    ]
}

fn get_mock_certificates() -> Vec<VpnCertificate> {
    vec![
        VpnCertificate {
            id: "cert-1".to_string(),
            name: "Office Client Certificate".to_string(),
            certificate_type: VpnCertificateType::Client,
            subject: "CN=office-client,O=Company,C=US".to_string(),
            issuer: "CN=Company CA,O=Company,C=US".to_string(),
            valid_from: "2024-01-01 00:00:00".to_string(),
            valid_to: "2025-01-01 00:00:00".to_string(),
            fingerprint: "AA:BB:CC:DD:EE:FF:11:22:33:44:55:66:77:88:99:00".to_string(),
            key_size: 2048,
            is_ca: false,
        },
        VpnCertificate {
            id: "cert-2".to_string(),
            name: "Company CA".to_string(),
            certificate_type: VpnCertificateType::CA,
            subject: "CN=Company CA,O=Company,C=US".to_string(),
            issuer: "CN=Company CA,O=Company,C=US".to_string(),
            valid_from: "2023-01-01 00:00:00".to_string(),
            valid_to: "2028-01-01 00:00:00".to_string(),
            fingerprint: "11:22:33:44:55:66:77:88:99:00:AA:BB:CC:DD:EE:FF".to_string(),
            key_size: 4096,
            is_ca: true,
        },
    ]
}

fn get_mock_connection_status() -> Vec<VpnConnectionStatus> {
    vec![
        VpnConnectionStatus {
            config_id: "vpn-1".to_string(),
            name: "Office VPN".to_string(),
            protocol: VpnProtocol::OpenVPN,
            status: VpnStatus::Connected,
            connected_since: Some("2024-01-15 10:30:00".to_string()),
            client_ip: Some("********".to_string()),
            server_ip: Some("********".to_string()),
            bytes_sent: 1024 * 1024 * 50, // 50 MB
            bytes_received: 1024 * 1024 * 120, // 120 MB
            packets_sent: 45000,
            packets_received: 78000,
            last_activity: "2024-01-15 14:25:30".to_string(),
        },
    ]
}
