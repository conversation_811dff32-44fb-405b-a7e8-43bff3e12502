use leptos::*;
use leptos_meta::*;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use chrono::{DateTime, Utc};
use crate::components::{
    buttons::{<PERSON><PERSON>, <PERSON><PERSON>V<PERSON>t, ButtonSize},
    loading::LoadingSpinner,
};

/// Enhanced package management page with dependency resolution, repository management,
/// update scheduling, and package validation
#[component]
pub fn EnhancedPackagesPage() -> impl IntoView {
    let (active_tab, set_active_tab) = create_signal("packages".to_string());
    let (loading, set_loading) = create_signal(false);
    let (error, set_error) = create_signal(None::<String>);
    let (success, set_success) = create_signal(None::<String>);

    // Package statistics
    let (package_stats, set_package_stats) = create_signal(None::<PackageStatistics>);
    
    // Repository management
    let (repositories, set_repositories) = create_signal(Vec::<RepositoryInfo>::new());
    
    // Update scheduling
    let (update_schedules, set_update_schedules) = create_signal(Vec::<UpdateSchedule>::new());
    
    // Dependency resolution
    let (dependency_result, set_dependency_result) = create_signal(None::<DependencyResolutionResult>);
    let (selected_package_for_deps, set_selected_package_for_deps) = create_signal(String::new());
    
    // Package validation
    let (validation_results, set_validation_results) = create_signal(HashMap::<String, PackageValidationResult>::new());

    // Load initial data
    create_effect(move |_| {
        spawn_local(async move {
            set_loading.set(true);
            
            // Load package statistics
            if let Ok(stats) = fetch_package_statistics().await {
                set_package_stats.set(Some(stats));
            }
            
            // Load repositories
            if let Ok(repos) = fetch_repositories().await {
                set_repositories.set(repos);
            }
            
            // Load update schedules
            if let Ok(schedules) = fetch_update_schedules().await {
                set_update_schedules.set(schedules);
            }
            
            set_loading.set(false);
        });
    });

    view! {
        <div class="min-h-screen bg-gray-50">
            <Title text="Enhanced Package Management"/>
            
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900">"Enhanced Package Management"</h1>
                    <p class="mt-2 text-gray-600">
                        "Comprehensive package management with dependency resolution, repository management, and automated updates"
                    </p>
                </div>

                // Package Statistics Overview
                {move || {
                    if let Some(stats) = package_stats.get() {
                        view! {
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                                <StatCard 
                                    title="Total Packages"
                                    value=stats.total_packages.to_string()
                                    icon="📦"
                                    color="blue"
                                />
                                <StatCard 
                                    title="Installed"
                                    value=stats.installed_packages.to_string()
                                    icon="✅"
                                    color="green"
                                />
                                <StatCard 
                                    title="Available Updates"
                                    value=stats.available_updates.to_string()
                                    icon="🔄"
                                    color="yellow"
                                />
                                <StatCard 
                                    title="Repositories"
                                    value=stats.repositories_count.to_string()
                                    icon="🗂️"
                                    color="purple"
                                />
                            </div>
                        }.into_view()
                    } else {
                        view! {
                            <div class="mb-8">
                                <LoadingSpinner />
                            </div>
                        }.into_view()
                    }
                }}

                // Tab Navigation
                <div class="border-b border-gray-200 mb-6">
                    <nav class="-mb-px flex space-x-8">
                        <TabButton 
                            label="Packages"
                            tab_id="packages"
                            active_tab=active_tab
                            set_active_tab=set_active_tab
                        />
                        <TabButton 
                            label="Dependencies"
                            tab_id="dependencies"
                            active_tab=active_tab
                            set_active_tab=set_active_tab
                        />
                        <TabButton 
                            label="Repositories"
                            tab_id="repositories"
                            active_tab=active_tab
                            set_active_tab=set_active_tab
                        />
                        <TabButton 
                            label="Schedules"
                            tab_id="schedules"
                            active_tab=active_tab
                            set_active_tab=set_active_tab
                        />
                        <TabButton 
                            label="Validation"
                            tab_id="validation"
                            active_tab=active_tab
                            set_active_tab=set_active_tab
                        />
                    </nav>
                </div>

                // Error/Success Messages
                {move || {
                    if let Some(err) = error.get() {
                        view! {
                            <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
                                <p class="text-red-800">{err}</p>
                            </div>
                        }.into_view()
                    } else if let Some(msg) = success.get() {
                        view! {
                            <div class="mb-4 p-4 bg-green-50 border border-green-200 rounded-md">
                                <p class="text-green-800">{msg}</p>
                            </div>
                        }.into_view()
                    } else {
                        view! {}.into_view()
                    }
                }}

                // Tab Content
                <div class="bg-white rounded-lg shadow">
                    {move || {
                        match active_tab.get().as_str() {
                            "packages" => view! {
                                <PackagesTab />
                            }.into_view(),
                            "dependencies" => view! {
                                <DependenciesTab 
                                    dependency_result=dependency_result
                                    set_dependency_result=set_dependency_result
                                    selected_package=selected_package_for_deps
                                    set_selected_package=set_selected_package_for_deps
                                    set_error=set_error
                                />
                            }.into_view(),
                            "repositories" => view! {
                                <RepositoriesTab 
                                    repositories=repositories
                                    set_repositories=set_repositories
                                    set_error=set_error
                                    set_success=set_success
                                />
                            }.into_view(),
                            "schedules" => view! {
                                <SchedulesTab 
                                    schedules=update_schedules
                                    set_schedules=set_update_schedules
                                    set_error=set_error
                                    set_success=set_success
                                />
                            }.into_view(),
                            "validation" => view! {
                                <ValidationTab 
                                    validation_results=validation_results
                                    set_validation_results=set_validation_results
                                    set_error=set_error
                                />
                            }.into_view(),
                            _ => view! {
                                <div class="p-6">
                                    <p class="text-gray-500">"Select a tab to view content"</p>
                                </div>
                            }.into_view(),
                        }
                    }}
                </div>
            </div>
        </div>
    }
}

// Statistics card component
#[component]
fn StatCard(
    title: &'static str,
    value: String,
    icon: &'static str,
    color: &'static str,
) -> impl IntoView {
    let color_classes = match color {
        "blue" => "bg-blue-50 text-blue-600",
        "green" => "bg-green-50 text-green-600", 
        "yellow" => "bg-yellow-50 text-yellow-600",
        "purple" => "bg-purple-50 text-purple-600",
        _ => "bg-gray-50 text-gray-600",
    };

    view! {
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class=format!("flex-shrink-0 p-3 rounded-md {}", color_classes)>
                    <span class="text-2xl">{icon}</span>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">{title}</p>
                    <p class="text-2xl font-semibold text-gray-900">{value}</p>
                </div>
            </div>
        </div>
    }
}

// Tab button component
#[component]
fn TabButton(
    label: &'static str,
    tab_id: &'static str,
    active_tab: ReadSignal<String>,
    set_active_tab: WriteSignal<String>,
) -> impl IntoView {
    let is_active = move || active_tab.get() == tab_id;
    
    view! {
        <button
            class=move || {
                if is_active() {
                    "border-blue-500 text-blue-600 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                } else {
                    "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                }
            }
            on:click=move |_| set_active_tab.set(tab_id.to_string())
        >
            {label}
        </button>
    }
}

// Data structures matching backend API
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PackageStatistics {
    pub total_packages: u32,
    pub installed_packages: u32,
    pub available_updates: u32,
    pub broken_packages: u32,
    pub total_download_size: u64,
    pub total_installed_size: u64,
    pub repositories_count: u32,
    pub last_update_check: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RepositoryInfo {
    pub name: String,
    pub repo_type: String,
    pub url: String,
    pub enabled: bool,
    pub priority: u32,
    pub description: Option<String>,
    pub architecture: Option<String>,
    pub gpg_key: Option<String>,
    pub last_update: Option<DateTime<Utc>>,
    pub health_status: RepositoryHealthStatus,
    pub package_count: u32,
    pub total_size: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RepositoryHealthStatus {
    Healthy,
    Warning { message: String },
    Error { message: String },
    Unknown,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateSchedule {
    pub id: String,
    pub name: String,
    pub enabled: bool,
    pub cron_expression: String,
    pub packages: Vec<String>,
    pub auto_install: bool,
    pub notify_only: bool,
    pub last_run: Option<DateTime<Utc>>,
    pub next_run: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DependencyResolutionResult {
    pub package: String,
    pub dependencies: Vec<DependencyInfo>,
    pub conflicts: Vec<ConflictInfo>,
    pub missing_dependencies: Vec<String>,
    pub install_order: Vec<String>,
    pub total_download_size: u64,
    pub total_installed_size: u64,
    pub resolution_time: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DependencyInfo {
    pub name: String,
    pub version: String,
    pub required_by: Vec<String>,
    pub optional: bool,
    pub satisfied: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConflictInfo {
    pub package: String,
    pub conflicting_package: String,
    pub reason: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PackageValidationResult {
    pub package_name: String,
    pub valid: bool,
    pub signature_valid: bool,
    pub checksum_valid: bool,
    pub dependency_issues: Vec<String>,
    pub security_issues: Vec<String>,
    pub warnings: Vec<String>,
    pub validation_time: DateTime<Utc>,
}

// API functions
async fn fetch_package_statistics() -> Result<PackageStatistics, String> {
    // Mock implementation - in real app would call backend API
    Ok(PackageStatistics {
        total_packages: 1250,
        installed_packages: 85,
        available_updates: 12,
        broken_packages: 2,
        total_download_size: 1024 * 1024 * 150,
        total_installed_size: 1024 * 1024 * 500,
        repositories_count: 4,
        last_update_check: Some(Utc::now()),
    })
}

async fn fetch_repositories() -> Result<Vec<RepositoryInfo>, String> {
    // Mock implementation
    Ok(vec![
        RepositoryInfo {
            name: "openwrt_core".to_string(),
            repo_type: "src/gz".to_string(),
            url: "https://downloads.openwrt.org/releases/23.05.0/targets/ath79/generic/packages".to_string(),
            enabled: true,
            priority: 1,
            description: Some("OpenWrt core packages".to_string()),
            architecture: Some("mips_24kc".to_string()),
            gpg_key: None,
            last_update: Some(Utc::now()),
            health_status: RepositoryHealthStatus::Healthy,
            package_count: 850,
            total_size: 1024 * 1024 * 120,
        },
    ])
}

async fn fetch_update_schedules() -> Result<Vec<UpdateSchedule>, String> {
    // Mock implementation
    Ok(vec![
        UpdateSchedule {
            id: "daily-security".to_string(),
            name: "Daily Security Updates".to_string(),
            enabled: true,
            cron_expression: "0 2 * * *".to_string(),
            packages: vec![],
            auto_install: true,
            notify_only: false,
            last_run: Some(Utc::now() - chrono::Duration::days(1)),
            next_run: Some(Utc::now() + chrono::Duration::hours(6)),
            created_at: Utc::now() - chrono::Duration::days(30),
            updated_at: Utc::now() - chrono::Duration::days(1),
        },
    ])
}

// ============================================================================
// TAB COMPONENTS
// ============================================================================

/// Packages tab - enhanced package listing with advanced features
#[component]
fn PackagesTab() -> impl IntoView {
    view! {
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-900">"Package Management"</h2>
                <div class="flex space-x-3">
                    <Button
                        variant=ButtonVariant::Secondary
                        size=ButtonSize::Medium
                        on_click=Box::new(|| {})
                    >
                        "Refresh Lists"
                    </Button>
                    <Button
                        variant=ButtonVariant::Primary
                        size=ButtonSize::Medium
                        on_click=Box::new(|| {})
                    >
                        "Update All"
                    </Button>
                </div>
            </div>

            <div class="bg-gray-50 rounded-lg p-4">
                <p class="text-gray-600 text-center">
                    "Enhanced package management interface will be integrated here."
                    <br/>
                    "This will include the existing packages.rs functionality with additional features."
                </p>
            </div>
        </div>
    }
}

/// Dependencies tab - package dependency resolution and visualization
#[component]
fn DependenciesTab(
    dependency_result: ReadSignal<Option<DependencyResolutionResult>>,
    set_dependency_result: WriteSignal<Option<DependencyResolutionResult>>,
    selected_package: ReadSignal<String>,
    set_selected_package: WriteSignal<String>,
    set_error: WriteSignal<Option<String>>,
) -> impl IntoView {
    let (resolving, set_resolving) = create_signal(false);

    let resolve_dependencies = move || {
        let package_name = selected_package.get();
        if package_name.is_empty() {
            set_error.set(Some("Please enter a package name".to_string()));
            return;
        }

        set_resolving.set(true);
        spawn_local(async move {
            // Mock dependency resolution
            let result = DependencyResolutionResult {
                package: package_name.clone(),
                dependencies: vec![
                    DependencyInfo {
                        name: "libc".to_string(),
                        version: "1.2.3".to_string(),
                        required_by: vec![package_name.clone()],
                        optional: false,
                        satisfied: true,
                    },
                    DependencyInfo {
                        name: "openssl".to_string(),
                        version: "1.1.1".to_string(),
                        required_by: vec![package_name.clone()],
                        optional: false,
                        satisfied: true,
                    },
                ],
                conflicts: vec![],
                missing_dependencies: vec![],
                install_order: vec!["libc".to_string(), "openssl".to_string(), package_name.clone()],
                total_download_size: 1024 * 1024 * 25,
                total_installed_size: 1024 * 1024 * 80,
                resolution_time: Utc::now(),
            };

            set_dependency_result.set(Some(result));
            set_resolving.set(false);
        });
    };

    view! {
        <div class="p-6">
            <div class="mb-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">"Dependency Resolution"</h2>

                <div class="flex space-x-4 mb-6">
                    <div class="flex-1">
                        <input
                            type="text"
                            placeholder="Enter package name..."
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            prop:value=move || selected_package.get()
                            on:input=move |ev| {
                                set_selected_package.set(event_target_value(&ev));
                            }
                        />
                    </div>
                    <Button
                        variant=ButtonVariant::Primary
                        size=ButtonSize::Medium
                        on_click=Box::new(resolve_dependencies)
                        disabled=resolving.get()
                    >
                        {move || if resolving.get() { "Resolving..." } else { "Resolve Dependencies" }}
                    </Button>
                </div>
            </div>

            {move || {
                if let Some(result) = dependency_result.get() {
                    view! {
                        <div class="space-y-6">
                            // Dependencies section
                            <div class="bg-white border rounded-lg p-4">
                                <h3 class="text-lg font-medium text-gray-900 mb-3">"Dependencies"</h3>
                                <div class="space-y-2">
                                    {result.dependencies.into_iter().map(|dep| {
                                        let status_color = if dep.satisfied { "text-green-600" } else { "text-red-600" };
                                        let status_icon = if dep.satisfied { "✅" } else { "❌" };

                                        view! {
                                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                                                <div class="flex items-center space-x-3">
                                                    <span class="text-lg">{status_icon}</span>
                                                    <div>
                                                        <span class="font-medium">{dep.name}</span>
                                                        <span class="text-gray-500 ml-2">{"v"}{dep.version}</span>
                                                    </div>
                                                </div>
                                                <span class=format!("text-sm {}", status_color)>
                                                    {if dep.satisfied { "Satisfied" } else { "Missing" }}
                                                </span>
                                            </div>
                                        }
                                    }).collect::<Vec<_>>()}
                                </div>
                            </div>

                            // Install order section
                            <div class="bg-white border rounded-lg p-4">
                                <h3 class="text-lg font-medium text-gray-900 mb-3">"Installation Order"</h3>
                                <div class="flex flex-wrap gap-2">
                                    {result.install_order.clone().into_iter().enumerate().map(|(i, pkg)| {
                                        let total_len = result.install_order.len();
                                        view! {
                                            <div class="flex items-center space-x-2">
                                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">
                                                    {i + 1}". "{pkg}
                                                </span>
                                                {if i < total_len - 1 {
                                                    view! { <span class="text-gray-400">"→"</span> }.into_view()
                                                } else {
                                                    view! {}.into_view()
                                                }}
                                            </div>
                                        }
                                    }).collect::<Vec<_>>()}
                                </div>
                            </div>

                            // Size information
                            <div class="bg-white border rounded-lg p-4">
                                <h3 class="text-lg font-medium text-gray-900 mb-3">"Size Information"</h3>
                                <div class="grid grid-cols-2 gap-4">
                                    <div class="text-center p-3 bg-blue-50 rounded">
                                        <div class="text-2xl font-bold text-blue-600">
                                            {format_size(result.total_download_size)}
                                        </div>
                                        <div class="text-sm text-blue-600">"Download Size"</div>
                                    </div>
                                    <div class="text-center p-3 bg-green-50 rounded">
                                        <div class="text-2xl font-bold text-green-600">
                                            {format_size(result.total_installed_size)}
                                        </div>
                                        <div class="text-sm text-green-600">"Installed Size"</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }.into_view()
                } else {
                    view! {
                        <div class="text-center py-12 text-gray-500">
                            <p>"Enter a package name and click 'Resolve Dependencies' to see dependency information."</p>
                        </div>
                    }.into_view()
                }
            }}
        </div>
    }
}

// Helper function to format file sizes
fn format_size(bytes: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = bytes as f64;
    let mut unit_index = 0;

    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }

    if unit_index == 0 {
        format!("{} {}", bytes, UNITS[unit_index])
    } else {
        format!("{:.1} {}", size, UNITS[unit_index])
    }
}

/// Repositories tab - repository management interface
#[component]
fn RepositoriesTab(
    repositories: ReadSignal<Vec<RepositoryInfo>>,
    set_repositories: WriteSignal<Vec<RepositoryInfo>>,
    set_error: WriteSignal<Option<String>>,
    set_success: WriteSignal<Option<String>>,
) -> impl IntoView {
    let (show_add_form, set_show_add_form) = create_signal(false);
    let (testing_repo, set_testing_repo) = create_signal(None::<String>);

    let test_repository = move |repo_name: String| {
        set_testing_repo.set(Some(repo_name.clone()));
        spawn_local(async move {
            // Mock repository test - simulate delay
            gloo_timers::future::TimeoutFuture::new(1000).await;
            set_testing_repo.set(None);
            set_success.set(Some(format!("Repository '{}' is accessible", repo_name)));
        });
    };

    view! {
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-900">"Repository Management"</h2>
                <Button
                    variant=ButtonVariant::Primary
                    size=ButtonSize::Medium
                    on_click=Box::new(move || set_show_add_form.set(true))
                >
                    "Add Repository"
                </Button>
            </div>

            <div class="space-y-4">
                {move || {
                    repositories.get().into_iter().map(|repo| {
                        let repo_name = repo.name.clone();
                        let is_testing = move || testing_repo.get().as_ref() == Some(&repo_name);

                        let health_badge = match &repo.health_status {
                            RepositoryHealthStatus::Healthy =>
                                view! { <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">"Healthy"</span> },
                            RepositoryHealthStatus::Warning { message } =>
                                view! { <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs" title=message>"Warning"</span> },
                            RepositoryHealthStatus::Error { message } =>
                                view! { <span class="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs" title=message>"Error"</span> },
                            RepositoryHealthStatus::Unknown =>
                                view! { <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs">"Unknown"</span> },
                        };

                        view! {
                            <div class="bg-white border rounded-lg p-4">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-3 mb-2">
                                            <h3 class="text-lg font-medium text-gray-900">{repo.name.clone()}</h3>
                                            {health_badge}
                                            {if repo.enabled {
                                                view! { <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">"Enabled"</span> }.into_view()
                                            } else {
                                                view! { <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs">"Disabled"</span> }.into_view()
                                            }}
                                        </div>
                                        <p class="text-sm text-gray-600 mb-2">{repo.url}</p>
                                        {if let Some(desc) = &repo.description {
                                            view! { <p class="text-sm text-gray-500">{desc}</p> }.into_view()
                                        } else {
                                            view! {}.into_view()
                                        }}
                                        <div class="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                                            <span>"Priority: "{repo.priority}</span>
                                            <span>"Packages: "{repo.package_count}</span>
                                            <span>"Size: "{format_size(repo.total_size)}</span>
                                        </div>
                                    </div>
                                    <div class="flex space-x-2">
                                        <Button
                                            variant=ButtonVariant::Secondary
                                            size=ButtonSize::Small
                                            on_click=Box::new({
                                                let repo_name = repo.name.clone();
                                                move || test_repository(repo_name.clone())
                                            })
                                            disabled=is_testing()
                                        >
                                            {move || if is_testing() { "Testing..." } else { "Test" }}
                                        </Button>
                                        <Button
                                            variant=ButtonVariant::Secondary
                                            size=ButtonSize::Small
                                            on_click=Box::new(|| {})
                                        >
                                            "Edit"
                                        </Button>
                                        <Button
                                            variant=ButtonVariant::Error
                                            size=ButtonSize::Small
                                            on_click=Box::new(|| {})
                                        >
                                            "Remove"
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        }
                    }).collect::<Vec<_>>()
                }}
            </div>

            {move || {
                if show_add_form.get() {
                    view! {
                        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                            <div class="bg-white rounded-lg p-6 w-full max-w-md">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">"Add Repository"</h3>
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">"Name"</label>
                                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">"URL"</label>
                                        <input type="url" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">"Type"</label>
                                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <option value="src/gz">"src/gz"</option>
                                            <option value="src">"src"</option>
                                            <option value="dest">"dest"</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="flex justify-end space-x-3 mt-6">
                                    <Button
                                        variant=ButtonVariant::Secondary
                                        size=ButtonSize::Medium
                                        on_click=Box::new(move || set_show_add_form.set(false))
                                    >
                                        "Cancel"
                                    </Button>
                                    <Button
                                        variant=ButtonVariant::Primary
                                        size=ButtonSize::Medium
                                        on_click=Box::new(move || {
                                            set_show_add_form.set(false);
                                            set_success.set(Some("Repository added successfully".to_string()));
                                        })
                                    >
                                        "Add Repository"
                                    </Button>
                                </div>
                            </div>
                        </div>
                    }.into_view()
                } else {
                    view! {}.into_view()
                }
            }}
        </div>
    }
}

/// Schedules tab - update scheduling management
#[component]
fn SchedulesTab(
    schedules: ReadSignal<Vec<UpdateSchedule>>,
    set_schedules: WriteSignal<Vec<UpdateSchedule>>,
    set_error: WriteSignal<Option<String>>,
    set_success: WriteSignal<Option<String>>,
) -> impl IntoView {
    let (show_add_form, set_show_add_form) = create_signal(false);

    view! {
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-900">"Update Scheduling"</h2>
                <Button
                    variant=ButtonVariant::Primary
                    size=ButtonSize::Medium
                    on_click=Box::new(move || set_show_add_form.set(true))
                >
                    "Add Schedule"
                </Button>
            </div>

            <div class="space-y-4">
                {move || {
                    schedules.get().into_iter().map(|schedule| {
                        let next_run_text = if let Some(next_run) = schedule.next_run {
                            format!("Next: {}", next_run.format("%Y-%m-%d %H:%M"))
                        } else {
                            "Not scheduled".to_string()
                        };

                        let last_run_text = if let Some(last_run) = schedule.last_run {
                            format!("Last: {}", last_run.format("%Y-%m-%d %H:%M"))
                        } else {
                            "Never run".to_string()
                        };

                        view! {
                            <div class="bg-white border rounded-lg p-4">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-3 mb-2">
                                            <h3 class="text-lg font-medium text-gray-900">{schedule.name.clone()}</h3>
                                            {if schedule.enabled {
                                                view! { <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">"Enabled"</span> }.into_view()
                                            } else {
                                                view! { <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs">"Disabled"</span> }.into_view()
                                            }}
                                            {if schedule.auto_install {
                                                view! { <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">"Auto Install"</span> }.into_view()
                                            } else {
                                                view! { <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs">"Notify Only"</span> }.into_view()
                                            }}
                                        </div>
                                        <div class="text-sm text-gray-600 space-y-1">
                                            <p>"Cron: "{schedule.cron_expression}</p>
                                            <p>{next_run_text}</p>
                                            <p>{last_run_text}</p>
                                            {if schedule.packages.is_empty() {
                                                view! { <p>"Packages: All packages"</p> }.into_view()
                                            } else {
                                                view! { <p>"Packages: "{schedule.packages.len()}" specific packages"</p> }.into_view()
                                            }}
                                        </div>
                                    </div>
                                    <div class="flex space-x-2">
                                        <Button
                                            variant=ButtonVariant::Secondary
                                            size=ButtonSize::Small
                                            on_click=Box::new(|| {})
                                        >
                                            "Run Now"
                                        </Button>
                                        <Button
                                            variant=ButtonVariant::Secondary
                                            size=ButtonSize::Small
                                            on_click=Box::new(|| {})
                                        >
                                            "Edit"
                                        </Button>
                                        <Button
                                            variant=ButtonVariant::Error
                                            size=ButtonSize::Small
                                            on_click=Box::new(|| {})
                                        >
                                            "Delete"
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        }
                    }).collect::<Vec<_>>()
                }}
            </div>

            {move || {
                if show_add_form.get() {
                    view! {
                        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                            <div class="bg-white rounded-lg p-6 w-full max-w-md">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">"Add Update Schedule"</h3>
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">"Name"</label>
                                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">"Cron Expression"</label>
                                        <input type="text" placeholder="0 2 * * *" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
                                        <p class="text-xs text-gray-500 mt-1">"Example: 0 2 * * * (daily at 2 AM)"</p>
                                    </div>
                                    <div class="flex items-center space-x-4">
                                        <label class="flex items-center">
                                            <input type="checkbox" class="mr-2" />
                                            <span class="text-sm text-gray-700">"Auto Install"</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="checkbox" class="mr-2" />
                                            <span class="text-sm text-gray-700">"Enabled"</span>
                                        </label>
                                    </div>
                                </div>
                                <div class="flex justify-end space-x-3 mt-6">
                                    <Button
                                        variant=ButtonVariant::Secondary
                                        size=ButtonSize::Medium
                                        on_click=Box::new(move || set_show_add_form.set(false))
                                    >
                                        "Cancel"
                                    </Button>
                                    <Button
                                        variant=ButtonVariant::Primary
                                        size=ButtonSize::Medium
                                        on_click=Box::new(move || {
                                            set_show_add_form.set(false);
                                            set_success.set(Some("Update schedule created successfully".to_string()));
                                        })
                                    >
                                        "Create Schedule"
                                    </Button>
                                </div>
                            </div>
                        </div>
                    }.into_view()
                } else {
                    view! {}.into_view()
                }
            }}
        </div>
    }
}

/// Validation tab - package validation and security checking
#[component]
fn ValidationTab(
    validation_results: ReadSignal<HashMap<String, PackageValidationResult>>,
    set_validation_results: WriteSignal<HashMap<String, PackageValidationResult>>,
    set_error: WriteSignal<Option<String>>,
) -> impl IntoView {
    let (package_to_validate, set_package_to_validate) = create_signal(String::new());
    let (validating, set_validating) = create_signal(false);

    let validate_package = move || {
        let package_name = package_to_validate.get();
        if package_name.is_empty() {
            set_error.set(Some("Please enter a package name".to_string()));
            return;
        }

        set_validating.set(true);
        spawn_local(async move {
            // Mock package validation
            let result = PackageValidationResult {
                package_name: package_name.clone(),
                valid: true,
                signature_valid: true,
                checksum_valid: true,
                dependency_issues: vec![],
                security_issues: if package_name == "old-package" {
                    vec!["Package contains known security vulnerability CVE-2023-1234".to_string()]
                } else {
                    vec![]
                },
                warnings: if package_name == "experimental-package" {
                    vec!["Package is marked as experimental".to_string()]
                } else {
                    vec![]
                },
                validation_time: Utc::now(),
            };

            let mut results = validation_results.get();
            results.insert(package_name, result);
            set_validation_results.set(results);
            set_validating.set(false);
        });
    };

    view! {
        <div class="p-6">
            <div class="mb-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">"Package Validation"</h2>

                <div class="flex space-x-4 mb-6">
                    <div class="flex-1">
                        <input
                            type="text"
                            placeholder="Enter package name to validate..."
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            prop:value=move || package_to_validate.get()
                            on:input=move |ev| {
                                set_package_to_validate.set(event_target_value(&ev));
                            }
                        />
                    </div>
                    <Button
                        variant=ButtonVariant::Primary
                        size=ButtonSize::Medium
                        on_click=Box::new(validate_package)
                        disabled=validating.get()
                    >
                        {move || if validating.get() { "Validating..." } else { "Validate Package" }}
                    </Button>
                </div>
            </div>

            <div class="space-y-4">
                {move || {
                    validation_results.get().into_iter().map(|(package_name, result)| {
                        let overall_status = if result.valid {
                            ("✅", "Valid", "text-green-600")
                        } else {
                            ("❌", "Invalid", "text-red-600")
                        };

                        view! {
                            <div class="bg-white border rounded-lg p-4">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center space-x-3">
                                        <span class="text-2xl">{overall_status.0}</span>
                                        <div>
                                            <h3 class="text-lg font-medium text-gray-900">{package_name}</h3>
                                            <span class=format!("text-sm {}", overall_status.2)>{overall_status.1}</span>
                                        </div>
                                    </div>
                                    <span class="text-sm text-gray-500">
                                        {result.validation_time.format("%Y-%m-%d %H:%M:%S").to_string()}
                                    </span>
                                </div>

                                <div class="grid grid-cols-2 gap-4 mb-4">
                                    <div class="flex items-center space-x-2">
                                        <span class="text-lg">{if result.signature_valid { "✅" } else { "❌" }}</span>
                                        <span class="text-sm">"Signature Valid"</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span class="text-lg">{if result.checksum_valid { "✅" } else { "❌" }}</span>
                                        <span class="text-sm">"Checksum Valid"</span>
                                    </div>
                                </div>

                                {if !result.security_issues.is_empty() {
                                    view! {
                                        <div class="mb-4 p-3 bg-red-50 border border-red-200 rounded">
                                            <h4 class="text-sm font-medium text-red-800 mb-2">"Security Issues"</h4>
                                            <ul class="text-sm text-red-700 space-y-1">
                                                {result.security_issues.into_iter().map(|issue| {
                                                    view! { <li>"• "{issue}</li> }
                                                }).collect::<Vec<_>>()}
                                            </ul>
                                        </div>
                                    }.into_view()
                                } else {
                                    view! {}.into_view()
                                }}

                                {if !result.warnings.is_empty() {
                                    view! {
                                        <div class="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
                                            <h4 class="text-sm font-medium text-yellow-800 mb-2">"Warnings"</h4>
                                            <ul class="text-sm text-yellow-700 space-y-1">
                                                {result.warnings.into_iter().map(|warning| {
                                                    view! { <li>"• "{warning}</li> }
                                                }).collect::<Vec<_>>()}
                                            </ul>
                                        </div>
                                    }.into_view()
                                } else {
                                    view! {}.into_view()
                                }}
                            </div>
                        }
                    }).collect::<Vec<_>>()
                }}
            </div>

            {move || {
                if validation_results.get().is_empty() {
                    view! {
                        <div class="text-center py-12 text-gray-500">
                            <p>"Enter a package name and click 'Validate Package' to check package integrity and security."</p>
                        </div>
                    }.into_view()
                } else {
                    view! {}.into_view()
                }
            }}
        </div>
    }
}
