@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom OpenWrt LuCI styles */
.luci-container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.luci-card {
  @apply bg-white rounded-lg shadow-md border border-gray-200;
}

.luci-button-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200;
}

.luci-button-secondary {
  @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-md transition-colors duration-200;
}

.luci-input {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500;
}

.luci-nav-item {
  @apply block px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200;
}

.luci-nav-item-active {
  @apply bg-blue-100 text-blue-700;
}

.luci-nav-item-inactive {
  @apply text-gray-600 hover:text-gray-900 hover:bg-gray-50;
}

.luci-table {
  @apply min-w-full divide-y divide-gray-200;
}

.luci-table-header {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.luci-table-cell {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

.luci-modal-overlay {
  @apply fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50;
}

.luci-modal-content {
  @apply relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white;
}

.luci-form-group {
  @apply mb-4;
}

.luci-form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.luci-form-error {
  @apply text-red-600 text-sm mt-1;
}

.luci-alert-success {
  @apply bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-md;
}

.luci-alert-error {
  @apply bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-md;
}

.luci-alert-warning {
  @apply bg-yellow-50 border border-yellow-200 text-yellow-800 px-4 py-3 rounded-md;
}

.luci-alert-info {
  @apply bg-blue-50 border border-blue-200 text-blue-800 px-4 py-3 rounded-md;
}

/* Loading spinner */
.luci-spinner {
  @apply inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600;
}

/* Progress bar */
.luci-progress-bar {
  @apply w-full bg-gray-200 rounded-full h-2;
}

.luci-progress-fill {
  @apply bg-blue-600 h-2 rounded-full transition-all duration-300;
}

/* Responsive navigation */
@media (max-width: 768px) {
  .luci-nav-mobile {
    @apply block;
  }
  
  .luci-nav-desktop {
    @apply hidden;
  }
}

@media (min-width: 769px) {
  .luci-nav-mobile {
    @apply hidden;
  }
  
  .luci-nav-desktop {
    @apply block;
  }
}
