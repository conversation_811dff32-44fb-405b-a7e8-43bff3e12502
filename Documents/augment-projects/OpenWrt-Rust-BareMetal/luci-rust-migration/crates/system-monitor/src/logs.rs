//! Log Monitoring
//!
//! This module provides system log monitoring and analysis functionality.

use crate::{MonitorError, MonitorResult};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{SystemTime, UNIX_EPOCH};

/// Log level
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord, Hash)]
pub enum LogLevel {
    Emergency = 0,
    Alert = 1,
    Critical = 2,
    Error = 3,
    Warning = 4,
    Notice = 5,
    Info = 6,
    Debug = 7,
}

impl std::fmt::Display for LogLevel {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            LogLevel::Emergency => write!(f, "EMERG"),
            LogLevel::Alert => write!(f, "ALERT"),
            LogLevel::Critical => write!(f, "CRIT"),
            LogLevel::Error => write!(f, "ERR"),
            LogLevel::Warning => write!(f, "WARNING"),
            LogLevel::Notice => write!(f, "NOTICE"),
            LogLevel::Info => write!(f, "INFO"),
            LogLevel::Debug => write!(f, "DEBUG"),
        }
    }
}

impl From<u8> for LogLevel {
    fn from(level: u8) -> Self {
        match level {
            0 => LogLevel::Emergency,
            1 => LogLevel::Alert,
            2 => LogLevel::Critical,
            3 => LogLevel::Error,
            4 => LogLevel::Warning,
            5 => LogLevel::Notice,
            6 => LogLevel::Info,
            7 => LogLevel::Debug,
            _ => LogLevel::Info,
        }
    }
}

/// Log facility
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum LogFacility {
    Kernel,
    User,
    Mail,
    Daemon,
    Auth,
    Syslog,
    Lpr,
    News,
    Uucp,
    Cron,
    Authpriv,
    Ftp,
    Local0,
    Local1,
    Local2,
    Local3,
    Local4,
    Local5,
    Local6,
    Local7,
}

impl std::fmt::Display for LogFacility {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            LogFacility::Kernel => write!(f, "kern"),
            LogFacility::User => write!(f, "user"),
            LogFacility::Mail => write!(f, "mail"),
            LogFacility::Daemon => write!(f, "daemon"),
            LogFacility::Auth => write!(f, "auth"),
            LogFacility::Syslog => write!(f, "syslog"),
            LogFacility::Lpr => write!(f, "lpr"),
            LogFacility::News => write!(f, "news"),
            LogFacility::Uucp => write!(f, "uucp"),
            LogFacility::Cron => write!(f, "cron"),
            LogFacility::Authpriv => write!(f, "authpriv"),
            LogFacility::Ftp => write!(f, "ftp"),
            LogFacility::Local0 => write!(f, "local0"),
            LogFacility::Local1 => write!(f, "local1"),
            LogFacility::Local2 => write!(f, "local2"),
            LogFacility::Local3 => write!(f, "local3"),
            LogFacility::Local4 => write!(f, "local4"),
            LogFacility::Local5 => write!(f, "local5"),
            LogFacility::Local6 => write!(f, "local6"),
            LogFacility::Local7 => write!(f, "local7"),
        }
    }
}

/// Log entry
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogEntry {
    pub timestamp: SystemTime,
    pub facility: LogFacility,
    pub level: LogLevel,
    pub hostname: String,
    pub process: String,
    pub pid: Option<u32>,
    pub message: String,
}

/// Log filter criteria
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogFilter {
    pub facility: Option<LogFacility>,
    pub level: Option<LogLevel>,
    pub process: Option<String>,
    pub message_contains: Option<String>,
    pub since: Option<SystemTime>,
    pub until: Option<SystemTime>,
}

impl Default for LogFilter {
    fn default() -> Self {
        Self {
            facility: None,
            level: None,
            process: None,
            message_contains: None,
            since: None,
            until: None,
        }
    }
}

/// Log statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogStats {
    pub total_entries: usize,
    pub by_level: HashMap<LogLevel, usize>,
    pub by_facility: HashMap<LogFacility, usize>,
    pub by_process: HashMap<String, usize>,
    pub error_count: usize,
    pub warning_count: usize,
}

/// Log monitor
pub struct LogMonitor {
    log_entries: Vec<LogEntry>,
    max_entries: usize,
}

impl LogMonitor {
    /// Create a new log monitor
    pub fn new() -> MonitorResult<Self> {
        Ok(Self {
            log_entries: Vec::new(),
            max_entries: 10000, // Keep last 10000 log entries
        })
    }

    /// Load system logs
    pub fn load_logs(&mut self) -> MonitorResult<()> {
        self.log_entries.clear();

        // Read kernel logs from dmesg
        if let Ok(kernel_logs) = self.read_kernel_logs() {
            self.log_entries.extend(kernel_logs);
        }

        // Read syslog from /var/log/messages or logread
        if let Ok(syslog_entries) = self.read_syslog() {
            self.log_entries.extend(syslog_entries);
        }

        // Read application logs from /var/log/
        if let Ok(app_logs) = self.read_application_logs() {
            self.log_entries.extend(app_logs);
        }

        // Sort by timestamp (newest first)
        self.log_entries.sort_by(|a, b| b.timestamp.cmp(&a.timestamp));

        Ok(())
    }

    /// Read kernel logs from dmesg
    fn read_kernel_logs(&self) -> MonitorResult<Vec<LogEntry>> {
        let mut logs = Vec::new();

        // Try to read from dmesg command first
        if let Ok(output) = std::process::Command::new("dmesg")
            .arg("-T") // Human readable timestamps
            .arg("-l") // Show log levels
            .output() {

            if output.status.success() {
                let content = String::from_utf8_lossy(&output.stdout);
                for line in content.lines() {
                    if let Ok(entry) = self.parse_dmesg_line(line) {
                        logs.push(entry);
                    }
                }
            }
        }

        // Fallback: try reading /proc/kmsg (requires root)
        if logs.is_empty() {
            if let Ok(content) = std::fs::read_to_string("/proc/kmsg") {
                for line in content.lines().take(100) { // Limit to avoid blocking
                    if let Ok(entry) = self.parse_kmsg_line(line) {
                        logs.push(entry);
                    }
                }
            }
        }

        Ok(logs)
    }

    /// Read syslog entries using OpenWrt's logread command
    fn read_syslog(&self) -> MonitorResult<Vec<LogEntry>> {
        let mut logs = Vec::new();

        // Try OpenWrt's logread command first (preferred method)
        if let Ok(output) = std::process::Command::new("logread")
            .output() {

            if output.status.success() {
                let content = String::from_utf8_lossy(&output.stdout);
                for line in content.lines() {
                    if let Ok(entry) = self.parse_logread_line(line) {
                        logs.push(entry);
                    }
                }
                return Ok(logs);
            }
        }

        // Fallback: try reading from common syslog locations
        let syslog_paths = [
            "/var/log/messages",
            "/var/log/syslog",
            "/tmp/messages",
            "/tmp/syslog"
        ];

        for path in &syslog_paths {
            if let Ok(content) = std::fs::read_to_string(path) {
                for line in content.lines() {
                    if let Ok(entry) = self.parse_syslog_line(line) {
                        logs.push(entry);
                    }
                }
                break; // Use first available log file
            }
        }

        Ok(logs)
    }

    /// Read application logs from /var/log/
    fn read_application_logs(&self) -> MonitorResult<Vec<LogEntry>> {
        let mut logs = Vec::new();

        // Common application log files in OpenWrt
        let log_files = [
            "/var/log/dropbear.log",
            "/var/log/uhttpd.log",
            "/var/log/hostapd.log",
            "/var/log/dnsmasq.log",
            "/var/log/firewall.log"
        ];

        for path in &log_files {
            if let Ok(content) = std::fs::read_to_string(path) {
                let process_name = std::path::Path::new(path)
                    .file_stem()
                    .and_then(|s| s.to_str())
                    .unwrap_or("unknown");

                for line in content.lines() {
                    if let Ok(entry) = self.parse_application_log_line(line, process_name) {
                        logs.push(entry);
                    }
                }
            }
        }

        Ok(logs)
    }

    /// Parse dmesg line with timestamp and log level
    fn parse_dmesg_line(&self, line: &str) -> MonitorResult<LogEntry> {
        // dmesg format: [timestamp] message or [timestamp] <level>message
        let line = line.trim();

        // Extract timestamp if present
        let (timestamp, rest_str) = if line.starts_with('[') {
            if let Some(end) = line.find(']') {
                let timestamp_str = &line[1..end];
                let rest = line[end + 1..].trim();

                // Parse timestamp (could be seconds since boot or actual time)
                let timestamp = if let Ok(secs) = timestamp_str.parse::<f64>() {
                    // Boot time + seconds since boot
                    SystemTime::now() - std::time::Duration::from_secs_f64(secs)
                } else {
                    // Try parsing as actual timestamp
                    SystemTime::now() // Fallback to current time
                };

                (timestamp, rest.to_string())
            } else {
                (SystemTime::now(), line.to_string())
            }
        } else {
            (SystemTime::now(), line.to_string())
        };

        // Extract log level if present
        let (level, message) = if rest_str.starts_with('<') && rest_str.len() > 2 {
            if let Some(end) = rest_str.find('>') {
                let level_str = &rest_str[1..end];
                let message = rest_str[end + 1..].trim();

                let level = if let Ok(level_num) = level_str.parse::<u8>() {
                    LogLevel::from(level_num & 7) // Extract priority from facility.priority
                } else {
                    LogLevel::Info
                };

                (level, message.to_string())
            } else {
                (LogLevel::Info, rest_str)
            }
        } else {
            (LogLevel::Info, rest_str)
        };

        Ok(LogEntry {
            timestamp,
            facility: LogFacility::Kernel,
            level,
            hostname: "localhost".to_string(),
            process: "kernel".to_string(),
            pid: None,
            message,
        })
    }

    /// Parse /proc/kmsg line
    fn parse_kmsg_line(&self, line: &str) -> MonitorResult<LogEntry> {
        // kmsg format: <priority>message
        let line = line.trim();

        let (level, message) = if line.starts_with('<') {
            if let Some(end) = line.find('>') {
                let priority_str = &line[1..end];
                let message = &line[end + 1..];

                let level = if let Ok(priority) = priority_str.parse::<u8>() {
                    LogLevel::from(priority & 7) // Extract priority from facility.priority
                } else {
                    LogLevel::Info
                };

                (level, message)
            } else {
                (LogLevel::Info, line)
            }
        } else {
            (LogLevel::Info, line)
        };

        Ok(LogEntry {
            timestamp: SystemTime::now(),
            facility: LogFacility::Kernel,
            level,
            hostname: "localhost".to_string(),
            process: "kernel".to_string(),
            pid: None,
            message: message.to_string(),
        })
    }

    /// Parse OpenWrt logread output
    fn parse_logread_line(&self, line: &str) -> MonitorResult<LogEntry> {
        // logread format: Mon Jan  1 12:00:00 2024 daemon.info process[pid]: message
        let line = line.trim();
        if line.is_empty() {
            return Err(MonitorError::ParseError("Empty line".to_string()));
        }

        let parts: Vec<&str> = line.splitn(6, ' ').collect();
        if parts.len() < 6 {
            return Err(MonitorError::ParseError("Invalid logread format".to_string()));
        }

        // Parse timestamp (approximate - would need proper date parsing)
        let timestamp = SystemTime::now(); // Simplified for now

        // Parse facility.level
        let facility_level = parts[4];
        let (facility, level) = if let Some(dot_pos) = facility_level.find('.') {
            let facility_str = &facility_level[..dot_pos];
            let level_str = &facility_level[dot_pos + 1..];

            let facility = self.parse_facility(facility_str);
            let level = self.parse_level(level_str);

            (facility, level)
        } else {
            (LogFacility::Daemon, LogLevel::Info)
        };

        // Parse process[pid]
        let process_info = parts[5];
        let (process, pid) = if let Some(bracket_pos) = process_info.find('[') {
            let process = &process_info[..bracket_pos];
            let pid_part = &process_info[bracket_pos + 1..];

            let pid = if let Some(end_bracket) = pid_part.find(']') {
                let pid_str = &pid_part[..end_bracket];
                pid_str.parse::<u32>().ok()
            } else {
                None
            };

            (process.to_string(), pid)
        } else {
            let process = if process_info.ends_with(':') {
                &process_info[..process_info.len() - 1]
            } else {
                process_info
            };
            (process.to_string(), None)
        };

        // Get message (everything after process info)
        let message_start = line.find(process_info)
            .map(|pos| pos + process_info.len())
            .unwrap_or(line.len());

        let message = if message_start < line.len() {
            line[message_start..].trim_start_matches(':').trim().to_string()
        } else {
            String::new()
        };

        Ok(LogEntry {
            timestamp,
            facility,
            level,
            hostname: "localhost".to_string(),
            process,
            pid,
            message,
        })
    }

    /// Parse standard syslog line
    fn parse_syslog_line(&self, line: &str) -> MonitorResult<LogEntry> {
        // Standard syslog format: Jan  1 12:00:00 hostname process[pid]: message
        let line = line.trim();
        if line.is_empty() {
            return Err(MonitorError::ParseError("Empty line".to_string()));
        }

        // Simple parsing - would need more sophisticated date parsing in production
        let parts: Vec<&str> = line.splitn(5, ' ').collect();
        if parts.len() < 5 {
            return Err(MonitorError::ParseError("Invalid syslog format".to_string()));
        }

        let timestamp = SystemTime::now(); // Simplified
        let hostname = parts[3].to_string();

        // Parse process[pid]: message
        let process_and_message = parts[4];
        let (process, pid, message) = if let Some(colon_pos) = process_and_message.find(':') {
            let process_part = &process_and_message[..colon_pos];
            let message = &process_and_message[colon_pos + 1..].trim();

            let (process, pid) = if let Some(bracket_pos) = process_part.find('[') {
                let process = &process_part[..bracket_pos];
                let pid_part = &process_part[bracket_pos + 1..];

                let pid = if let Some(end_bracket) = pid_part.find(']') {
                    let pid_str = &pid_part[..end_bracket];
                    pid_str.parse::<u32>().ok()
                } else {
                    None
                };

                (process.to_string(), pid)
            } else {
                (process_part.to_string(), None)
            };

            (process, pid, message.to_string())
        } else {
            ("unknown".to_string(), None, process_and_message.to_string())
        };

        Ok(LogEntry {
            timestamp,
            facility: LogFacility::Daemon,
            level: LogLevel::Info,
            hostname,
            process,
            pid,
            message,
        })
    }

    /// Parse application log line
    fn parse_application_log_line(&self, line: &str, process_name: &str) -> MonitorResult<LogEntry> {
        let line = line.trim();
        if line.is_empty() {
            return Err(MonitorError::ParseError("Empty line".to_string()));
        }

        // Simple application log parsing
        Ok(LogEntry {
            timestamp: SystemTime::now(),
            facility: LogFacility::Daemon,
            level: LogLevel::Info,
            hostname: "localhost".to_string(),
            process: process_name.to_string(),
            pid: None,
            message: line.to_string(),
        })
    }

    /// Parse facility string to LogFacility
    fn parse_facility(&self, facility_str: &str) -> LogFacility {
        match facility_str {
            "kern" => LogFacility::Kernel,
            "user" => LogFacility::User,
            "mail" => LogFacility::Mail,
            "daemon" => LogFacility::Daemon,
            "auth" => LogFacility::Auth,
            "syslog" => LogFacility::Syslog,
            "lpr" => LogFacility::Lpr,
            "news" => LogFacility::News,
            "uucp" => LogFacility::Uucp,
            "cron" => LogFacility::Cron,
            "authpriv" => LogFacility::Authpriv,
            "ftp" => LogFacility::Ftp,
            "local0" => LogFacility::Local0,
            "local1" => LogFacility::Local1,
            "local2" => LogFacility::Local2,
            "local3" => LogFacility::Local3,
            "local4" => LogFacility::Local4,
            "local5" => LogFacility::Local5,
            "local6" => LogFacility::Local6,
            "local7" => LogFacility::Local7,
            _ => LogFacility::Daemon,
        }
    }

    /// Parse level string to LogLevel
    fn parse_level(&self, level_str: &str) -> LogLevel {
        match level_str {
            "emerg" => LogLevel::Emergency,
            "alert" => LogLevel::Alert,
            "crit" => LogLevel::Critical,
            "err" => LogLevel::Error,
            "warning" => LogLevel::Warning,
            "notice" => LogLevel::Notice,
            "info" => LogLevel::Info,
            "debug" => LogLevel::Debug,
            _ => LogLevel::Info,
        }
    }

    /// Get all log entries
    pub fn get_logs(&mut self) -> MonitorResult<&Vec<LogEntry>> {
        self.load_logs()?;
        Ok(&self.log_entries)
    }

    /// Get filtered log entries
    pub fn get_filtered_logs(&mut self, filter: &LogFilter) -> MonitorResult<Vec<LogEntry>> {
        self.load_logs()?;
        
        let filtered: Vec<LogEntry> = self.log_entries.iter()
            .filter(|entry| self.matches_filter(entry, filter))
            .cloned()
            .collect();

        Ok(filtered)
    }

    /// Check if log entry matches filter
    fn matches_filter(&self, entry: &LogEntry, filter: &LogFilter) -> bool {
        if let Some(ref facility) = filter.facility {
            if entry.facility != *facility {
                return false;
            }
        }

        if let Some(ref level) = filter.level {
            if entry.level > *level {
                return false;
            }
        }

        if let Some(ref process) = filter.process {
            if !entry.process.contains(process) {
                return false;
            }
        }

        if let Some(ref message_contains) = filter.message_contains {
            if !entry.message.to_lowercase().contains(&message_contains.to_lowercase()) {
                return false;
            }
        }

        if let Some(since) = filter.since {
            if entry.timestamp < since {
                return false;
            }
        }

        if let Some(until) = filter.until {
            if entry.timestamp > until {
                return false;
            }
        }

        true
    }

    /// Get log statistics
    pub fn get_log_stats(&mut self) -> MonitorResult<LogStats> {
        self.load_logs()?;
        
        let mut stats = LogStats {
            total_entries: self.log_entries.len(),
            by_level: HashMap::new(),
            by_facility: HashMap::new(),
            by_process: HashMap::new(),
            error_count: 0,
            warning_count: 0,
        };

        for entry in &self.log_entries {
            // Count by level
            *stats.by_level.entry(entry.level.clone()).or_insert(0) += 1;
            
            // Count by facility
            *stats.by_facility.entry(entry.facility.clone()).or_insert(0) += 1;
            
            // Count by process
            *stats.by_process.entry(entry.process.clone()).or_insert(0) += 1;

            // Count errors and warnings
            match entry.level {
                LogLevel::Emergency | LogLevel::Alert | LogLevel::Critical | LogLevel::Error => {
                    stats.error_count += 1;
                }
                LogLevel::Warning => {
                    stats.warning_count += 1;
                }
                _ => {}
            }
        }

        Ok(stats)
    }

    /// Get recent errors
    pub fn get_recent_errors(&mut self, limit: usize) -> MonitorResult<Vec<LogEntry>> {
        self.load_logs()?;
        
        let mut errors: Vec<LogEntry> = self.log_entries.iter()
            .filter(|entry| matches!(entry.level, LogLevel::Emergency | LogLevel::Alert | LogLevel::Critical | LogLevel::Error))
            .cloned()
            .collect();

        errors.sort_by(|a, b| b.timestamp.cmp(&a.timestamp));
        errors.truncate(limit);
        
        Ok(errors)
    }

    /// Get recent warnings
    pub fn get_recent_warnings(&mut self, limit: usize) -> MonitorResult<Vec<LogEntry>> {
        self.load_logs()?;
        
        let mut warnings: Vec<LogEntry> = self.log_entries.iter()
            .filter(|entry| entry.level == LogLevel::Warning)
            .cloned()
            .collect();

        warnings.sort_by(|a, b| b.timestamp.cmp(&a.timestamp));
        warnings.truncate(limit);
        
        Ok(warnings)
    }



    /// Format log entry
    pub fn format_log_entry(entry: &LogEntry) -> String {
        let timestamp = entry.timestamp
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        
        let pid_str = entry.pid
            .map(|p| format!("[{}]", p))
            .unwrap_or_default();

        format!(
            "{} {} {}.{}: {}{}: {}",
            timestamp,
            entry.hostname,
            entry.facility,
            entry.level,
            entry.process,
            pid_str,
            entry.message
        )
    }

    /// Get log tail (most recent entries)
    pub fn get_log_tail(&mut self, lines: usize) -> MonitorResult<Vec<LogEntry>> {
        self.load_logs()?;
        
        let mut entries = self.log_entries.clone();
        entries.sort_by(|a, b| b.timestamp.cmp(&a.timestamp));
        entries.truncate(lines);
        
        Ok(entries)
    }

    /// Clear old log entries
    pub fn clear_old_logs(&mut self, keep_duration: std::time::Duration) -> MonitorResult<usize> {
        let cutoff_time = SystemTime::now() - keep_duration;
        let initial_count = self.log_entries.len();
        
        self.log_entries.retain(|entry| entry.timestamp >= cutoff_time);
        
        Ok(initial_count - self.log_entries.len())
    }

    /// Set maximum log entries to keep
    pub fn set_max_entries(&mut self, max_entries: usize) {
        self.max_entries = max_entries;
        if self.log_entries.len() > max_entries {
            let excess = self.log_entries.len() - max_entries;
            self.log_entries.drain(0..excess);
        }
    }

    /// Export logs to string
    pub fn export_logs(&mut self, filter: Option<&LogFilter>) -> MonitorResult<String> {
        let entries = if let Some(filter) = filter {
            self.get_filtered_logs(filter)?
        } else {
            self.get_logs()?.clone()
        };

        let mut output = String::new();
        for entry in entries {
            output.push_str(&Self::format_log_entry(&entry));
            output.push('\n');
        }

        Ok(output)
    }

    /// Start real-time log monitoring
    pub async fn start_real_time_monitoring(&mut self) -> MonitorResult<()> {
        // This would typically use inotify or similar to watch log files
        // For now, we'll implement a simple polling mechanism

        // Store initial log count
        self.load_logs()?;
        let initial_count = self.log_entries.len();

        // In a real implementation, this would:
        // 1. Use inotify to watch log files for changes
        // 2. Use tail -f equivalent functionality
        // 3. Parse new entries as they arrive
        // 4. Emit events for new log entries

        println!("Real-time log monitoring started with {} existing entries", initial_count);
        Ok(())
    }

    /// Stop real-time log monitoring
    pub fn stop_real_time_monitoring(&mut self) -> MonitorResult<()> {
        // Stop any background monitoring tasks
        println!("Real-time log monitoring stopped");
        Ok(())
    }

    /// Get new log entries since last check
    pub fn get_new_entries(&mut self) -> MonitorResult<Vec<LogEntry>> {
        let previous_count = self.log_entries.len();
        self.load_logs()?;

        if self.log_entries.len() > previous_count {
            // Return only new entries
            let new_entries = self.log_entries[previous_count..].to_vec();
            Ok(new_entries)
        } else {
            Ok(Vec::new())
        }
    }

    /// Clear old log entries to manage memory
    pub fn clear_old_entries(&mut self, keep_count: usize) -> MonitorResult<()> {
        if self.log_entries.len() > keep_count {
            // Keep only the most recent entries
            self.log_entries.sort_by(|a, b| b.timestamp.cmp(&a.timestamp));
            self.log_entries.truncate(keep_count);
        }
        Ok(())
    }

    /// Get log entries in a specific time range
    pub fn get_entries_in_range(&mut self, start: SystemTime, end: SystemTime) -> MonitorResult<Vec<LogEntry>> {
        self.load_logs()?;

        let filtered: Vec<LogEntry> = self.log_entries.iter()
            .filter(|entry| entry.timestamp >= start && entry.timestamp <= end)
            .cloned()
            .collect();

        Ok(filtered)
    }

    /// Search log entries by message content
    pub fn search_logs(&mut self, query: &str, case_sensitive: bool) -> MonitorResult<Vec<LogEntry>> {
        self.load_logs()?;

        let search_query = if case_sensitive {
            query.to_string()
        } else {
            query.to_lowercase()
        };

        let filtered: Vec<LogEntry> = self.log_entries.iter()
            .filter(|entry| {
                let message = if case_sensitive {
                    &entry.message
                } else {
                    &entry.message.to_lowercase()
                };
                message.contains(&search_query)
            })
            .cloned()
            .collect();

        Ok(filtered)
    }

    /// Get log entries by process name
    pub fn get_entries_by_process(&mut self, process_name: &str) -> MonitorResult<Vec<LogEntry>> {
        self.load_logs()?;

        let filtered: Vec<LogEntry> = self.log_entries.iter()
            .filter(|entry| entry.process == process_name)
            .cloned()
            .collect();

        Ok(filtered)
    }

    /// Get log entries by facility
    pub fn get_entries_by_facility(&mut self, facility: LogFacility) -> MonitorResult<Vec<LogEntry>> {
        self.load_logs()?;

        let filtered: Vec<LogEntry> = self.log_entries.iter()
            .filter(|entry| entry.facility == facility)
            .cloned()
            .collect();

        Ok(filtered)
    }

    /// Get log entries by minimum level
    pub fn get_entries_by_level(&mut self, min_level: LogLevel) -> MonitorResult<Vec<LogEntry>> {
        self.load_logs()?;

        let filtered: Vec<LogEntry> = self.log_entries.iter()
            .filter(|entry| entry.level <= min_level)
            .cloned()
            .collect();

        Ok(filtered)
    }
}

impl Default for LogMonitor {
    fn default() -> Self {
        Self::new().expect("Failed to create LogMonitor")
    }
}
