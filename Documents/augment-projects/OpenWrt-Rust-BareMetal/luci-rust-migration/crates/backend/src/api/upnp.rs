use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::<PERSON><PERSON>,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;

use crate::AppState;
use super::{ApiError, ApiResponse, PaginationQuery, PaginatedResponse};

/// UPnP device information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpnpDevice {
    pub id: String,
    pub name: String,
    pub device_type: String,
    pub manufacturer: String,
    pub model: String,
    pub serial_number: Option<String>,
    pub mac_address: String,
    pub ip_address: String,
    pub port: u16,
    pub location: String,
    pub services: Vec<UpnpService>,
    pub status: UpnpDeviceStatus,
    pub last_seen: String,
    pub uptime: Option<u64>,
}

/// UPnP service information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpnpService {
    pub service_type: String,
    pub service_id: String,
    pub control_url: String,
    pub event_sub_url: Option<String>,
    pub scpd_url: String,
    pub actions: Vec<String>,
}

/// UPnP device status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum UpnpDeviceStatus {
    Online,
    Offline,
    Unknown,
}

/// UPnP port mapping
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpnpPortMapping {
    pub id: String,
    pub device_id: String,
    pub description: String,
    pub protocol: UpnpProtocol,
    pub external_port: u16,
    pub internal_port: u16,
    pub internal_client: String,
    pub enabled: bool,
    pub lease_duration: Option<u32>,
    pub created_at: String,
    pub expires_at: Option<String>,
}

/// UPnP protocol types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "UPPERCASE")]
pub enum UpnpProtocol {
    TCP,
    UDP,
}

/// UPnP configuration settings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpnpConfig {
    pub enabled: bool,
    pub secure_mode: bool,
    pub allow_multicast: bool,
    pub presentation_url: Option<String>,
    pub download_dir: Option<String>,
    pub upload_dir: Option<String>,
    pub port_range_start: u16,
    pub port_range_end: u16,
    pub lease_duration: u32,
    pub clean_ruleset_threshold: u32,
    pub clean_ruleset_interval: u32,
}

/// UPnP statistics
#[derive(Debug, Clone, Serialize)]
pub struct UpnpStats {
    pub total_devices: usize,
    pub online_devices: usize,
    pub total_port_mappings: usize,
    pub active_port_mappings: usize,
    pub bytes_sent: u64,
    pub bytes_received: u64,
    pub packets_sent: u64,
    pub packets_received: u64,
}

/// Query parameters for UPnP devices
#[derive(Debug, Deserialize)]
pub struct UpnpDeviceQuery {
    pub page: Option<usize>,
    pub limit: Option<usize>,
    pub status: Option<UpnpDeviceStatus>,
    pub device_type: Option<String>,
    pub search: Option<String>,
}

/// Query parameters for port mappings
#[derive(Debug, Deserialize)]
pub struct PortMappingQuery {
    pub page: Option<usize>,
    pub limit: Option<usize>,
    pub device_id: Option<String>,
    pub protocol: Option<UpnpProtocol>,
    pub enabled: Option<bool>,
}

/// Request for creating port mapping
#[derive(Debug, Deserialize)]
pub struct CreatePortMappingRequest {
    pub device_id: String,
    pub description: String,
    pub protocol: UpnpProtocol,
    pub external_port: u16,
    pub internal_port: u16,
    pub internal_client: String,
    pub enabled: bool,
    pub lease_duration: Option<u32>,
}

/// Request for updating port mapping
#[derive(Debug, Deserialize)]
pub struct UpdatePortMappingRequest {
    pub description: Option<String>,
    pub enabled: Option<bool>,
    pub lease_duration: Option<u32>,
}

/// Request for updating UPnP configuration
#[derive(Debug, Deserialize)]
pub struct UpdateUpnpConfigRequest {
    pub enabled: Option<bool>,
    pub secure_mode: Option<bool>,
    pub allow_multicast: Option<bool>,
    pub presentation_url: Option<String>,
    pub download_dir: Option<String>,
    pub upload_dir: Option<String>,
    pub port_range_start: Option<u16>,
    pub port_range_end: Option<u16>,
    pub lease_duration: Option<u32>,
    pub clean_ruleset_threshold: Option<u32>,
    pub clean_ruleset_interval: Option<u32>,
}

/// Get all UPnP devices
pub async fn get_upnp_devices(
    Query(query): Query<UpnpDeviceQuery>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<PaginatedResponse<UpnpDevice>>>, ApiError> {
    let page = query.page.unwrap_or(1);
    let limit = query.limit.unwrap_or(10);
    
    // Mock data for development
    let devices = get_mock_upnp_devices();
    
    // Apply filters
    let filtered_devices: Vec<UpnpDevice> = devices
        .into_iter()
        .filter(|device| {
            if let Some(ref status) = query.status {
                if device.status != *status {
                    return false;
                }
            }
            if let Some(ref device_type) = query.device_type {
                if !device.device_type.contains(device_type) {
                    return false;
                }
            }
            if let Some(ref search) = query.search {
                let search_lower = search.to_lowercase();
                if !device.name.to_lowercase().contains(&search_lower)
                    && !device.manufacturer.to_lowercase().contains(&search_lower)
                    && !device.model.to_lowercase().contains(&search_lower) {
                    return false;
                }
            }
            true
        })
        .collect();
    
    let total = filtered_devices.len();
    let start = (page - 1) * limit;
    let end = std::cmp::min(start + limit, total);
    let items = filtered_devices[start..end].to_vec();
    
    let response = PaginatedResponse::new(
        items,
        total as u32,
        page as u32,
        limit as u32,
    );
    
    Ok(Json(ApiResponse::success(response)))
}

/// Get specific UPnP device
pub async fn get_upnp_device(
    Path(device_id): Path<String>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<UpnpDevice>>, ApiError> {
    let devices = get_mock_upnp_devices();
    
    let device = devices
        .into_iter()
        .find(|d| d.id == device_id)
        .ok_or_else(|| ApiError::NotFound("UPnP device not found".to_string()))?;
    
    Ok(Json(ApiResponse::success(device)))
}

/// Refresh UPnP device discovery
pub async fn refresh_upnp_discovery(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<String>>, ApiError> {
    // In a real implementation, this would trigger UPnP discovery
    // For now, return success message
    Ok(Json(ApiResponse::success("UPnP device discovery refreshed".to_string())))
}

/// Get all port mappings
pub async fn get_port_mappings(
    Query(query): Query<PortMappingQuery>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<PaginatedResponse<UpnpPortMapping>>>, ApiError> {
    let page = query.page.unwrap_or(1);
    let limit = query.limit.unwrap_or(10);
    
    // Mock data for development
    let mappings = get_mock_port_mappings();
    
    // Apply filters
    let filtered_mappings: Vec<UpnpPortMapping> = mappings
        .into_iter()
        .filter(|mapping| {
            if let Some(ref device_id) = query.device_id {
                if mapping.device_id != *device_id {
                    return false;
                }
            }
            if let Some(ref protocol) = query.protocol {
                if mapping.protocol != *protocol {
                    return false;
                }
            }
            if let Some(enabled) = query.enabled {
                if mapping.enabled != enabled {
                    return false;
                }
            }
            true
        })
        .collect();
    
    let total = filtered_mappings.len();
    let start = (page - 1) * limit;
    let end = std::cmp::min(start + limit, total);
    let items = filtered_mappings[start..end].to_vec();
    
    let response = PaginatedResponse::new(
        items,
        total as u32,
        page as u32,
        limit as u32,
    );
    
    Ok(Json(ApiResponse::success(response)))
}

/// Create new port mapping
pub async fn create_port_mapping(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreatePortMappingRequest>,
) -> Result<Json<ApiResponse<UpnpPortMapping>>, ApiError> {
    // Validate port range
    if request.external_port == 0 || request.internal_port == 0 {
        return Err(ApiError::BadRequest("Port numbers must be greater than 0".to_string()));
    }
    
    // Validate IP address format
    if request.internal_client.parse::<std::net::Ipv4Addr>().is_err() {
        return Err(ApiError::BadRequest("Invalid internal client IP address".to_string()));
    }
    
    let mapping = UpnpPortMapping {
        id: format!("mapping_{}", uuid::Uuid::new_v4().to_string()[..8].to_string()),
        device_id: request.device_id,
        description: request.description,
        protocol: request.protocol,
        external_port: request.external_port,
        internal_port: request.internal_port,
        internal_client: request.internal_client,
        enabled: request.enabled,
        lease_duration: request.lease_duration,
        created_at: chrono::Utc::now().to_rfc3339(),
        expires_at: request.lease_duration.map(|duration| {
            (chrono::Utc::now() + chrono::Duration::seconds(duration as i64)).to_rfc3339()
        }),
    };
    
    Ok(Json(ApiResponse::success(mapping)))
}

/// Update port mapping
pub async fn update_port_mapping(
    Path(mapping_id): Path<String>,
    State(state): State<Arc<AppState>>,
    Json(request): Json<UpdatePortMappingRequest>,
) -> Result<Json<ApiResponse<UpnpPortMapping>>, ApiError> {
    let mut mappings = get_mock_port_mappings();
    
    let mapping = mappings
        .iter_mut()
        .find(|m| m.id == mapping_id)
        .ok_or_else(|| ApiError::NotFound("Port mapping not found".to_string()))?;
    
    if let Some(description) = request.description {
        mapping.description = description;
    }
    if let Some(enabled) = request.enabled {
        mapping.enabled = enabled;
    }
    if let Some(lease_duration) = request.lease_duration {
        mapping.lease_duration = Some(lease_duration);
        mapping.expires_at = Some(
            (chrono::Utc::now() + chrono::Duration::seconds(lease_duration as i64)).to_rfc3339()
        );
    }
    
    Ok(Json(ApiResponse::success(mapping.clone())))
}

/// Delete port mapping
pub async fn delete_port_mapping(
    Path(mapping_id): Path<String>,
    State(state): State<Arc<AppState>>,
) -> Result<StatusCode, ApiError> {
    let mappings = get_mock_port_mappings();

    if !mappings.iter().any(|m| m.id == mapping_id) {
        return Err(ApiError::NotFound("Port mapping not found".to_string()));
    }

    // In a real implementation, this would delete the port mapping
    Ok(StatusCode::NO_CONTENT)
}

/// Get UPnP configuration
pub async fn get_upnp_config(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<UpnpConfig>>, ApiError> {
    let config = get_mock_upnp_config();
    Ok(Json(ApiResponse::success(config)))
}

/// Update UPnP configuration
pub async fn update_upnp_config(
    State(state): State<Arc<AppState>>,
    Json(request): Json<UpdateUpnpConfigRequest>,
) -> Result<Json<ApiResponse<UpnpConfig>>, ApiError> {
    let mut config = get_mock_upnp_config();

    if let Some(enabled) = request.enabled {
        config.enabled = enabled;
    }
    if let Some(secure_mode) = request.secure_mode {
        config.secure_mode = secure_mode;
    }
    if let Some(allow_multicast) = request.allow_multicast {
        config.allow_multicast = allow_multicast;
    }
    if let Some(presentation_url) = request.presentation_url {
        config.presentation_url = Some(presentation_url);
    }
    if let Some(download_dir) = request.download_dir {
        config.download_dir = Some(download_dir);
    }
    if let Some(upload_dir) = request.upload_dir {
        config.upload_dir = Some(upload_dir);
    }
    if let Some(port_range_start) = request.port_range_start {
        config.port_range_start = port_range_start;
    }
    if let Some(port_range_end) = request.port_range_end {
        config.port_range_end = port_range_end;
    }
    if let Some(lease_duration) = request.lease_duration {
        config.lease_duration = lease_duration;
    }
    if let Some(clean_ruleset_threshold) = request.clean_ruleset_threshold {
        config.clean_ruleset_threshold = clean_ruleset_threshold;
    }
    if let Some(clean_ruleset_interval) = request.clean_ruleset_interval {
        config.clean_ruleset_interval = clean_ruleset_interval;
    }

    Ok(Json(ApiResponse::success(config)))
}

/// Get UPnP statistics
pub async fn get_upnp_stats(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<UpnpStats>>, ApiError> {
    let devices = get_mock_upnp_devices();
    let mappings = get_mock_port_mappings();

    let stats = UpnpStats {
        total_devices: devices.len(),
        online_devices: devices.iter().filter(|d| d.status == UpnpDeviceStatus::Online).count(),
        total_port_mappings: mappings.len(),
        active_port_mappings: mappings.iter().filter(|m| m.enabled).count(),
        bytes_sent: 1024 * 1024 * 50, // Mock data: 50MB
        bytes_received: 1024 * 1024 * 75, // Mock data: 75MB
        packets_sent: 12500,
        packets_received: 18750,
    };

    Ok(Json(ApiResponse::success(stats)))
}

/// Enable/disable UPnP service
pub async fn toggle_upnp_service(
    State(state): State<Arc<AppState>>,
    Json(enabled): Json<bool>,
) -> Result<Json<ApiResponse<String>>, ApiError> {
    // In a real implementation, this would start/stop the UPnP service
    let message = if enabled {
        "UPnP service enabled"
    } else {
        "UPnP service disabled"
    };

    Ok(Json(ApiResponse::success(message.to_string())))
}

/// Mock data functions
fn get_mock_upnp_devices() -> Vec<UpnpDevice> {
    vec![
        UpnpDevice {
            id: "device_001".to_string(),
            name: "Smart TV Living Room".to_string(),
            device_type: "MediaRenderer".to_string(),
            manufacturer: "Samsung".to_string(),
            model: "UN55TU8000".to_string(),
            serial_number: Some("SN123456789".to_string()),
            mac_address: "00:1A:2B:3C:4D:5E".to_string(),
            ip_address: "***********00".to_string(),
            port: 1900,
            location: "http://***********00:1900/description.xml".to_string(),
            services: vec![
                UpnpService {
                    service_type: "urn:schemas-upnp-org:service:AVTransport:1".to_string(),
                    service_id: "urn:upnp-org:serviceId:AVTransport".to_string(),
                    control_url: "/AVTransport/control".to_string(),
                    event_sub_url: Some("/AVTransport/event".to_string()),
                    scpd_url: "/AVTransport/scpd.xml".to_string(),
                    actions: vec!["Play".to_string(), "Pause".to_string(), "Stop".to_string()],
                },
            ],
            status: UpnpDeviceStatus::Online,
            last_seen: chrono::Utc::now().to_rfc3339(),
            uptime: Some(86400), // 1 day in seconds
        },
        UpnpDevice {
            id: "device_002".to_string(),
            name: "Gaming Console".to_string(),
            device_type: "MediaServer".to_string(),
            manufacturer: "Sony".to_string(),
            model: "PlayStation 5".to_string(),
            serial_number: Some("PS5123456789".to_string()),
            mac_address: "00:2A:3B:4C:5D:6E".to_string(),
            ip_address: "***********01".to_string(),
            port: 1900,
            location: "http://***********01:1900/description.xml".to_string(),
            services: vec![
                UpnpService {
                    service_type: "urn:schemas-upnp-org:service:ContentDirectory:1".to_string(),
                    service_id: "urn:upnp-org:serviceId:ContentDirectory".to_string(),
                    control_url: "/ContentDirectory/control".to_string(),
                    event_sub_url: Some("/ContentDirectory/event".to_string()),
                    scpd_url: "/ContentDirectory/scpd.xml".to_string(),
                    actions: vec!["Browse".to_string(), "Search".to_string()],
                },
            ],
            status: UpnpDeviceStatus::Online,
            last_seen: chrono::Utc::now().to_rfc3339(),
            uptime: Some(43200), // 12 hours in seconds
        },
        UpnpDevice {
            id: "device_003".to_string(),
            name: "Network Printer".to_string(),
            device_type: "Printer".to_string(),
            manufacturer: "HP".to_string(),
            model: "LaserJet Pro M404n".to_string(),
            serial_number: Some("HP987654321".to_string()),
            mac_address: "00:3A:4B:5C:6D:7E".to_string(),
            ip_address: "*************".to_string(),
            port: 1900,
            location: "http://*************:1900/description.xml".to_string(),
            services: vec![
                UpnpService {
                    service_type: "urn:schemas-upnp-org:service:PrintBasic:1".to_string(),
                    service_id: "urn:upnp-org:serviceId:PrintBasic".to_string(),
                    control_url: "/PrintBasic/control".to_string(),
                    event_sub_url: None,
                    scpd_url: "/PrintBasic/scpd.xml".to_string(),
                    actions: vec!["PrintDocument".to_string(), "GetPrinterAttributes".to_string()],
                },
            ],
            status: UpnpDeviceStatus::Offline,
            last_seen: (chrono::Utc::now() - chrono::Duration::hours(2)).to_rfc3339(),
            uptime: None,
        },
    ]
}

fn get_mock_port_mappings() -> Vec<UpnpPortMapping> {
    vec![
        UpnpPortMapping {
            id: "mapping_001".to_string(),
            device_id: "device_001".to_string(),
            description: "HTTP Server".to_string(),
            protocol: UpnpProtocol::TCP,
            external_port: 8080,
            internal_port: 80,
            internal_client: "***********00".to_string(),
            enabled: true,
            lease_duration: Some(3600), // 1 hour
            created_at: chrono::Utc::now().to_rfc3339(),
            expires_at: Some((chrono::Utc::now() + chrono::Duration::hours(1)).to_rfc3339()),
        },
        UpnpPortMapping {
            id: "mapping_002".to_string(),
            device_id: "device_002".to_string(),
            description: "Gaming Port".to_string(),
            protocol: UpnpProtocol::UDP,
            external_port: 3074,
            internal_port: 3074,
            internal_client: "***********01".to_string(),
            enabled: true,
            lease_duration: None, // Permanent
            created_at: (chrono::Utc::now() - chrono::Duration::days(1)).to_rfc3339(),
            expires_at: None,
        },
        UpnpPortMapping {
            id: "mapping_003".to_string(),
            device_id: "device_001".to_string(),
            description: "HTTPS Server".to_string(),
            protocol: UpnpProtocol::TCP,
            external_port: 8443,
            internal_port: 443,
            internal_client: "***********00".to_string(),
            enabled: false,
            lease_duration: Some(7200), // 2 hours
            created_at: (chrono::Utc::now() - chrono::Duration::hours(3)).to_rfc3339(),
            expires_at: Some((chrono::Utc::now() + chrono::Duration::hours(2)).to_rfc3339()),
        },
        UpnpPortMapping {
            id: "mapping_004".to_string(),
            device_id: "device_003".to_string(),
            description: "Printer Service".to_string(),
            protocol: UpnpProtocol::TCP,
            external_port: 9100,
            internal_port: 9100,
            internal_client: "*************".to_string(),
            enabled: true,
            lease_duration: Some(86400), // 24 hours
            created_at: (chrono::Utc::now() - chrono::Duration::hours(12)).to_rfc3339(),
            expires_at: Some((chrono::Utc::now() + chrono::Duration::hours(12)).to_rfc3339()),
        },
    ]
}

fn get_mock_upnp_config() -> UpnpConfig {
    UpnpConfig {
        enabled: true,
        secure_mode: false,
        allow_multicast: true,
        presentation_url: Some("http://***********:8080".to_string()),
        download_dir: Some("/tmp/upnp/downloads".to_string()),
        upload_dir: Some("/tmp/upnp/uploads".to_string()),
        port_range_start: 1024,
        port_range_end: 65535,
        lease_duration: 3600, // 1 hour default
        clean_ruleset_threshold: 20,
        clean_ruleset_interval: 600, // 10 minutes
    }
}
