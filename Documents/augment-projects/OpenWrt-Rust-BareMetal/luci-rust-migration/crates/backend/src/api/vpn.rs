//! VPN Configuration API endpoints
//! 
//! This module provides REST API endpoints for managing VPN configurations,
//! including OpenVPN, WireGuard, and IPSec support with certificate management
//! and connection monitoring.

use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::<PERSON><PERSON>,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use chrono::{DateTime, Utc};

use crate::AppState;
use super::{ApiError, ApiResponse, PaginationQuery, PaginatedResponse};

/// VPN protocol types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
#[serde(rename_all = "lowercase")]
pub enum VpnProtocol {
    OpenVPN,
    WireGuard,
    IPSec,
}

/// VPN connection status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum VpnStatus {
    Connected,
    Disconnected,
    Connecting,
    Error,
    Unknown,
}

/// VPN authentication method
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "snake_case")]
pub enum VpnAuthMethod {
    Certificate,
    UsernamePassword,
    PreSharedKey,
    PublicKey,
}

/// VPN encryption algorithm
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "UPPERCASE")]
pub enum VpnEncryption {
    AES128,
    AES256,
    ChaCha20Poly1305,
    Blowfish,
}

/// VPN configuration information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VpnConfig {
    pub id: String,
    pub name: String,
    pub protocol: VpnProtocol,
    pub enabled: bool,
    pub server_address: String,
    pub server_port: u16,
    pub auth_method: VpnAuthMethod,
    pub encryption: VpnEncryption,
    pub local_ip: Option<String>,
    pub remote_ip: Option<String>,
    pub dns_servers: Vec<String>,
    pub routes: Vec<VpnRoute>,
    pub certificate_id: Option<String>,
    pub username: Option<String>,
    pub auto_connect: bool,
    pub keep_alive: bool,
    pub compression: bool,
    pub mtu: Option<u16>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// VPN route configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VpnRoute {
    pub network: String,
    pub netmask: String,
    pub gateway: Option<String>,
    pub metric: Option<u32>,
}

/// VPN connection information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VpnConnection {
    pub config_id: String,
    pub status: VpnStatus,
    pub connected_at: Option<DateTime<Utc>>,
    pub disconnected_at: Option<DateTime<Utc>>,
    pub local_ip: Option<String>,
    pub remote_ip: Option<String>,
    pub bytes_sent: u64,
    pub bytes_received: u64,
    pub duration: Option<u64>, // seconds
    pub last_error: Option<String>,
}

/// VPN certificate information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VpnCertificate {
    pub id: String,
    pub name: String,
    pub certificate_type: VpnCertificateType,
    pub subject: String,
    pub issuer: String,
    pub valid_from: DateTime<Utc>,
    pub valid_to: DateTime<Utc>,
    pub fingerprint: String,
    pub key_size: u32,
    pub is_ca: bool,
    pub created_at: DateTime<Utc>,
}

/// VPN certificate type
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum VpnCertificateType {
    Server,
    Client,
    CA,
}

/// VPN statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VpnStats {
    pub total_configs: u32,
    pub active_connections: u32,
    pub total_bytes_sent: u64,
    pub total_bytes_received: u64,
    pub uptime: u64, // seconds
    pub protocol_stats: std::collections::HashMap<VpnProtocol, VpnProtocolStats>,
}

/// VPN protocol-specific statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VpnProtocolStats {
    pub configs: u32,
    pub active_connections: u32,
    pub bytes_sent: u64,
    pub bytes_received: u64,
}

/// Query parameters for VPN configurations
#[derive(Debug, Deserialize)]
pub struct VpnConfigQuery {
    pub protocol: Option<VpnProtocol>,
    pub status: Option<VpnStatus>,
    pub enabled: Option<bool>,
    pub search: Option<String>,
    #[serde(flatten)]
    pub pagination: PaginationQuery,
}

/// Request to create VPN configuration
#[derive(Debug, Deserialize)]
pub struct CreateVpnConfigRequest {
    pub name: String,
    pub protocol: VpnProtocol,
    pub server_address: String,
    pub server_port: u16,
    pub auth_method: VpnAuthMethod,
    pub encryption: VpnEncryption,
    pub certificate_id: Option<String>,
    pub username: Option<String>,
    pub password: Option<String>,
    pub dns_servers: Option<Vec<String>>,
    pub routes: Option<Vec<VpnRoute>>,
    pub auto_connect: Option<bool>,
    pub keep_alive: Option<bool>,
    pub compression: Option<bool>,
    pub mtu: Option<u16>,
}

/// Request to update VPN configuration
#[derive(Debug, Deserialize)]
pub struct UpdateVpnConfigRequest {
    pub name: Option<String>,
    pub enabled: Option<bool>,
    pub server_address: Option<String>,
    pub server_port: Option<u16>,
    pub auth_method: Option<VpnAuthMethod>,
    pub encryption: Option<VpnEncryption>,
    pub certificate_id: Option<String>,
    pub username: Option<String>,
    pub password: Option<String>,
    pub dns_servers: Option<Vec<String>>,
    pub routes: Option<Vec<VpnRoute>>,
    pub auto_connect: Option<bool>,
    pub keep_alive: Option<bool>,
    pub compression: Option<bool>,
    pub mtu: Option<u16>,
}

/// Request to create VPN certificate
#[derive(Debug, Deserialize)]
pub struct CreateVpnCertificateRequest {
    pub name: String,
    pub certificate_type: VpnCertificateType,
    pub certificate_data: String, // PEM format
    pub private_key: Option<String>, // PEM format
    pub ca_certificate: Option<String>, // PEM format for client certs
}

/// VPN connection control request
#[derive(Debug, Deserialize)]
pub struct VpnConnectionControlRequest {
    pub action: VpnConnectionAction,
}

/// VPN connection actions
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum VpnConnectionAction {
    Connect,
    Disconnect,
    Restart,
}

/// Get all VPN configurations
pub async fn get_vpn_configs(
    Query(query): Query<VpnConfigQuery>,
    State(_state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<PaginatedResponse<VpnConfig>>>, ApiError> {
    // Mock implementation - replace with actual VPN configuration retrieval
    let mut configs = get_mock_vpn_configs();
    
    // Apply filters
    if let Some(protocol) = &query.protocol {
        configs.retain(|c| &c.protocol == protocol);
    }
    
    if let Some(enabled) = query.enabled {
        configs.retain(|c| c.enabled == enabled);
    }
    
    if let Some(search) = &query.search {
        let search_lower = search.to_lowercase();
        configs.retain(|c| {
            c.name.to_lowercase().contains(&search_lower) ||
            c.server_address.to_lowercase().contains(&search_lower)
        });
    }
    
    // Apply pagination
    let total = configs.len();
    let (page, limit) = super::utils::extract_pagination(&query.pagination);
    let start = ((page - 1) * limit) as usize;
    let end = std::cmp::min(start + limit as usize, total);
    let items = configs[start..end].to_vec();
    
    let response = PaginatedResponse::new(
        items,
        total as u32,
        page as u32,
        limit as u32,
    );
    
    Ok(Json(ApiResponse::success(response)))
}

/// Get specific VPN configuration
pub async fn get_vpn_config(
    Path(id): Path<String>,
    State(_state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<VpnConfig>>, ApiError> {
    // Mock implementation
    let configs = get_mock_vpn_configs();
    let config = configs.into_iter()
        .find(|c| c.id == id)
        .ok_or_else(|| ApiError::NotFound("VPN configuration not found".to_string()))?;
    
    Ok(Json(ApiResponse::success(config)))
}

/// Create new VPN configuration
pub async fn create_vpn_config(
    State(_state): State<Arc<AppState>>,
    Json(request): Json<CreateVpnConfigRequest>,
) -> Result<Json<ApiResponse<VpnConfig>>, ApiError> {
    // Mock implementation
    let config = VpnConfig {
        id: uuid::Uuid::new_v4().to_string(),
        name: request.name,
        protocol: request.protocol,
        enabled: false,
        server_address: request.server_address,
        server_port: request.server_port,
        auth_method: request.auth_method,
        encryption: request.encryption,
        local_ip: None,
        remote_ip: None,
        dns_servers: request.dns_servers.unwrap_or_default(),
        routes: request.routes.unwrap_or_default(),
        certificate_id: request.certificate_id,
        username: request.username,
        auto_connect: request.auto_connect.unwrap_or(false),
        keep_alive: request.keep_alive.unwrap_or(true),
        compression: request.compression.unwrap_or(false),
        mtu: request.mtu,
        created_at: Utc::now(),
        updated_at: Utc::now(),
    };
    
    Ok(Json(ApiResponse::success(config)))
}

/// Update VPN configuration
pub async fn update_vpn_config(
    Path(id): Path<String>,
    State(_state): State<Arc<AppState>>,
    Json(request): Json<UpdateVpnConfigRequest>,
) -> Result<Json<ApiResponse<VpnConfig>>, ApiError> {
    // Mock implementation
    let mut configs = get_mock_vpn_configs();
    let config = configs.iter_mut()
        .find(|c| c.id == id)
        .ok_or_else(|| ApiError::NotFound("VPN configuration not found".to_string()))?;

    // Update fields if provided
    if let Some(name) = request.name {
        config.name = name;
    }
    if let Some(enabled) = request.enabled {
        config.enabled = enabled;
    }
    if let Some(server_address) = request.server_address {
        config.server_address = server_address;
    }
    if let Some(server_port) = request.server_port {
        config.server_port = server_port;
    }
    if let Some(auth_method) = request.auth_method {
        config.auth_method = auth_method;
    }
    if let Some(encryption) = request.encryption {
        config.encryption = encryption;
    }
    if let Some(certificate_id) = request.certificate_id {
        config.certificate_id = Some(certificate_id);
    }
    if let Some(username) = request.username {
        config.username = Some(username);
    }
    if let Some(dns_servers) = request.dns_servers {
        config.dns_servers = dns_servers;
    }
    if let Some(routes) = request.routes {
        config.routes = routes;
    }
    if let Some(auto_connect) = request.auto_connect {
        config.auto_connect = auto_connect;
    }
    if let Some(keep_alive) = request.keep_alive {
        config.keep_alive = keep_alive;
    }
    if let Some(compression) = request.compression {
        config.compression = compression;
    }
    if let Some(mtu) = request.mtu {
        config.mtu = Some(mtu);
    }

    config.updated_at = Utc::now();

    Ok(Json(ApiResponse::success(config.clone())))
}

/// Delete VPN configuration
pub async fn delete_vpn_config(
    Path(id): Path<String>,
    State(_state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<()>>, ApiError> {
    // Mock implementation - in real implementation, would delete from storage
    let configs = get_mock_vpn_configs();
    let _config = configs.into_iter()
        .find(|c| c.id == id)
        .ok_or_else(|| ApiError::NotFound("VPN configuration not found".to_string()))?;

    Ok(Json(ApiResponse::success(())))
}

/// Get VPN connection status
pub async fn get_vpn_connections(
    Query(query): Query<PaginationQuery>,
    State(_state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<PaginatedResponse<VpnConnection>>>, ApiError> {
    // Mock implementation
    let connections = get_mock_vpn_connections();

    // Apply pagination
    let total = connections.len();
    let (page, limit) = super::utils::extract_pagination(&query);
    let start = ((page - 1) * limit) as usize;
    let end = std::cmp::min(start + limit as usize, total);
    let items = connections[start..end].to_vec();

    let response = PaginatedResponse::new(
        items,
        total as u32,
        page as u32,
        limit as u32,
    );

    Ok(Json(ApiResponse::success(response)))
}

/// Get specific VPN connection
pub async fn get_vpn_connection(
    Path(config_id): Path<String>,
    State(_state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<VpnConnection>>, ApiError> {
    // Mock implementation
    let connections = get_mock_vpn_connections();
    let connection = connections.into_iter()
        .find(|c| c.config_id == config_id)
        .ok_or_else(|| ApiError::NotFound("VPN connection not found".to_string()))?;

    Ok(Json(ApiResponse::success(connection)))
}

/// Control VPN connection (connect/disconnect/restart)
pub async fn control_vpn_connection(
    Path(config_id): Path<String>,
    State(_state): State<Arc<AppState>>,
    Json(request): Json<VpnConnectionControlRequest>,
) -> Result<Json<ApiResponse<VpnConnection>>, ApiError> {
    // Mock implementation
    let mut connections = get_mock_vpn_connections();
    let connection = connections.iter_mut()
        .find(|c| c.config_id == config_id)
        .ok_or_else(|| ApiError::NotFound("VPN connection not found".to_string()))?;

    match request.action {
        VpnConnectionAction::Connect => {
            connection.status = VpnStatus::Connected;
            connection.connected_at = Some(Utc::now());
            connection.disconnected_at = None;
        }
        VpnConnectionAction::Disconnect => {
            connection.status = VpnStatus::Disconnected;
            connection.disconnected_at = Some(Utc::now());
        }
        VpnConnectionAction::Restart => {
            connection.status = VpnStatus::Connecting;
            connection.connected_at = Some(Utc::now());
            connection.disconnected_at = None;
        }
    }

    Ok(Json(ApiResponse::success(connection.clone())))
}

/// Get VPN certificates
pub async fn get_vpn_certificates(
    Query(query): Query<PaginationQuery>,
    State(_state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<PaginatedResponse<VpnCertificate>>>, ApiError> {
    // Mock implementation
    let certificates = get_mock_vpn_certificates();

    // Apply pagination
    let total = certificates.len();
    let (page, limit) = super::utils::extract_pagination(&query);
    let start = ((page - 1) * limit) as usize;
    let end = std::cmp::min(start + limit as usize, total);
    let items = certificates[start..end].to_vec();

    let response = PaginatedResponse::new(
        items,
        total as u32,
        page as u32,
        limit as u32,
    );

    Ok(Json(ApiResponse::success(response)))
}

/// Get specific VPN certificate
pub async fn get_vpn_certificate(
    Path(id): Path<String>,
    State(_state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<VpnCertificate>>, ApiError> {
    // Mock implementation
    let certificates = get_mock_vpn_certificates();
    let certificate = certificates.into_iter()
        .find(|c| c.id == id)
        .ok_or_else(|| ApiError::NotFound("VPN certificate not found".to_string()))?;

    Ok(Json(ApiResponse::success(certificate)))
}

/// Create VPN certificate
pub async fn create_vpn_certificate(
    State(_state): State<Arc<AppState>>,
    Json(request): Json<CreateVpnCertificateRequest>,
) -> Result<Json<ApiResponse<VpnCertificate>>, ApiError> {
    // Mock implementation - in real implementation, would parse certificate data
    let is_ca = request.certificate_type == VpnCertificateType::CA;
    let certificate = VpnCertificate {
        id: uuid::Uuid::new_v4().to_string(),
        name: request.name,
        certificate_type: request.certificate_type,
        subject: "CN=Mock Certificate".to_string(),
        issuer: "CN=Mock CA".to_string(),
        valid_from: Utc::now(),
        valid_to: Utc::now() + chrono::Duration::days(365),
        fingerprint: "AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99:AA:BB:CC:DD".to_string(),
        key_size: 2048,
        is_ca,
        created_at: Utc::now(),
    };

    Ok(Json(ApiResponse::success(certificate)))
}

/// Delete VPN certificate
pub async fn delete_vpn_certificate(
    Path(id): Path<String>,
    State(_state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<()>>, ApiError> {
    // Mock implementation
    let certificates = get_mock_vpn_certificates();
    let _certificate = certificates.into_iter()
        .find(|c| c.id == id)
        .ok_or_else(|| ApiError::NotFound("VPN certificate not found".to_string()))?;

    Ok(Json(ApiResponse::success(())))
}

/// Get VPN statistics
pub async fn get_vpn_stats(
    State(_state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<VpnStats>>, ApiError> {
    // Mock implementation
    let mut protocol_stats = std::collections::HashMap::new();

    protocol_stats.insert(VpnProtocol::OpenVPN, VpnProtocolStats {
        configs: 2,
        active_connections: 1,
        bytes_sent: 1024000,
        bytes_received: 2048000,
    });

    protocol_stats.insert(VpnProtocol::WireGuard, VpnProtocolStats {
        configs: 1,
        active_connections: 1,
        bytes_sent: 512000,
        bytes_received: 1024000,
    });

    protocol_stats.insert(VpnProtocol::IPSec, VpnProtocolStats {
        configs: 1,
        active_connections: 0,
        bytes_sent: 0,
        bytes_received: 0,
    });

    let stats = VpnStats {
        total_configs: 4,
        active_connections: 2,
        total_bytes_sent: 1536000,
        total_bytes_received: 3072000,
        uptime: 86400, // 1 day
        protocol_stats,
    };

    Ok(Json(ApiResponse::success(stats)))
}

// Mock data functions for development and testing

/// Generate mock VPN configurations
fn get_mock_vpn_configs() -> Vec<VpnConfig> {
    vec![
        VpnConfig {
            id: "vpn-1".to_string(),
            name: "Office VPN".to_string(),
            protocol: VpnProtocol::OpenVPN,
            enabled: true,
            server_address: "vpn.company.com".to_string(),
            server_port: 1194,
            auth_method: VpnAuthMethod::Certificate,
            encryption: VpnEncryption::AES256,
            local_ip: Some("********".to_string()),
            remote_ip: Some("********".to_string()),
            dns_servers: vec!["*******".to_string(), "*******".to_string()],
            routes: vec![
                VpnRoute {
                    network: "***********".to_string(),
                    netmask: "*************".to_string(),
                    gateway: Some("********".to_string()),
                    metric: Some(100),
                }
            ],
            certificate_id: Some("cert-1".to_string()),
            username: None,
            auto_connect: true,
            keep_alive: true,
            compression: false,
            mtu: Some(1500),
            created_at: Utc::now() - chrono::Duration::days(30),
            updated_at: Utc::now() - chrono::Duration::days(1),
        },
        VpnConfig {
            id: "vpn-2".to_string(),
            name: "Home WireGuard".to_string(),
            protocol: VpnProtocol::WireGuard,
            enabled: true,
            server_address: "home.example.com".to_string(),
            server_port: 51820,
            auth_method: VpnAuthMethod::PublicKey,
            encryption: VpnEncryption::ChaCha20Poly1305,
            local_ip: Some("********".to_string()),
            remote_ip: Some("********".to_string()),
            dns_servers: vec!["*******".to_string(), "*******".to_string()],
            routes: vec![],
            certificate_id: None,
            username: None,
            auto_connect: false,
            keep_alive: true,
            compression: false,
            mtu: Some(1420),
            created_at: Utc::now() - chrono::Duration::days(15),
            updated_at: Utc::now() - chrono::Duration::hours(6),
        },
        VpnConfig {
            id: "vpn-3".to_string(),
            name: "IPSec Tunnel".to_string(),
            protocol: VpnProtocol::IPSec,
            enabled: false,
            server_address: "ipsec.example.com".to_string(),
            server_port: 500,
            auth_method: VpnAuthMethod::PreSharedKey,
            encryption: VpnEncryption::AES128,
            local_ip: None,
            remote_ip: None,
            dns_servers: vec![],
            routes: vec![],
            certificate_id: None,
            username: Some("user123".to_string()),
            auto_connect: false,
            keep_alive: false,
            compression: false,
            mtu: None,
            created_at: Utc::now() - chrono::Duration::days(7),
            updated_at: Utc::now() - chrono::Duration::days(7),
        },
        VpnConfig {
            id: "vpn-4".to_string(),
            name: "Remote Access".to_string(),
            protocol: VpnProtocol::OpenVPN,
            enabled: true,
            server_address: "remote.company.com".to_string(),
            server_port: 443,
            auth_method: VpnAuthMethod::UsernamePassword,
            encryption: VpnEncryption::AES256,
            local_ip: Some("********".to_string()),
            remote_ip: Some("********".to_string()),
            dns_servers: vec!["***********".to_string()],
            routes: vec![
                VpnRoute {
                    network: "************".to_string(),
                    netmask: "*************".to_string(),
                    gateway: Some("********".to_string()),
                    metric: Some(50),
                }
            ],
            certificate_id: None,
            username: Some("admin".to_string()),
            auto_connect: false,
            keep_alive: true,
            compression: true,
            mtu: Some(1200),
            created_at: Utc::now() - chrono::Duration::days(3),
            updated_at: Utc::now() - chrono::Duration::hours(2),
        },
    ]
}

/// Generate mock VPN connections
fn get_mock_vpn_connections() -> Vec<VpnConnection> {
    vec![
        VpnConnection {
            config_id: "vpn-1".to_string(),
            status: VpnStatus::Connected,
            connected_at: Some(Utc::now() - chrono::Duration::hours(2)),
            disconnected_at: None,
            local_ip: Some("********".to_string()),
            remote_ip: Some("********".to_string()),
            bytes_sent: 1024000,
            bytes_received: 2048000,
            duration: Some(7200), // 2 hours
            last_error: None,
        },
        VpnConnection {
            config_id: "vpn-2".to_string(),
            status: VpnStatus::Connected,
            connected_at: Some(Utc::now() - chrono::Duration::minutes(30)),
            disconnected_at: None,
            local_ip: Some("********".to_string()),
            remote_ip: Some("********".to_string()),
            bytes_sent: 512000,
            bytes_received: 1024000,
            duration: Some(1800), // 30 minutes
            last_error: None,
        },
        VpnConnection {
            config_id: "vpn-3".to_string(),
            status: VpnStatus::Disconnected,
            connected_at: None,
            disconnected_at: Some(Utc::now() - chrono::Duration::days(1)),
            local_ip: None,
            remote_ip: None,
            bytes_sent: 0,
            bytes_received: 0,
            duration: None,
            last_error: Some("Authentication failed".to_string()),
        },
        VpnConnection {
            config_id: "vpn-4".to_string(),
            status: VpnStatus::Disconnected,
            connected_at: None,
            disconnected_at: Some(Utc::now() - chrono::Duration::hours(1)),
            local_ip: None,
            remote_ip: None,
            bytes_sent: 256000,
            bytes_received: 512000,
            duration: None,
            last_error: None,
        },
    ]
}

/// Generate mock VPN certificates
fn get_mock_vpn_certificates() -> Vec<VpnCertificate> {
    vec![
        VpnCertificate {
            id: "cert-1".to_string(),
            name: "Office VPN Client Certificate".to_string(),
            certificate_type: VpnCertificateType::Client,
            subject: "CN=office-client,O=Company,C=US".to_string(),
            issuer: "CN=Company CA,O=Company,C=US".to_string(),
            valid_from: Utc::now() - chrono::Duration::days(30),
            valid_to: Utc::now() + chrono::Duration::days(335),
            fingerprint: "AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99:AA:BB:CC:DD".to_string(),
            key_size: 2048,
            is_ca: false,
            created_at: Utc::now() - chrono::Duration::days(30),
        },
        VpnCertificate {
            id: "cert-2".to_string(),
            name: "Company Root CA".to_string(),
            certificate_type: VpnCertificateType::CA,
            subject: "CN=Company CA,O=Company,C=US".to_string(),
            issuer: "CN=Company CA,O=Company,C=US".to_string(),
            valid_from: Utc::now() - chrono::Duration::days(365),
            valid_to: Utc::now() + chrono::Duration::days(3650),
            fingerprint: "11:22:33:44:55:66:77:88:99:AA:BB:CC:DD:EE:FF:00:11:22:33:44".to_string(),
            key_size: 4096,
            is_ca: true,
            created_at: Utc::now() - chrono::Duration::days(365),
        },
        VpnCertificate {
            id: "cert-3".to_string(),
            name: "VPN Server Certificate".to_string(),
            certificate_type: VpnCertificateType::Server,
            subject: "CN=vpn.company.com,O=Company,C=US".to_string(),
            issuer: "CN=Company CA,O=Company,C=US".to_string(),
            valid_from: Utc::now() - chrono::Duration::days(60),
            valid_to: Utc::now() + chrono::Duration::days(305),
            fingerprint: "FF:EE:DD:CC:BB:AA:99:88:77:66:55:44:33:22:11:00:FF:EE:DD:CC".to_string(),
            key_size: 2048,
            is_ca: false,
            created_at: Utc::now() - chrono::Duration::days(60),
        },
    ]
}
