//! Enhanced Package management API endpoints with dependency resolution,
//! repository management, update scheduling, and package validation

use axum::{
    extract::{Path, Query, State},
    response::J<PERSON>,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::collections::HashMap;
use chrono::{DateTime, Utc};
use crate::{AppState, api::{ApiResponse, ApiError, SearchQuery, PaginatedResponse}};

/// Package information
#[derive(Debug, Serialize, Deserialize)]
pub struct Package {
    pub name: String,
    pub version: String,
    pub description: String,
    pub size: u64,
    pub installed: bool,
    pub category: String,
    pub dependencies: Vec<String>,
    pub conflicts: Vec<String>,
}

/// Package operation request
#[derive(Debug, Deserialize)]
pub struct PackageOperationRequest {
    pub packages: Vec<String>,
    pub force: Option<bool>,
    pub no_deps: Option<bool>,
}

/// Package operation response
#[derive(Debug, Serialize)]
pub struct PackageOperationResponse {
    pub success: bool,
    pub message: String,
    pub affected_packages: Vec<String>,
    pub errors: Vec<String>,
}

/// Repository information
#[derive(Debug, Serialize, Deserialize)]
pub struct Repository {
    pub name: String,
    pub url: String,
    pub enabled: bool,
    pub priority: u32,
    pub description: Option<String>,
}

/// Removal safety query parameters
#[derive(Debug, Deserialize)]
pub struct RemovalSafetyQuery {
    pub packages: String, // Comma-separated list of package names
}

/// Removal safety information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RemovalSafetyInfo {
    pub packages_to_remove: Vec<String>,
    pub affected_packages: Vec<String>,
    pub broken_dependencies: Vec<String>,
    pub safe_to_remove: bool,
}

/// Package update information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PackageUpdate {
    pub name: String,
    pub current_version: String,
    pub available_version: String,
    pub description: String,
    pub size: u64,
    pub category: String,
    pub dependencies: Vec<String>,
    pub changelog: Option<String>,
    pub security_update: bool,
}

/// Update operation request
#[derive(Debug, Deserialize)]
pub struct UpdateOperationRequest {
    pub packages: Vec<String>,
    pub force: Option<bool>,
    pub no_deps: Option<bool>,
}

/// Enhanced repository information with health monitoring
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RepositoryInfo {
    pub name: String,
    pub repo_type: String,
    pub url: String,
    pub enabled: bool,
    pub priority: u32,
    pub description: Option<String>,
    pub architecture: Option<String>,
    pub gpg_key: Option<String>,
    pub last_update: Option<DateTime<Utc>>,
    pub health_status: RepositoryHealthStatus,
    pub package_count: u32,
    pub total_size: u64,
}

/// Repository health status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RepositoryHealthStatus {
    Healthy,
    Warning { message: String },
    Error { message: String },
    Unknown,
}

/// Repository operation request
#[derive(Debug, Deserialize)]
pub struct RepositoryOperationRequest {
    pub name: String,
    pub repo_type: String,
    pub url: String,
    pub enabled: Option<bool>,
    pub priority: Option<u32>,
    pub description: Option<String>,
    pub architecture: Option<String>,
    pub gpg_key: Option<String>,
}

/// Update schedule configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateSchedule {
    pub id: String,
    pub name: String,
    pub enabled: bool,
    pub cron_expression: String,
    pub packages: Vec<String>, // Empty means all packages
    pub auto_install: bool,
    pub notify_only: bool,
    pub last_run: Option<DateTime<Utc>>,
    pub next_run: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// Request structure for creating/updating update schedules
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateScheduleRequest {
    pub name: String,
    pub enabled: bool,
    pub cron_expression: String,
    pub packages: Vec<String>,
    pub auto_install: bool,
    pub notify_only: bool,
}



/// Package validation result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PackageValidationResult {
    pub package_name: String,
    pub valid: bool,
    pub signature_valid: bool,
    pub checksum_valid: bool,
    pub dependency_issues: Vec<String>,
    pub security_issues: Vec<String>,
    pub warnings: Vec<String>,
    pub validation_time: DateTime<Utc>,
}

/// Dependency resolution result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DependencyResolutionResult {
    pub package: String,
    pub dependencies: Vec<DependencyInfo>,
    pub conflicts: Vec<ConflictInfo>,
    pub missing_dependencies: Vec<String>,
    pub install_order: Vec<String>,
    pub total_download_size: u64,
    pub total_installed_size: u64,
    pub resolution_time: DateTime<Utc>,
}

/// Dependency information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DependencyInfo {
    pub name: String,
    pub version: String,
    pub required_by: Vec<String>,
    pub optional: bool,
    pub satisfied: bool,
}

/// Conflict information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConflictInfo {
    pub package: String,
    pub conflicting_package: String,
    pub reason: String,
}

/// Package statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PackageStatistics {
    pub total_packages: u32,
    pub installed_packages: u32,
    pub available_updates: u32,
    pub broken_packages: u32,
    pub total_download_size: u64,
    pub total_installed_size: u64,
    pub repositories_count: u32,
    pub last_update_check: Option<DateTime<Utc>>,
}

/// List all packages
pub async fn list_packages(
    Query(query): Query<SearchQuery>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<PaginatedResponse<Package>>>, ApiError> {
    // Mock implementation with sample packages
    let mut packages = vec![
        Package {
            name: "wget".to_string(),
            version: "1.21.3-1".to_string(),
            description: "Non-interactive network downloader".to_string(),
            size: 1024 * 300,
            installed: false,
            category: "Network".to_string(),
            dependencies: vec!["libc".to_string(), "libpcre".to_string()],
            conflicts: vec![],
        },
        Package {
            name: "curl".to_string(),
            version: "7.85.0-1".to_string(),
            description: "Command line tool for transferring data with URL syntax".to_string(),
            size: 1024 * 500,
            installed: true,
            category: "Network".to_string(),
            dependencies: vec!["libc".to_string(), "libcurl".to_string()],
            conflicts: vec![],
        },
        Package {
            name: "nano".to_string(),
            version: "6.4-1".to_string(),
            description: "Small and friendly text editor".to_string(),
            size: 1024 * 200,
            installed: false,
            category: "Utilities".to_string(),
            dependencies: vec!["libc".to_string()],
            conflicts: vec![],
        },
    ];

    // Apply search filter if provided
    if let Some(search_query) = &query.q {
        packages.retain(|p| {
            p.name.contains(search_query) || p.description.contains(search_query)
        });
    }

    // Apply category filter if provided
    if let Some(category) = &query.category {
        packages.retain(|p| p.category == *category);
    }

    let total = packages.len() as u32;
    let page = query.pagination.page.unwrap_or(1);
    let limit = query.pagination.limit.unwrap_or(20);

    let response = PaginatedResponse::new(packages, total, page, limit);
    Ok(Json(ApiResponse::success(response)))
}

/// Search packages
pub async fn search_packages(
    Query(query): Query<SearchQuery>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<PaginatedResponse<Package>>>, ApiError> {
    // Delegate to list_packages with search functionality
    list_packages(Query(query), State(state)).await
}

/// Get specific package
pub async fn get_package(
    Path(name): Path<String>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<Package>>, ApiError> {
    // Mock implementation with sample packages
    match name.as_str() {
        "wget" => Ok(Json(ApiResponse::success(Package {
            name: "wget".to_string(),
            version: "1.21.3-1".to_string(),
            description: "Non-interactive network downloader".to_string(),
            size: 1024 * 300,
            installed: false,
            category: "Network".to_string(),
            dependencies: vec!["libc".to_string(), "libpcre".to_string()],
            conflicts: vec![],
        }))),
        "curl" => Ok(Json(ApiResponse::success(Package {
            name: "curl".to_string(),
            version: "7.85.0-1".to_string(),
            description: "Command line tool for transferring data with URL syntax".to_string(),
            size: 1024 * 500,
            installed: true,
            category: "Network".to_string(),
            dependencies: vec!["libc".to_string(), "libcurl".to_string()],
            conflicts: vec![],
        }))),
        "nano" => Ok(Json(ApiResponse::success(Package {
            name: "nano".to_string(),
            version: "6.4-1".to_string(),
            description: "Small and friendly text editor".to_string(),
            size: 1024 * 200,
            installed: false,
            category: "Utilities".to_string(),
            dependencies: vec!["libc".to_string()],
            conflicts: vec![],
        }))),
        _ => Err(ApiError::NotFound(format!("Package {} not found", name))),
    }
}

/// Install package
pub async fn install_package(
    Path(name): Path<String>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<PackageOperationResponse>>, ApiError> {
    // Mock implementation
    let response = PackageOperationResponse {
        success: true,
        message: format!("Successfully installed package: {}", name),
        affected_packages: vec![name],
        errors: vec![],
    };
    Ok(Json(ApiResponse::success(response)))
}

/// Remove package
pub async fn remove_package(
    Path(name): Path<String>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<PackageOperationResponse>>, ApiError> {
    // Mock implementation
    let response = PackageOperationResponse {
        success: true,
        message: format!("Successfully removed package: {}", name),
        affected_packages: vec![name],
        errors: vec![],
    };
    Ok(Json(ApiResponse::success(response)))
}

/// Install multiple packages
pub async fn install_packages(
    State(state): State<Arc<AppState>>,
    Json(request): Json<PackageOperationRequest>,
) -> Result<Json<ApiResponse<PackageOperationResponse>>, ApiError> {
    // Mock implementation
    let response = PackageOperationResponse {
        success: true,
        message: format!("Successfully installed {} packages", request.packages.len()),
        affected_packages: request.packages,
        errors: vec![],
    };
    Ok(Json(ApiResponse::success(response)))
}

/// Remove multiple packages
pub async fn remove_packages(
    State(state): State<Arc<AppState>>,
    Json(request): Json<PackageOperationRequest>,
) -> Result<Json<ApiResponse<PackageOperationResponse>>, ApiError> {
    // Mock implementation
    let response = PackageOperationResponse {
        success: true,
        message: format!("Successfully removed {} packages", request.packages.len()),
        affected_packages: request.packages,
        errors: vec![],
    };
    Ok(Json(ApiResponse::success(response)))
}

/// Check removal safety for packages
pub async fn check_removal_safety(
    Query(query): Query<RemovalSafetyQuery>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<RemovalSafetyInfo>>, ApiError> {
    // Mock implementation - in real implementation, this would use the dependency resolver
    let packages_to_remove: Vec<String> = query.packages.split(',').map(|s| s.trim().to_string()).collect();

    // Mock data for demonstration
    let mut affected_packages = Vec::new();
    let mut broken_dependencies = Vec::new();
    let mut safe_to_remove = true;

    // Simulate dependency checking
    for package in &packages_to_remove {
        match package.as_str() {
            "curl" => {
                // Simulate that some packages depend on curl
                affected_packages.push("wget".to_string());
                broken_dependencies.push("wget depends on curl".to_string());
                safe_to_remove = false;
            },
            "libc" => {
                // Simulate that many packages depend on libc
                affected_packages.extend(vec!["curl".to_string(), "nano".to_string(), "wget".to_string()]);
                broken_dependencies.extend(vec![
                    "curl depends on libc".to_string(),
                    "nano depends on libc".to_string(),
                    "wget depends on libc".to_string(),
                ]);
                safe_to_remove = false;
            },
            _ => {
                // Most packages are safe to remove
            }
        }
    }

    let response = RemovalSafetyInfo {
        packages_to_remove,
        affected_packages,
        broken_dependencies,
        safe_to_remove,
    };

    Ok(Json(ApiResponse::success(response)))
}

/// Get installed packages
pub async fn get_installed_packages(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<Vec<Package>>>, ApiError> {
    // Mock implementation - return only installed packages
    let installed_packages = vec![
        Package {
            name: "curl".to_string(),
            version: "7.85.0-1".to_string(),
            description: "Command line tool for transferring data with URL syntax".to_string(),
            size: 1024 * 500,
            installed: true,
            category: "Network".to_string(),
            dependencies: vec!["libc".to_string(), "libcurl".to_string()],
            conflicts: vec![],
        },
    ];
    Ok(Json(ApiResponse::success(installed_packages)))
}

/// Get repositories
pub async fn get_repositories(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<Vec<Repository>>>, ApiError> {
    // Mock implementation
    let repositories = vec![
        Repository {
            name: "openwrt_core".to_string(),
            url: "https://downloads.openwrt.org/releases/23.05.0/targets/ath79/generic/packages".to_string(),
            enabled: true,
            priority: 1,
            description: Some("OpenWrt core packages".to_string()),
        },
        Repository {
            name: "openwrt_packages".to_string(),
            url: "https://downloads.openwrt.org/releases/23.05.0/packages/mips_24kc/packages".to_string(),
            enabled: true,
            priority: 2,
            description: Some("OpenWrt additional packages".to_string()),
        },
    ];
    Ok(Json(ApiResponse::success(repositories)))
}

/// Update package lists
pub async fn update_package_lists(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<PackageOperationResponse>>, ApiError> {
    // Mock implementation
    let response = PackageOperationResponse {
        success: true,
        message: "Package lists updated successfully".to_string(),
        affected_packages: vec![],
        errors: vec![],
    };
    Ok(Json(ApiResponse::success(response)))
}

/// Get available package updates
pub async fn get_available_updates(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<Vec<PackageUpdate>>>, ApiError> {
    // Mock implementation with sample updates
    let updates = vec![
        PackageUpdate {
            name: "curl".to_string(),
            current_version: "7.85.0-1".to_string(),
            available_version: "7.88.1-1".to_string(),
            description: "Command line tool for transferring data with URL syntax".to_string(),
            size: 1024 * 520,
            category: "Network".to_string(),
            dependencies: vec!["libc".to_string(), "libcurl".to_string()],
            changelog: Some("Security fixes and performance improvements".to_string()),
            security_update: true,
        },
        PackageUpdate {
            name: "nano".to_string(),
            current_version: "6.4-1".to_string(),
            available_version: "7.2-1".to_string(),
            description: "Small and friendly text editor".to_string(),
            size: 1024 * 220,
            category: "Utilities".to_string(),
            dependencies: vec!["libc".to_string()],
            changelog: Some("New features and bug fixes".to_string()),
            security_update: false,
        },
    ];
    Ok(Json(ApiResponse::success(updates)))
}

/// Upgrade a single package
pub async fn upgrade_package(
    Path(name): Path<String>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<PackageOperationResponse>>, ApiError> {
    // Mock implementation
    let response = PackageOperationResponse {
        success: true,
        message: format!("Successfully upgraded package: {}", name),
        affected_packages: vec![name],
        errors: vec![],
    };
    Ok(Json(ApiResponse::success(response)))
}

/// Upgrade multiple packages
pub async fn upgrade_packages(
    State(state): State<Arc<AppState>>,
    Json(request): Json<UpdateOperationRequest>,
) -> Result<Json<ApiResponse<PackageOperationResponse>>, ApiError> {
    // Mock implementation
    let response = PackageOperationResponse {
        success: true,
        message: format!("Successfully upgraded {} packages", request.packages.len()),
        affected_packages: request.packages,
        errors: vec![],
    };
    Ok(Json(ApiResponse::success(response)))
}

/// Upgrade all packages
pub async fn upgrade_all_packages(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<PackageOperationResponse>>, ApiError> {
    // Mock implementation
    let response = PackageOperationResponse {
        success: true,
        message: "Successfully upgraded all packages".to_string(),
        affected_packages: vec!["curl".to_string(), "nano".to_string()],
        errors: vec![],
    };
    Ok(Json(ApiResponse::success(response)))
}

// ============================================================================
// ENHANCED PACKAGE MANAGEMENT API ENDPOINTS
// ============================================================================

/// Get package statistics
pub async fn get_package_statistics(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<PackageStatistics>>, ApiError> {
    let stats = PackageStatistics {
        total_packages: 1250,
        installed_packages: 85,
        available_updates: 12,
        broken_packages: 2,
        total_download_size: 1024 * 1024 * 150, // 150MB
        total_installed_size: 1024 * 1024 * 500, // 500MB
        repositories_count: 4,
        last_update_check: Some(Utc::now()),
    };
    Ok(Json(ApiResponse::success(stats)))
}

/// Resolve package dependencies
pub async fn resolve_dependencies(
    Path(package_name): Path<String>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<DependencyResolutionResult>>, ApiError> {
    // Mock dependency resolution
    let dependencies = match package_name.as_str() {
        "nginx" => vec![
            DependencyInfo {
                name: "libc".to_string(),
                version: "1.2.3".to_string(),
                required_by: vec!["nginx".to_string()],
                optional: false,
                satisfied: true,
            },
            DependencyInfo {
                name: "openssl".to_string(),
                version: "1.1.1".to_string(),
                required_by: vec!["nginx".to_string()],
                optional: false,
                satisfied: true,
            },
            DependencyInfo {
                name: "pcre".to_string(),
                version: "8.45".to_string(),
                required_by: vec!["nginx".to_string()],
                optional: false,
                satisfied: false,
            },
        ],
        _ => vec![
            DependencyInfo {
                name: "libc".to_string(),
                version: "1.2.3".to_string(),
                required_by: vec![package_name.clone()],
                optional: false,
                satisfied: true,
            },
        ],
    };

    let conflicts = if package_name == "apache2" {
        vec![ConflictInfo {
            package: "apache2".to_string(),
            conflicting_package: "nginx".to_string(),
            reason: "Both packages provide web server functionality".to_string(),
        }]
    } else {
        vec![]
    };

    let missing_dependencies = dependencies
        .iter()
        .filter(|dep| !dep.satisfied)
        .map(|dep| dep.name.clone())
        .collect();

    let install_order = vec![
        "libc".to_string(),
        "openssl".to_string(),
        "pcre".to_string(),
        package_name.clone(),
    ];

    let result = DependencyResolutionResult {
        package: package_name,
        dependencies,
        conflicts,
        missing_dependencies,
        install_order,
        total_download_size: 1024 * 1024 * 25, // 25MB
        total_installed_size: 1024 * 1024 * 80, // 80MB
        resolution_time: Utc::now(),
    };

    Ok(Json(ApiResponse::success(result)))
}

/// Validate package
pub async fn validate_package(
    Path(package_name): Path<String>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<PackageValidationResult>>, ApiError> {
    // Mock package validation
    let validation = PackageValidationResult {
        package_name: package_name.clone(),
        valid: true,
        signature_valid: true,
        checksum_valid: true,
        dependency_issues: vec![],
        security_issues: if package_name == "old-package" {
            vec!["Package contains known security vulnerability CVE-2023-1234".to_string()]
        } else {
            vec![]
        },
        warnings: if package_name == "experimental-package" {
            vec!["Package is marked as experimental".to_string()]
        } else {
            vec![]
        },
        validation_time: Utc::now(),
    };

    Ok(Json(ApiResponse::success(validation)))
}

// ============================================================================
// REPOSITORY MANAGEMENT API ENDPOINTS
// ============================================================================

/// List all repositories
pub async fn list_repositories(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<Vec<RepositoryInfo>>>, ApiError> {
    let repositories = vec![
        RepositoryInfo {
            name: "openwrt_core".to_string(),
            repo_type: "src/gz".to_string(),
            url: "https://downloads.openwrt.org/releases/23.05.0/targets/ath79/generic/packages".to_string(),
            enabled: true,
            priority: 1,
            description: Some("OpenWrt core packages".to_string()),
            architecture: Some("mips_24kc".to_string()),
            gpg_key: None,
            last_update: Some(Utc::now()),
            health_status: RepositoryHealthStatus::Healthy,
            package_count: 850,
            total_size: 1024 * 1024 * 120, // 120MB
        },
        RepositoryInfo {
            name: "openwrt_base".to_string(),
            repo_type: "src/gz".to_string(),
            url: "https://downloads.openwrt.org/releases/23.05.0/packages/mips_24kc/base".to_string(),
            enabled: true,
            priority: 2,
            description: Some("OpenWrt base packages".to_string()),
            architecture: Some("mips_24kc".to_string()),
            gpg_key: None,
            last_update: Some(Utc::now()),
            health_status: RepositoryHealthStatus::Healthy,
            package_count: 400,
            total_size: 1024 * 1024 * 80, // 80MB
        },
        RepositoryInfo {
            name: "custom_repo".to_string(),
            repo_type: "src/gz".to_string(),
            url: "https://custom.example.com/packages".to_string(),
            enabled: false,
            priority: 10,
            description: Some("Custom package repository".to_string()),
            architecture: Some("mips_24kc".to_string()),
            gpg_key: Some("ABCD1234".to_string()),
            last_update: None,
            health_status: RepositoryHealthStatus::Warning {
                message: "Repository not accessible".to_string()
            },
            package_count: 0,
            total_size: 0,
        },
    ];

    Ok(Json(ApiResponse::success(repositories)))
}

/// Add repository
pub async fn add_repository(
    State(state): State<Arc<AppState>>,
    Json(request): Json<RepositoryOperationRequest>,
) -> Result<Json<ApiResponse<RepositoryInfo>>, ApiError> {
    // Mock repository addition
    let repository = RepositoryInfo {
        name: request.name,
        repo_type: request.repo_type,
        url: request.url,
        enabled: request.enabled.unwrap_or(true),
        priority: request.priority.unwrap_or(10),
        description: request.description,
        architecture: request.architecture,
        gpg_key: request.gpg_key,
        last_update: None,
        health_status: RepositoryHealthStatus::Unknown,
        package_count: 0,
        total_size: 0,
    };

    Ok(Json(ApiResponse::success(repository)))
}

/// Update repository
pub async fn update_repository(
    Path(repo_name): Path<String>,
    State(state): State<Arc<AppState>>,
    Json(request): Json<RepositoryOperationRequest>,
) -> Result<Json<ApiResponse<RepositoryInfo>>, ApiError> {
    // Mock repository update
    let repository = RepositoryInfo {
        name: repo_name,
        repo_type: request.repo_type,
        url: request.url,
        enabled: request.enabled.unwrap_or(true),
        priority: request.priority.unwrap_or(10),
        description: request.description,
        architecture: request.architecture,
        gpg_key: request.gpg_key,
        last_update: Some(Utc::now()),
        health_status: RepositoryHealthStatus::Healthy,
        package_count: 150,
        total_size: 1024 * 1024 * 30, // 30MB
    };

    Ok(Json(ApiResponse::success(repository)))
}

/// Remove repository
pub async fn remove_repository(
    Path(repo_name): Path<String>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<String>>, ApiError> {
    Ok(Json(ApiResponse::success(format!("Repository '{}' removed successfully", repo_name))))
}

/// Test repository connection
pub async fn test_repository(
    Path(repo_name): Path<String>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<RepositoryHealthStatus>>, ApiError> {
    // Mock repository health check
    let health_status = match repo_name.as_str() {
        "openwrt_core" | "openwrt_base" => RepositoryHealthStatus::Healthy,
        "slow_repo" => RepositoryHealthStatus::Warning {
            message: "Repository responding slowly".to_string()
        },
        "broken_repo" => RepositoryHealthStatus::Error {
            message: "Repository not accessible".to_string()
        },
        _ => RepositoryHealthStatus::Unknown,
    };

    Ok(Json(ApiResponse::success(health_status)))
}

// ============================================================================
// UPDATE SCHEDULING API ENDPOINTS
// ============================================================================

/// List update schedules
pub async fn list_update_schedules(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<Vec<UpdateSchedule>>>, ApiError> {
    let schedules = vec![
        UpdateSchedule {
            id: "daily-security".to_string(),
            name: "Daily Security Updates".to_string(),
            enabled: true,
            cron_expression: "0 2 * * *".to_string(), // Daily at 2 AM
            packages: vec![], // All packages
            auto_install: true,
            notify_only: false,
            last_run: Some(Utc::now() - chrono::Duration::days(1)),
            next_run: Some(Utc::now() + chrono::Duration::hours(6)),
            created_at: Utc::now() - chrono::Duration::days(30),
            updated_at: Utc::now() - chrono::Duration::days(1),
        },
        UpdateSchedule {
            id: "weekly-all".to_string(),
            name: "Weekly Full Update Check".to_string(),
            enabled: true,
            cron_expression: "0 3 * * 0".to_string(), // Weekly on Sunday at 3 AM
            packages: vec![],
            auto_install: false,
            notify_only: true,
            last_run: Some(Utc::now() - chrono::Duration::days(7)),
            next_run: Some(Utc::now() + chrono::Duration::days(7)),
            created_at: Utc::now() - chrono::Duration::days(60),
            updated_at: Utc::now() - chrono::Duration::days(7),
        },
    ];

    Ok(Json(ApiResponse::success(schedules)))
}

/// Create update schedule
pub async fn create_update_schedule(
    State(state): State<Arc<AppState>>,
    Json(request): Json<UpdateScheduleRequest>,
) -> Result<Json<ApiResponse<UpdateSchedule>>, ApiError> {
    let schedule = UpdateSchedule {
        id: format!("schedule-{}", Utc::now().timestamp()),
        name: request.name,
        enabled: request.enabled,
        cron_expression: request.cron_expression,
        packages: request.packages,
        auto_install: request.auto_install,
        notify_only: request.notify_only,
        last_run: None,
        next_run: Some(Utc::now() + chrono::Duration::hours(1)), // Mock next run
        created_at: Utc::now(),
        updated_at: Utc::now(),
    };

    Ok(Json(ApiResponse::success(schedule)))
}

/// Update schedule
pub async fn update_schedule(
    Path(schedule_id): Path<String>,
    State(state): State<Arc<AppState>>,
    Json(request): Json<UpdateScheduleRequest>,
) -> Result<Json<ApiResponse<UpdateSchedule>>, ApiError> {
    let schedule = UpdateSchedule {
        id: schedule_id,
        name: request.name,
        enabled: request.enabled,
        cron_expression: request.cron_expression,
        packages: request.packages,
        auto_install: request.auto_install,
        notify_only: request.notify_only,
        last_run: Some(Utc::now() - chrono::Duration::hours(2)),
        next_run: Some(Utc::now() + chrono::Duration::hours(22)),
        created_at: Utc::now() - chrono::Duration::days(10),
        updated_at: Utc::now(),
    };

    Ok(Json(ApiResponse::success(schedule)))
}

/// Delete update schedule
pub async fn delete_update_schedule(
    Path(schedule_id): Path<String>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<String>>, ApiError> {
    Ok(Json(ApiResponse::success(format!("Update schedule '{}' deleted successfully", schedule_id))))
}

/// Trigger update schedule manually
pub async fn trigger_update_schedule(
    Path(schedule_id): Path<String>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<String>>, ApiError> {
    Ok(Json(ApiResponse::success(format!("Update schedule '{}' triggered successfully", schedule_id))))
}

/// Create routes for enhanced package management
pub fn create_enhanced_routes() -> axum::Router<Arc<crate::AppState>> {
    use axum::routing::{get, post, put, delete};

    axum::Router::new()
        // Enhanced package management
        .route("/packages/statistics", get(get_package_statistics))
        .route("/packages/:name/dependencies", get(resolve_dependencies))
        .route("/packages/:name/validate", get(validate_package))

        // Repository management
        .route("/repositories", get(list_repositories))
        .route("/repositories", post(add_repository))
        .route("/repositories/:name", put(update_repository))
        .route("/repositories/:name", delete(remove_repository))
        .route("/repositories/:name/test", post(test_repository))

        // Update scheduling
        .route("/schedules", get(list_update_schedules))
        .route("/schedules", post(create_update_schedule))
        .route("/schedules/:id", put(update_schedule))
        .route("/schedules/:id", delete(delete_update_schedule))
        .route("/schedules/:id/trigger", post(trigger_update_schedule))
}
