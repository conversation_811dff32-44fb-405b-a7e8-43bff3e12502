//! DDNS (Dynamic DNS) API endpoints
//! 
//! This module provides REST API endpoints for managing Dynamic DNS configurations,
//! including provider support, update scheduling, and status monitoring.

use axum::{
    extract::{Path, Query, State},
    response::<PERSON><PERSON>,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use chrono::{DateTime, Utc};

use crate::AppState;
use super::{ApiError, ApiResponse, PaginationQuery, PaginatedResponse};

/// DDNS provider types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum DdnsProvider {
    DynDns,
    NoIp,
    Cloudflare,
    Namecheap,
    GoDaddy,
    Route53,
    Custom,
}

impl std::fmt::Display for DdnsProvider {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            DdnsProvider::DynDns => write!(f, "DynDNS"),
            DdnsProvider::NoIp => write!(f, "No-IP"),
            DdnsProvider::Cloudflare => write!(f, "Cloudflare"),
            DdnsProvider::Namecheap => write!(f, "Namecheap"),
            DdnsProvider::GoDaddy => write!(f, "GoDaddy"),
            DdnsProvider::Route53 => write!(f, "Amazon Route 53"),
            DdnsProvider::Custom => write!(f, "Custom Provider"),
        }
    }
}

/// DDNS configuration status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum DdnsStatus {
    Active,
    Inactive,
    Error,
    Updating,
    Unknown,
}

/// DDNS update method
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum UpdateMethod {
    Http,
    Https,
    Script,
}

/// DDNS configuration entry
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DdnsConfig {
    pub id: String,
    pub name: String,
    pub provider: DdnsProvider,
    pub hostname: String,
    pub domain: String,
    pub username: String,
    #[serde(skip_serializing)]
    pub password: String,
    pub update_url: Option<String>,
    pub update_method: UpdateMethod,
    pub update_interval: u32, // minutes
    pub enabled: bool,
    pub status: DdnsStatus,
    pub last_update: Option<DateTime<Utc>>,
    pub last_ip: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// DDNS configuration request for creation/update
#[derive(Debug, Deserialize)]
pub struct DdnsConfigRequest {
    pub name: String,
    pub provider: DdnsProvider,
    pub hostname: String,
    pub domain: String,
    pub username: String,
    pub password: String,
    pub update_url: Option<String>,
    pub update_method: UpdateMethod,
    pub update_interval: u32,
    pub enabled: bool,
}

/// DDNS status information
#[derive(Debug, Clone, Serialize)]
pub struct DdnsStatusInfo {
    pub id: String,
    pub name: String,
    pub status: DdnsStatus,
    pub last_update: Option<DateTime<Utc>>,
    pub last_ip: Option<String>,
    pub next_update: Option<DateTime<Utc>>,
    pub update_count: u32,
    pub error_count: u32,
    pub last_error: Option<String>,
}

/// DDNS update history entry
#[derive(Debug, Serialize)]
pub struct DdnsUpdateHistory {
    pub id: String,
    pub config_id: String,
    pub timestamp: DateTime<Utc>,
    pub old_ip: Option<String>,
    pub new_ip: String,
    pub success: bool,
    pub error_message: Option<String>,
    pub response_time_ms: u32,
}

/// DDNS provider information
#[derive(Debug, Serialize)]
pub struct DdnsProviderInfo {
    pub provider: DdnsProvider,
    pub name: String,
    pub description: String,
    pub default_update_url: Option<String>,
    pub supports_ipv6: bool,
    pub requires_api_key: bool,
    pub documentation_url: Option<String>,
}

/// Query parameters for DDNS configurations
#[derive(Debug, Deserialize)]
pub struct DdnsQuery {
    pub provider: Option<DdnsProvider>,
    pub status: Option<DdnsStatus>,
    pub enabled: Option<bool>,
    #[serde(flatten)]
    pub pagination: PaginationQuery,
}

/// Get all DDNS configurations
pub async fn get_ddns_configs(
    Query(query): Query<DdnsQuery>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<PaginatedResponse<DdnsConfig>>>, ApiError> {
    // Mock implementation - in real implementation, this would query the database
    let mut configs = get_mock_ddns_configs();
    
    // Apply filters
    if let Some(provider) = &query.provider {
        configs.retain(|c| &c.provider == provider);
    }
    
    if let Some(status) = &query.status {
        configs.retain(|c| &c.status == status);
    }
    
    if let Some(enabled) = query.enabled {
        configs.retain(|c| c.enabled == enabled);
    }
    
    let total = configs.len() as u32;
    let page = query.pagination.page.unwrap_or(1);
    let limit = query.pagination.limit.unwrap_or(20);
    
    // Apply pagination
    let start = ((page - 1) * limit) as usize;
    let end = (start + limit as usize).min(configs.len());
    let items = configs.into_iter().skip(start).take(end - start).collect();
    
    let response = PaginatedResponse::new(items, total, page, limit);
    Ok(Json(ApiResponse::success(response)))
}

/// Get DDNS configuration by ID
pub async fn get_ddns_config(
    Path(id): Path<String>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<DdnsConfig>>, ApiError> {
    let configs = get_mock_ddns_configs();
    
    match configs.into_iter().find(|c| c.id == id) {
        Some(config) => Ok(Json(ApiResponse::success(config))),
        None => Err(ApiError::NotFound(format!("DDNS configuration with ID '{}' not found", id))),
    }
}

/// Create new DDNS configuration
pub async fn create_ddns_config(
    State(state): State<Arc<AppState>>,
    Json(request): Json<DdnsConfigRequest>,
) -> Result<Json<ApiResponse<DdnsConfig>>, ApiError> {
    // Validate request
    if request.name.trim().is_empty() {
        return Err(ApiError::BadRequest("Name cannot be empty".to_string()));
    }
    
    if request.hostname.trim().is_empty() {
        return Err(ApiError::BadRequest("Hostname cannot be empty".to_string()));
    }
    
    if request.domain.trim().is_empty() {
        return Err(ApiError::BadRequest("Domain cannot be empty".to_string()));
    }
    
    if request.username.trim().is_empty() {
        return Err(ApiError::BadRequest("Username cannot be empty".to_string()));
    }
    
    if request.password.trim().is_empty() {
        return Err(ApiError::BadRequest("Password cannot be empty".to_string()));
    }
    
    if request.update_interval < 5 {
        return Err(ApiError::BadRequest("Update interval must be at least 5 minutes".to_string()));
    }
    
    // Create new configuration
    let now = Utc::now();
    let config = DdnsConfig {
        id: format!("ddns_{}", uuid::Uuid::new_v4().to_string().replace("-", "")),
        name: request.name,
        provider: request.provider,
        hostname: request.hostname,
        domain: request.domain,
        username: request.username,
        password: request.password,
        update_url: request.update_url,
        update_method: request.update_method,
        update_interval: request.update_interval,
        enabled: request.enabled,
        status: if request.enabled { DdnsStatus::Active } else { DdnsStatus::Inactive },
        last_update: None,
        last_ip: None,
        created_at: now,
        updated_at: now,
    };
    
    // In real implementation, save to database/UCI config
    
    Ok(Json(ApiResponse::success(config)))
}

/// Update DDNS configuration
pub async fn update_ddns_config(
    Path(id): Path<String>,
    State(state): State<Arc<AppState>>,
    Json(request): Json<DdnsConfigRequest>,
) -> Result<Json<ApiResponse<DdnsConfig>>, ApiError> {
    let mut configs = get_mock_ddns_configs();
    
    match configs.iter_mut().find(|c| c.id == id) {
        Some(config) => {
            // Update configuration
            config.name = request.name;
            config.provider = request.provider;
            config.hostname = request.hostname;
            config.domain = request.domain;
            config.username = request.username;
            config.password = request.password;
            config.update_url = request.update_url;
            config.update_method = request.update_method;
            config.update_interval = request.update_interval;
            config.enabled = request.enabled;
            config.status = if request.enabled { DdnsStatus::Active } else { DdnsStatus::Inactive };
            config.updated_at = Utc::now();
            
            Ok(Json(ApiResponse::success(config.clone())))
        }
        None => Err(ApiError::NotFound(format!("DDNS configuration with ID '{}' not found", id))),
    }
}

/// Delete DDNS configuration
pub async fn delete_ddns_config(
    Path(id): Path<String>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<()>>, ApiError> {
    // In real implementation, delete from database/UCI config
    Ok(Json(ApiResponse::success(())))
}

/// Get DDNS status for all configurations
pub async fn get_ddns_status(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<Vec<DdnsStatusInfo>>>, ApiError> {
    let status_info = get_mock_ddns_status();
    Ok(Json(ApiResponse::success(status_info)))
}

/// Get DDNS status for specific configuration
pub async fn get_ddns_config_status(
    Path(id): Path<String>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<DdnsStatusInfo>>, ApiError> {
    let status_info = get_mock_ddns_status();
    
    match status_info.into_iter().find(|s| s.id == id) {
        Some(status) => Ok(Json(ApiResponse::success(status))),
        None => Err(ApiError::NotFound(format!("DDNS configuration with ID '{}' not found", id))),
    }
}

/// Force update DDNS configuration
pub async fn force_update_ddns(
    Path(id): Path<String>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<DdnsStatusInfo>>, ApiError> {
    // In real implementation, trigger immediate DDNS update
    let mut status_info = get_mock_ddns_status();
    
    match status_info.iter_mut().find(|s| s.id == id) {
        Some(status) => {
            status.status = DdnsStatus::Updating;
            status.last_update = Some(Utc::now());
            Ok(Json(ApiResponse::success(status.clone())))
        }
        None => Err(ApiError::NotFound(format!("DDNS configuration with ID '{}' not found", id))),
    }
}

/// Get DDNS update history
pub async fn get_ddns_history(
    Path(id): Path<String>,
    Query(pagination): Query<PaginationQuery>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<PaginatedResponse<DdnsUpdateHistory>>>, ApiError> {
    let history = get_mock_ddns_history(&id);
    
    let total = history.len() as u32;
    let page = pagination.page.unwrap_or(1);
    let limit = pagination.limit.unwrap_or(20);
    
    let start = ((page - 1) * limit) as usize;
    let end = (start + limit as usize).min(history.len());
    let items = history.into_iter().skip(start).take(end - start).collect();
    
    let response = PaginatedResponse::new(items, total, page, limit);
    Ok(Json(ApiResponse::success(response)))
}

/// Get supported DDNS providers
pub async fn get_ddns_providers(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<Vec<DdnsProviderInfo>>>, ApiError> {
    let providers = get_supported_providers();
    Ok(Json(ApiResponse::success(providers)))
}

// Mock data functions (replace with real implementations)

fn get_mock_ddns_configs() -> Vec<DdnsConfig> {
    let now = Utc::now();
    vec![
        DdnsConfig {
            id: "ddns_001".to_string(),
            name: "Home Router".to_string(),
            provider: DdnsProvider::DynDns,
            hostname: "myhome".to_string(),
            domain: "dyndns.org".to_string(),
            username: "user1".to_string(),
            password: "password123".to_string(),
            update_url: None,
            update_method: UpdateMethod::Https,
            update_interval: 10,
            enabled: true,
            status: DdnsStatus::Active,
            last_update: Some(now - chrono::Duration::minutes(5)),
            last_ip: Some("*************".to_string()),
            created_at: now - chrono::Duration::days(30),
            updated_at: now - chrono::Duration::minutes(5),
        },
        DdnsConfig {
            id: "ddns_002".to_string(),
            name: "Office Network".to_string(),
            provider: DdnsProvider::NoIp,
            hostname: "office".to_string(),
            domain: "no-ip.com".to_string(),
            username: "office_user".to_string(),
            password: "office_pass".to_string(),
            update_url: None,
            update_method: UpdateMethod::Https,
            update_interval: 15,
            enabled: false,
            status: DdnsStatus::Inactive,
            last_update: Some(now - chrono::Duration::hours(2)),
            last_ip: Some("********".to_string()),
            created_at: now - chrono::Duration::days(15),
            updated_at: now - chrono::Duration::hours(2),
        },
    ]
}

fn get_mock_ddns_status() -> Vec<DdnsStatusInfo> {
    let now = Utc::now();
    vec![
        DdnsStatusInfo {
            id: "ddns_001".to_string(),
            name: "Home Router".to_string(),
            status: DdnsStatus::Active,
            last_update: Some(now - chrono::Duration::minutes(5)),
            last_ip: Some("*************".to_string()),
            next_update: Some(now + chrono::Duration::minutes(5)),
            update_count: 1440,
            error_count: 2,
            last_error: None,
        },
        DdnsStatusInfo {
            id: "ddns_002".to_string(),
            name: "Office Network".to_string(),
            status: DdnsStatus::Inactive,
            last_update: Some(now - chrono::Duration::hours(2)),
            last_ip: Some("********".to_string()),
            next_update: None,
            update_count: 720,
            error_count: 0,
            last_error: None,
        },
    ]
}

fn get_mock_ddns_history(config_id: &str) -> Vec<DdnsUpdateHistory> {
    let now = Utc::now();
    vec![
        DdnsUpdateHistory {
            id: "hist_001".to_string(),
            config_id: config_id.to_string(),
            timestamp: now - chrono::Duration::minutes(5),
            old_ip: Some("************".to_string()),
            new_ip: "*************".to_string(),
            success: true,
            error_message: None,
            response_time_ms: 250,
        },
        DdnsUpdateHistory {
            id: "hist_002".to_string(),
            config_id: config_id.to_string(),
            timestamp: now - chrono::Duration::minutes(15),
            old_ip: Some("************".to_string()),
            new_ip: "************".to_string(),
            success: true,
            error_message: None,
            response_time_ms: 180,
        },
    ]
}

fn get_supported_providers() -> Vec<DdnsProviderInfo> {
    vec![
        DdnsProviderInfo {
            provider: DdnsProvider::DynDns,
            name: "DynDNS".to_string(),
            description: "Dynamic DNS service by Oracle Dyn".to_string(),
            default_update_url: Some("https://members.dyndns.org/nic/update".to_string()),
            supports_ipv6: true,
            requires_api_key: false,
            documentation_url: Some("https://help.dyn.com/remote-access-api/".to_string()),
        },
        DdnsProviderInfo {
            provider: DdnsProvider::NoIp,
            name: "No-IP".to_string(),
            description: "Free and paid dynamic DNS service".to_string(),
            default_update_url: Some("https://dynupdate.no-ip.com/nic/update".to_string()),
            supports_ipv6: true,
            requires_api_key: false,
            documentation_url: Some("https://www.noip.com/integrate/request".to_string()),
        },
        DdnsProviderInfo {
            provider: DdnsProvider::Cloudflare,
            name: "Cloudflare".to_string(),
            description: "Cloudflare DNS with API support".to_string(),
            default_update_url: Some("https://api.cloudflare.com/client/v4/zones".to_string()),
            supports_ipv6: true,
            requires_api_key: true,
            documentation_url: Some("https://developers.cloudflare.com/api/".to_string()),
        },
    ]
}
