//! System information and status API endpoints

use axum::{
    extract::{State, Query},
    response::<PERSON><PERSON>,
};
use serde::{Deserialize, Serialize};
use std::{sync::Arc, collections::HashMap, time::{SystemTime, Duration}};
use crate::{AppState, api::{ApiResponse, ApiError}};
use luci_system_monitor::{SystemMonitor, MonitorResult, SystemMetrics};

/// System information response
#[derive(Debug, Serialize, Deserialize)]
pub struct SystemInfo {
    pub hostname: String,
    pub kernel: String,
    pub architecture: String,
    pub cpu_model: String,
    pub cpu_cores: u32,
    pub memory_total: u64,
    pub memory_available: u64,
    pub storage_total: u64,
    pub storage_available: u64,
    pub uptime: u64,
    pub load_average: [f32; 3],
    pub openwrt_version: Option<String>,
    pub board_name: Option<String>,
}

/// System status response
#[derive(Debug, Serialize, Deserialize)]
pub struct SystemStatus {
    pub cpu_usage: f32,
    pub memory_usage: f32,
    pub storage_usage: f32,
    pub network_interfaces: Vec<NetworkInterfaceStatus>,
    pub running_processes: u32,
    pub active_connections: u32,
    pub temperature: Option<f32>,
}

/// Network interface status
#[derive(Debug, Serialize, Deserialize)]
pub struct NetworkInterfaceStatus {
    pub name: String,
    pub status: String,
    pub ip_address: Option<String>,
    pub rx_bytes: u64,
    pub tx_bytes: u64,
    pub rx_packets: u64,
    pub tx_packets: u64,
}

/// Uptime information
#[derive(Debug, Serialize, Deserialize)]
pub struct UptimeInfo {
    pub uptime_seconds: u64,
    pub uptime_formatted: String,
    pub boot_time: chrono::DateTime<chrono::Utc>,
    pub load_average: [f32; 3],
}

/// Historical metrics request parameters
#[derive(Debug, Deserialize)]
pub struct HistoricalMetricsRequest {
    pub time_range: Option<String>, // "1h", "6h", "24h", "7d", "30d"
    pub interval: Option<String>,   // "1m", "5m", "15m", "1h"
    pub metrics: Option<String>,    // comma-separated: "cpu,memory,network,storage"
}

/// Historical metrics response
#[derive(Debug, Serialize, Deserialize)]
pub struct HistoricalMetricsResponse {
    pub time_range: String,
    pub interval: String,
    pub data_points: Vec<MetricsDataPoint>,
    pub summary: MetricsSummary,
}

/// Single metrics data point
#[derive(Debug, Serialize, Deserialize)]
pub struct MetricsDataPoint {
    pub timestamp: SystemTime,
    pub cpu_usage: f32,
    pub memory_usage: f32,
    pub storage_usage: f32,
    pub network_rx_rate: f64, // bytes per second
    pub network_tx_rate: f64, // bytes per second
    pub load_average: f32,
    pub process_count: u32,
    pub temperature: Option<f32>,
}

/// Metrics summary statistics
#[derive(Debug, Serialize, Deserialize)]
pub struct MetricsSummary {
    pub cpu_avg: f32,
    pub cpu_max: f32,
    pub cpu_min: f32,
    pub memory_avg: f32,
    pub memory_max: f32,
    pub memory_min: f32,
    pub network_peak_rx: f64,
    pub network_peak_tx: f64,
    pub uptime_percentage: f32,
}

/// Performance analysis response
#[derive(Debug, Serialize, Deserialize)]
pub struct PerformanceAnalysis {
    pub overall_score: u8, // 0-100
    pub cpu_analysis: CpuPerformanceAnalysis,
    pub memory_analysis: MemoryPerformanceAnalysis,
    pub network_analysis: NetworkPerformanceAnalysis,
    pub storage_analysis: StoragePerformanceAnalysis,
    pub recommendations: Vec<String>,
    pub bottlenecks: Vec<String>,
}

/// CPU performance analysis
#[derive(Debug, Serialize, Deserialize)]
pub struct CpuPerformanceAnalysis {
    pub average_usage: f32,
    pub peak_usage: f32,
    pub idle_time_percentage: f32,
    pub load_balance_score: u8, // 0-100
    pub thermal_status: String, // "Normal", "Warm", "Hot", "Critical"
    pub frequency_efficiency: f32,
}

/// Memory performance analysis
#[derive(Debug, Serialize, Deserialize)]
pub struct MemoryPerformanceAnalysis {
    pub average_usage: f32,
    pub peak_usage: f32,
    pub swap_usage: f32,
    pub cache_efficiency: f32,
    pub memory_pressure: f32,
    pub fragmentation_level: String, // "Low", "Medium", "High"
}

/// Network performance analysis
#[derive(Debug, Serialize, Deserialize)]
pub struct NetworkPerformanceAnalysis {
    pub total_throughput: f64,
    pub peak_rx_rate: f64,
    pub peak_tx_rate: f64,
    pub error_rate: f32,
    pub packet_loss: f32,
    pub latency_score: u8, // 0-100
}

/// Storage performance analysis
#[derive(Debug, Serialize, Deserialize)]
pub struct StoragePerformanceAnalysis {
    pub usage_percentage: f32,
    pub io_wait_time: f32,
    pub read_write_ratio: f32,
    pub fragmentation: f32,
    pub health_status: String, // "Excellent", "Good", "Fair", "Poor"
}

/// System alert
#[derive(Debug, Serialize, Deserialize)]
pub struct SystemAlert {
    pub id: String,
    pub severity: AlertSeverity,
    pub title: String,
    pub message: String,
    pub metric: String,
    pub current_value: f64,
    pub threshold: f64,
    pub timestamp: SystemTime,
    pub acknowledged: bool,
}

/// Alert severity levels
#[derive(Debug, Serialize, Deserialize)]
pub enum AlertSeverity {
    Critical,
    Warning,
    Info,
}

/// Metrics export request
#[derive(Debug, Deserialize)]
pub struct MetricsExportRequest {
    pub format: String, // "csv", "json", "xml"
    pub time_range: String,
    pub metrics: Vec<String>,
    pub include_summary: bool,
}

/// Get system information
pub async fn get_system_info(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<SystemInfo>>, ApiError> {
    let mut monitor = SystemMonitor::new()
        .map_err(|e| ApiError::Internal(format!("Failed to create system monitor: {}", e)))?;

    let info = collect_system_info(&mut monitor).await
        .map_err(|e| ApiError::Internal(format!("Failed to collect system info: {}", e)))?;

    Ok(Json(ApiResponse::success(info)))
}

/// Get system status
pub async fn get_system_status(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<SystemStatus>>, ApiError> {
    let mut monitor = SystemMonitor::new()
        .map_err(|e| ApiError::Internal(format!("Failed to create system monitor: {}", e)))?;

    let status = collect_system_status(&mut monitor).await
        .map_err(|e| ApiError::Internal(format!("Failed to collect system status: {}", e)))?;

    Ok(Json(ApiResponse::success(status)))
}

/// Get uptime information
pub async fn get_uptime(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<UptimeInfo>>, ApiError> {
    let mut monitor = SystemMonitor::new()
        .map_err(|e| ApiError::Internal(format!("Failed to create system monitor: {}", e)))?;

    let uptime = collect_uptime_info(&mut monitor).await
        .map_err(|e| ApiError::Internal(format!("Failed to collect uptime info: {}", e)))?;

    Ok(Json(ApiResponse::success(uptime)))
}

/// Get historical metrics data
pub async fn get_metrics_history(
    Query(params): Query<HistoricalMetricsRequest>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<HistoricalMetricsResponse>>, ApiError> {
    let mut monitor = SystemMonitor::new()
        .map_err(|e| ApiError::Internal(format!("Failed to create system monitor: {}", e)))?;

    let history = collect_metrics_history(&mut monitor, params).await
        .map_err(|e| ApiError::Internal(format!("Failed to collect metrics history: {}", e)))?;

    Ok(Json(ApiResponse::success(history)))
}

/// Get performance analysis
pub async fn get_performance_analysis(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<PerformanceAnalysis>>, ApiError> {
    let mut monitor = SystemMonitor::new()
        .map_err(|e| ApiError::Internal(format!("Failed to create system monitor: {}", e)))?;

    let analysis = collect_performance_analysis(&mut monitor).await
        .map_err(|e| ApiError::Internal(format!("Failed to collect performance analysis: {}", e)))?;

    Ok(Json(ApiResponse::success(analysis)))
}

/// Get system alerts
pub async fn get_system_alerts(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<Vec<SystemAlert>>>, ApiError> {
    let mut monitor = SystemMonitor::new()
        .map_err(|e| ApiError::Internal(format!("Failed to create system monitor: {}", e)))?;

    let alerts = collect_system_alerts(&mut monitor).await
        .map_err(|e| ApiError::Internal(format!("Failed to collect system alerts: {}", e)))?;

    Ok(Json(ApiResponse::success(alerts)))
}

/// Export metrics data
pub async fn export_metrics(
    State(state): State<Arc<AppState>>,
    Json(request): Json<MetricsExportRequest>,
) -> Result<Json<ApiResponse<String>>, ApiError> {
    let mut monitor = SystemMonitor::new()
        .map_err(|e| ApiError::Internal(format!("Failed to create system monitor: {}", e)))?;

    let export_data = export_metrics_data(&mut monitor, request).await
        .map_err(|e| ApiError::Internal(format!("Failed to export metrics: {}", e)))?;

    Ok(Json(ApiResponse::success(export_data)))
}

/// Collect system information from the system
async fn collect_system_info(monitor: &mut SystemMonitor) -> anyhow::Result<SystemInfo> {
    // Get system info from monitor
    let system_info = monitor.get_system_info()
        .map_err(|e| anyhow::anyhow!("Failed to get system info: {}", e))?;

    // Get storage information
    let storage_usage = monitor.storage_monitor().get_all_usage()
        .map_err(|e| anyhow::anyhow!("Failed to get storage info: {}", e))?;

    let (storage_total, storage_available) = if let Some(root_storage) = storage_usage.first() {
        (root_storage.total, root_storage.available)
    } else {
        (0, 0)
    };

    // Get OpenWrt version if available
    let openwrt_version = tokio::fs::read_to_string("/etc/openwrt_version")
        .await
        .ok()
        .map(|s| s.trim().to_string());

    // Get board name if available
    let board_name = tokio::fs::read_to_string("/tmp/sysinfo/board_name")
        .await
        .ok()
        .map(|s| s.trim().to_string());

    Ok(SystemInfo {
        hostname: system_info.hostname,
        kernel: system_info.kernel_version,
        architecture: system_info.architecture,
        cpu_model: system_info.cpu_model,
        cpu_cores: system_info.cpu_cores,
        memory_total: system_info.total_memory,
        memory_available: system_info.total_memory, // Will be updated with actual available memory
        storage_total,
        storage_available,
        uptime: system_info.uptime.uptime_seconds,
        load_average: [
            system_info.load_average.one_minute as f32,
            system_info.load_average.five_minutes as f32,
            system_info.load_average.fifteen_minutes as f32,
        ],
        openwrt_version,
        board_name,
    })
}

/// Collect system status information
async fn collect_system_status(monitor: &mut SystemMonitor) -> anyhow::Result<SystemStatus> {
    // Get current metrics from monitor
    let metrics = monitor.get_current_metrics()
        .map_err(|e| anyhow::anyhow!("Failed to get system metrics: {}", e))?;

    // Convert network stats to our API format
    let network_interfaces = metrics.network_stats.into_iter().map(|stats| {
        NetworkInterfaceStatus {
            name: stats.interface.clone(),
            status: "up".to_string(), // TODO: Determine interface status from stats
            ip_address: None, // TODO: Get IP address from interface
            rx_bytes: stats.rx_bytes,
            tx_bytes: stats.tx_bytes,
            rx_packets: stats.rx_packets,
            tx_packets: stats.tx_packets,
        }
    }).collect();

    // Calculate storage usage percentage
    let storage_usage = if let Some(storage) = metrics.storage_usage.first() {
        if storage.total > 0 {
            ((storage.used as f32 / storage.total as f32) * 100.0)
        } else {
            0.0
        }
    } else {
        0.0
    };

    // Get temperature if available (placeholder for now)
    let temperature = get_temperature().await;

    Ok(SystemStatus {
        cpu_usage: metrics.cpu_usage.overall as f32,
        memory_usage: (metrics.memory_usage.used as f32 / metrics.memory_usage.total as f32) * 100.0,
        storage_usage,
        network_interfaces,
        running_processes: metrics.process_count,
        active_connections: 0, // TODO: Implement connection counting
        temperature,
    })
}

/// Collect uptime information
async fn collect_uptime_info(monitor: &mut SystemMonitor) -> anyhow::Result<UptimeInfo> {
    let system_info = monitor.get_system_info()
        .map_err(|e| anyhow::anyhow!("Failed to get system info: {}", e))?;

    let uptime_seconds = system_info.uptime.uptime_seconds;
    let uptime_formatted = format_uptime(uptime_seconds);
    let boot_time = chrono::DateTime::from(system_info.uptime.boot_time);
    let load_average = [
        system_info.load_average.one_minute as f32,
        system_info.load_average.five_minutes as f32,
        system_info.load_average.fifteen_minutes as f32,
    ];

    Ok(UptimeInfo {
        uptime_seconds,
        uptime_formatted,
        boot_time,
        load_average,
    })
}



/// Get system temperature
async fn get_temperature() -> Option<f32> {
    // This is a simplified implementation
    // In a real implementation, you'd read from thermal sensors
    None
}

/// Format uptime seconds into human-readable string
fn format_uptime(seconds: u64) -> String {
    let days = seconds / 86400;
    let hours = (seconds % 86400) / 3600;
    let minutes = (seconds % 3600) / 60;
    let secs = seconds % 60;

    if days > 0 {
        format!("{}d {}h {}m {}s", days, hours, minutes, secs)
    } else if hours > 0 {
        format!("{}h {}m {}s", hours, minutes, secs)
    } else if minutes > 0 {
        format!("{}m {}s", minutes, secs)
    } else {
        format!("{}s", secs)
    }
}

/// Collect historical metrics data
async fn collect_metrics_history(
    monitor: &mut SystemMonitor,
    params: HistoricalMetricsRequest
) -> anyhow::Result<HistoricalMetricsResponse> {
    let time_range = params.time_range.unwrap_or_else(|| "24h".to_string());
    let interval = params.interval.unwrap_or_else(|| "5m".to_string());

    // Parse time range to determine how many data points to generate
    let data_points_count = match time_range.as_str() {
        "1h" => 12,   // 5-minute intervals
        "6h" => 72,   // 5-minute intervals
        "24h" => 288, // 5-minute intervals
        "7d" => 168,  // 1-hour intervals
        "30d" => 720, // 1-hour intervals
        _ => 288,     // default to 24h
    };

    // Generate mock historical data points
    let mut data_points = Vec::new();
    let now = SystemTime::now();
    let interval_duration = parse_interval(&interval);

    for i in 0..data_points_count {
        let timestamp = now - Duration::from_secs(interval_duration * (data_points_count - i - 1) as u64);

        // Generate realistic mock data with some variation
        let base_cpu = 15.0 + (i as f32 * 0.1) % 30.0;
        let base_memory = 45.0 + (i as f32 * 0.2) % 25.0;
        let base_network_rx = 1024.0 * (50.0 + (i as f64 * 0.3) % 100.0);
        let base_network_tx = 1024.0 * (30.0 + (i as f64 * 0.25) % 80.0);

        data_points.push(MetricsDataPoint {
            timestamp,
            cpu_usage: base_cpu + (i as f32 * 0.05) % 10.0,
            memory_usage: base_memory + (i as f32 * 0.1) % 15.0,
            storage_usage: 65.0 + (i as f32 * 0.01) % 5.0,
            network_rx_rate: base_network_rx,
            network_tx_rate: base_network_tx,
            load_average: 0.5 + (i as f32 * 0.01) % 0.8,
            process_count: 85 + (i % 20) as u32,
            temperature: Some(42.0 + (i as f32 * 0.02) % 8.0),
        });
    }

    // Calculate summary statistics
    let cpu_values: Vec<f32> = data_points.iter().map(|dp| dp.cpu_usage).collect();
    let memory_values: Vec<f32> = data_points.iter().map(|dp| dp.memory_usage).collect();
    let network_rx_values: Vec<f64> = data_points.iter().map(|dp| dp.network_rx_rate).collect();
    let network_tx_values: Vec<f64> = data_points.iter().map(|dp| dp.network_tx_rate).collect();

    let summary = MetricsSummary {
        cpu_avg: cpu_values.iter().sum::<f32>() / cpu_values.len() as f32,
        cpu_max: cpu_values.iter().fold(0.0f32, |a, &b| a.max(b)),
        cpu_min: cpu_values.iter().fold(100.0f32, |a, &b| a.min(b)),
        memory_avg: memory_values.iter().sum::<f32>() / memory_values.len() as f32,
        memory_max: memory_values.iter().fold(0.0f32, |a, &b| a.max(b)),
        memory_min: memory_values.iter().fold(100.0f32, |a, &b| a.min(b)),
        network_peak_rx: network_rx_values.iter().fold(0.0f64, |a, &b| a.max(b)),
        network_peak_tx: network_tx_values.iter().fold(0.0f64, |a, &b| a.max(b)),
        uptime_percentage: 99.8, // Mock uptime percentage
    };

    Ok(HistoricalMetricsResponse {
        time_range,
        interval,
        data_points,
        summary,
    })
}

/// Parse interval string to seconds
fn parse_interval(interval: &str) -> u64 {
    match interval {
        "1m" => 60,
        "5m" => 300,
        "15m" => 900,
        "1h" => 3600,
        _ => 300, // default to 5 minutes
    }
}

/// Collect performance analysis
async fn collect_performance_analysis(monitor: &mut SystemMonitor) -> anyhow::Result<PerformanceAnalysis> {
    // Get current metrics for analysis
    let metrics = monitor.get_current_metrics()
        .map_err(|e| anyhow::anyhow!("Failed to get current metrics: {}", e))?;

    // Mock performance analysis based on current metrics
    let cpu_analysis = CpuPerformanceAnalysis {
        average_usage: metrics.cpu_usage.overall as f32,
        peak_usage: (metrics.cpu_usage.overall as f32 * 1.2).min(100.0),
        idle_time_percentage: 100.0 - metrics.cpu_usage.overall as f32,
        load_balance_score: if metrics.load_average.one_minute < 1.0 { 85 } else { 65 },
        thermal_status: if metrics.cpu_usage.overall > 80.0 { "Hot".to_string() }
                       else if metrics.cpu_usage.overall > 60.0 { "Warm".to_string() }
                       else { "Normal".to_string() },
        frequency_efficiency: 0.85,
    };

    let memory_usage_percent = (metrics.memory_usage.used as f32 / metrics.memory_usage.total as f32) * 100.0;
    let memory_analysis = MemoryPerformanceAnalysis {
        average_usage: memory_usage_percent,
        peak_usage: (memory_usage_percent * 1.1).min(100.0),
        swap_usage: metrics.memory_usage.swap_usage_percent as f32,
        cache_efficiency: 0.78,
        memory_pressure: if memory_usage_percent > 80.0 { 0.8 } else { 0.3 },
        fragmentation_level: if memory_usage_percent > 85.0 { "High".to_string() }
                            else if memory_usage_percent > 70.0 { "Medium".to_string() }
                            else { "Low".to_string() },
    };

    let network_analysis = NetworkPerformanceAnalysis {
        total_throughput: metrics.network_stats.iter().map(|s| s.rx_bytes + s.tx_bytes).sum::<u64>() as f64,
        peak_rx_rate: metrics.network_stats.iter().map(|s| s.rx_bytes).max().unwrap_or(0) as f64,
        peak_tx_rate: metrics.network_stats.iter().map(|s| s.tx_bytes).max().unwrap_or(0) as f64,
        error_rate: 0.01, // Mock low error rate
        packet_loss: 0.001, // Mock very low packet loss
        latency_score: 92, // Mock good latency score
    };

    let storage_usage_percent = if let Some(storage) = metrics.storage_usage.first() {
        if storage.total > 0 {
            (storage.used as f32 / storage.total as f32) * 100.0
        } else {
            0.0
        }
    } else {
        0.0
    };

    let storage_analysis = StoragePerformanceAnalysis {
        usage_percentage: storage_usage_percent,
        io_wait_time: metrics.cpu_usage.iowait as f32,
        read_write_ratio: 1.2, // Mock read/write ratio
        fragmentation: if storage_usage_percent > 90.0 { 0.3 } else { 0.1 },
        health_status: if storage_usage_percent > 95.0 { "Poor".to_string() }
                      else if storage_usage_percent > 85.0 { "Fair".to_string() }
                      else if storage_usage_percent > 70.0 { "Good".to_string() }
                      else { "Excellent".to_string() },
    };

    // Generate recommendations based on analysis
    let mut recommendations = Vec::new();
    let mut bottlenecks = Vec::new();

    if cpu_analysis.average_usage > 80.0 {
        recommendations.push("Consider reducing CPU-intensive processes".to_string());
        bottlenecks.push("High CPU usage detected".to_string());
    }

    if memory_analysis.average_usage > 85.0 {
        recommendations.push("Consider increasing available memory or reducing memory usage".to_string());
        bottlenecks.push("High memory usage detected".to_string());
    }

    if storage_analysis.usage_percentage > 90.0 {
        recommendations.push("Clean up disk space or expand storage capacity".to_string());
        bottlenecks.push("Low disk space available".to_string());
    }

    if recommendations.is_empty() {
        recommendations.push("System performance is optimal".to_string());
    }

    // Calculate overall performance score
    let cpu_score = ((100.0 - cpu_analysis.average_usage) * 0.3) as u8;
    let memory_score = ((100.0 - memory_analysis.average_usage) * 0.3) as u8;
    let storage_score = ((100.0 - storage_analysis.usage_percentage) * 0.2) as u8;
    let network_score = (network_analysis.latency_score as f32 * 0.2) as u8;
    let overall_score = cpu_score + memory_score + storage_score + network_score;

    Ok(PerformanceAnalysis {
        overall_score,
        cpu_analysis,
        memory_analysis,
        network_analysis,
        storage_analysis,
        recommendations,
        bottlenecks,
    })
}

/// Collect system alerts
async fn collect_system_alerts(monitor: &mut SystemMonitor) -> anyhow::Result<Vec<SystemAlert>> {
    let mut alerts = Vec::new();

    // Get current metrics for alert evaluation
    let metrics = monitor.get_current_metrics()
        .map_err(|e| anyhow::anyhow!("Failed to get current metrics: {}", e))?;

    let now = SystemTime::now();

    // CPU usage alert
    if metrics.cpu_usage.overall > 85.0 {
        alerts.push(SystemAlert {
            id: "cpu_high".to_string(),
            severity: AlertSeverity::Critical,
            title: "High CPU Usage".to_string(),
            message: format!("CPU usage is at {:.1}%, which exceeds the critical threshold", metrics.cpu_usage.overall),
            metric: "cpu_usage".to_string(),
            current_value: metrics.cpu_usage.overall,
            threshold: 85.0,
            timestamp: now,
            acknowledged: false,
        });
    } else if metrics.cpu_usage.overall > 70.0 {
        alerts.push(SystemAlert {
            id: "cpu_warning".to_string(),
            severity: AlertSeverity::Warning,
            title: "Elevated CPU Usage".to_string(),
            message: format!("CPU usage is at {:.1}%, approaching high usage threshold", metrics.cpu_usage.overall),
            metric: "cpu_usage".to_string(),
            current_value: metrics.cpu_usage.overall,
            threshold: 70.0,
            timestamp: now,
            acknowledged: false,
        });
    }

    // Memory usage alert
    let memory_usage_percent = (metrics.memory_usage.used as f64 / metrics.memory_usage.total as f64) * 100.0;
    if memory_usage_percent > 90.0 {
        alerts.push(SystemAlert {
            id: "memory_critical".to_string(),
            severity: AlertSeverity::Critical,
            title: "Critical Memory Usage".to_string(),
            message: format!("Memory usage is at {:.1}%, system may become unstable", memory_usage_percent),
            metric: "memory_usage".to_string(),
            current_value: memory_usage_percent,
            threshold: 90.0,
            timestamp: now,
            acknowledged: false,
        });
    } else if memory_usage_percent > 80.0 {
        alerts.push(SystemAlert {
            id: "memory_warning".to_string(),
            severity: AlertSeverity::Warning,
            title: "High Memory Usage".to_string(),
            message: format!("Memory usage is at {:.1}%, consider freeing up memory", memory_usage_percent),
            metric: "memory_usage".to_string(),
            current_value: memory_usage_percent,
            threshold: 80.0,
            timestamp: now,
            acknowledged: false,
        });
    }

    // Storage usage alert
    if let Some(storage) = metrics.storage_usage.first() {
        let storage_usage_percent = if storage.total > 0 {
            (storage.used as f64 / storage.total as f64) * 100.0
        } else {
            0.0
        };

        if storage_usage_percent > 95.0 {
            alerts.push(SystemAlert {
                id: "storage_critical".to_string(),
                severity: AlertSeverity::Critical,
                title: "Critical Disk Space".to_string(),
                message: format!("Disk usage is at {:.1}%, immediate action required", storage_usage_percent),
                metric: "storage_usage".to_string(),
                current_value: storage_usage_percent,
                threshold: 95.0,
                timestamp: now,
                acknowledged: false,
            });
        } else if storage_usage_percent > 85.0 {
            alerts.push(SystemAlert {
                id: "storage_warning".to_string(),
                severity: AlertSeverity::Warning,
                title: "Low Disk Space".to_string(),
                message: format!("Disk usage is at {:.1}%, consider cleaning up files", storage_usage_percent),
                metric: "storage_usage".to_string(),
                current_value: storage_usage_percent,
                threshold: 85.0,
                timestamp: now,
                acknowledged: false,
            });
        }
    }

    // Load average alert
    if metrics.load_average.one_minute > 4.0 {
        alerts.push(SystemAlert {
            id: "load_high".to_string(),
            severity: AlertSeverity::Warning,
            title: "High System Load".to_string(),
            message: format!("System load average is {:.2}, system may be overloaded", metrics.load_average.one_minute),
            metric: "load_average".to_string(),
            current_value: metrics.load_average.one_minute,
            threshold: 4.0,
            timestamp: now,
            acknowledged: false,
        });
    }

    // Add an informational alert if no issues
    if alerts.is_empty() {
        alerts.push(SystemAlert {
            id: "system_healthy".to_string(),
            severity: AlertSeverity::Info,
            title: "System Healthy".to_string(),
            message: "All system metrics are within normal ranges".to_string(),
            metric: "overall".to_string(),
            current_value: 100.0,
            threshold: 100.0,
            timestamp: now,
            acknowledged: false,
        });
    }

    Ok(alerts)
}

/// Export metrics data
async fn export_metrics_data(
    monitor: &mut SystemMonitor,
    request: MetricsExportRequest
) -> anyhow::Result<String> {
    // Get historical data for export
    let history_request = HistoricalMetricsRequest {
        time_range: Some(request.time_range.clone()),
        interval: Some("5m".to_string()),
        metrics: Some(request.metrics.join(",")),
    };

    let history = collect_metrics_history(monitor, history_request).await?;

    match request.format.as_str() {
        "csv" => export_as_csv(&history, &request),
        "json" => export_as_json(&history, &request),
        "xml" => export_as_xml(&history, &request),
        _ => Err(anyhow::anyhow!("Unsupported export format: {}", request.format)),
    }
}

/// Export data as CSV format
fn export_as_csv(history: &HistoricalMetricsResponse, _request: &MetricsExportRequest) -> anyhow::Result<String> {
    let mut csv = String::new();

    // CSV header
    csv.push_str("timestamp,cpu_usage,memory_usage,storage_usage,network_rx_rate,network_tx_rate,load_average,process_count,temperature\n");

    // CSV data rows
    for point in &history.data_points {
        let timestamp = point.timestamp.duration_since(SystemTime::UNIX_EPOCH)
            .unwrap_or_default().as_secs();

        csv.push_str(&format!(
            "{},{},{},{},{},{},{},{},{}\n",
            timestamp,
            point.cpu_usage,
            point.memory_usage,
            point.storage_usage,
            point.network_rx_rate,
            point.network_tx_rate,
            point.load_average,
            point.process_count,
            point.temperature.unwrap_or(0.0)
        ));
    }

    // Add summary if requested
    if _request.include_summary {
        csv.push_str("\n# Summary Statistics\n");
        csv.push_str(&format!("# CPU Average: {:.2}%\n", history.summary.cpu_avg));
        csv.push_str(&format!("# CPU Max: {:.2}%\n", history.summary.cpu_max));
        csv.push_str(&format!("# Memory Average: {:.2}%\n", history.summary.memory_avg));
        csv.push_str(&format!("# Memory Max: {:.2}%\n", history.summary.memory_max));
        csv.push_str(&format!("# Network Peak RX: {:.0} bytes/s\n", history.summary.network_peak_rx));
        csv.push_str(&format!("# Network Peak TX: {:.0} bytes/s\n", history.summary.network_peak_tx));
    }

    Ok(csv)
}

/// Export data as JSON format
fn export_as_json(history: &HistoricalMetricsResponse, _request: &MetricsExportRequest) -> anyhow::Result<String> {
    let json = serde_json::to_string_pretty(history)
        .map_err(|e| anyhow::anyhow!("Failed to serialize to JSON: {}", e))?;
    Ok(json)
}

/// Export data as XML format
fn export_as_xml(history: &HistoricalMetricsResponse, request: &MetricsExportRequest) -> anyhow::Result<String> {
    let mut xml = String::new();
    xml.push_str("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n");
    xml.push_str("<metrics_export>\n");
    xml.push_str(&format!("  <time_range>{}</time_range>\n", history.time_range));
    xml.push_str(&format!("  <interval>{}</interval>\n", history.interval));
    xml.push_str("  <data_points>\n");

    for point in &history.data_points {
        let timestamp = point.timestamp.duration_since(SystemTime::UNIX_EPOCH)
            .unwrap_or_default().as_secs();

        xml.push_str("    <data_point>\n");
        xml.push_str(&format!("      <timestamp>{}</timestamp>\n", timestamp));
        xml.push_str(&format!("      <cpu_usage>{}</cpu_usage>\n", point.cpu_usage));
        xml.push_str(&format!("      <memory_usage>{}</memory_usage>\n", point.memory_usage));
        xml.push_str(&format!("      <storage_usage>{}</storage_usage>\n", point.storage_usage));
        xml.push_str(&format!("      <network_rx_rate>{}</network_rx_rate>\n", point.network_rx_rate));
        xml.push_str(&format!("      <network_tx_rate>{}</network_tx_rate>\n", point.network_tx_rate));
        xml.push_str(&format!("      <load_average>{}</load_average>\n", point.load_average));
        xml.push_str(&format!("      <process_count>{}</process_count>\n", point.process_count));
        if let Some(temp) = point.temperature {
            xml.push_str(&format!("      <temperature>{}</temperature>\n", temp));
        }
        xml.push_str("    </data_point>\n");
    }

    xml.push_str("  </data_points>\n");

    if request.include_summary {
        xml.push_str("  <summary>\n");
        xml.push_str(&format!("    <cpu_avg>{}</cpu_avg>\n", history.summary.cpu_avg));
        xml.push_str(&format!("    <cpu_max>{}</cpu_max>\n", history.summary.cpu_max));
        xml.push_str(&format!("    <memory_avg>{}</memory_avg>\n", history.summary.memory_avg));
        xml.push_str(&format!("    <memory_max>{}</memory_max>\n", history.summary.memory_max));
        xml.push_str(&format!("    <network_peak_rx>{}</network_peak_rx>\n", history.summary.network_peak_rx));
        xml.push_str(&format!("    <network_peak_tx>{}</network_peak_tx>\n", history.summary.network_peak_tx));
        xml.push_str("  </summary>\n");
    }

    xml.push_str("</metrics_export>\n");
    Ok(xml)
}
