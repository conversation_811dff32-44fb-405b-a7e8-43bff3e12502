//! Firewall management API endpoints
//!
//! Provides REST API endpoints for managing OpenWrt firewall configuration
//! including zones, rules, port forwarding, and security policies.

use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::Json,
    routing::{get, post, put, delete},
    Router,
};
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, sync::Arc};
use luci_network_config::{
    FirewallManager, FirewallZone, FirewallRule, PortForwardRule, ForwardingRule,
    FirewallPolicy, FirewallTarget, Protocol,
};
use crate::{AppState, api::{ApiResponse, ApiError}};

/// Firewall zone response
#[derive(Debug, Serialize, Deserialize)]
pub struct FirewallZoneResponse {
    pub name: String,
    pub network: Vec<String>,
    pub input: String,
    pub output: String,
    pub forward: String,
    pub masq: bool,
    pub mtu_fix: bool,
    pub log: bool,
    pub log_limit: Option<String>,
}

/// Firewall rule response
#[derive(Debug, Serialize, Deserialize)]
pub struct FirewallRuleResponse {
    pub name: String,
    pub src: Option<String>,
    pub dest: Option<String>,
    pub src_ip: Option<String>,
    pub dest_ip: Option<String>,
    pub src_port: Option<String>,
    pub dest_port: Option<String>,
    pub proto: Option<String>,
    pub target: String,
    pub enabled: bool,
    pub family: Option<String>,
}

/// Port forward rule response
#[derive(Debug, Serialize, Deserialize)]
pub struct PortForwardResponse {
    pub name: String,
    pub src: String,
    pub dest: String,
    pub src_dport: String,
    pub dest_ip: String,
    pub dest_port: Option<String>,
    pub proto: String,
    pub enabled: bool,
}

/// Firewall statistics response
#[derive(Debug, Serialize, Deserialize)]
pub struct FirewallStatsResponse {
    pub zones: HashMap<String, ZoneStats>,
    pub rules: HashMap<String, RuleStats>,
    pub total_packets: u64,
    pub total_bytes: u64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ZoneStats {
    pub packets_in: u64,
    pub packets_out: u64,
    pub bytes_in: u64,
    pub bytes_out: u64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RuleStats {
    pub packets: u64,
    pub bytes: u64,
    pub last_seen: Option<String>,
}

/// Firewall zone creation/update request
#[derive(Debug, Deserialize)]
pub struct CreateZoneRequest {
    pub name: String,
    pub network: Vec<String>,
    pub input: String,
    pub output: String,
    pub forward: String,
    pub masq: Option<bool>,
    pub mtu_fix: Option<bool>,
    pub log: Option<bool>,
    pub log_limit: Option<String>,
}

/// Firewall rule creation/update request
#[derive(Debug, Deserialize)]
pub struct CreateRuleRequest {
    pub name: String,
    pub src: Option<String>,
    pub dest: Option<String>,
    pub src_ip: Option<String>,
    pub dest_ip: Option<String>,
    pub src_port: Option<String>,
    pub dest_port: Option<String>,
    pub proto: Option<String>,
    pub target: String,
    pub enabled: Option<bool>,
    pub family: Option<String>,
}

/// Port forward creation/update request
#[derive(Debug, Deserialize)]
pub struct CreatePortForwardRequest {
    pub name: String,
    pub src: String,
    pub dest: String,
    pub src_dport: String,
    pub dest_ip: String,
    pub dest_port: Option<String>,
    pub proto: String,
    pub enabled: Option<bool>,
}

/// Get all firewall zones
pub async fn get_zones(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<Vec<FirewallZoneResponse>>>, ApiError> {
    let firewall_manager = FirewallManager::new();
    let zones = firewall_manager.get_zones();

    let zone_responses: Vec<FirewallZoneResponse> = zones.into_iter().map(|zone| {
        FirewallZoneResponse {
            name: zone.name.clone(),
            network: zone.network.clone(),
            input: format!("{:?}", zone.input).to_lowercase(),
            output: format!("{:?}", zone.output).to_lowercase(),
            forward: format!("{:?}", zone.forward).to_lowercase(),
            masq: zone.masq,
            mtu_fix: zone.mtu_fix,
            log: zone.log,
            log_limit: zone.log_limit.clone(),
        }
    }).collect();

    Ok(Json(ApiResponse::success(zone_responses)))
}

/// Get specific firewall zone
pub async fn get_zone(
    Path(zone_name): Path<String>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<FirewallZoneResponse>>, ApiError> {
    let firewall_manager = FirewallManager::new();
    
    if let Some(zone) = firewall_manager.get_zone(&zone_name) {
        let zone_response = FirewallZoneResponse {
            name: zone.name.clone(),
            network: zone.network.clone(),
            input: format!("{:?}", zone.input).to_lowercase(),
            output: format!("{:?}", zone.output).to_lowercase(),
            forward: format!("{:?}", zone.forward).to_lowercase(),
            masq: zone.masq,
            mtu_fix: zone.mtu_fix,
            log: zone.log,
            log_limit: zone.log_limit.clone(),
        };
        
        Ok(Json(ApiResponse::success(zone_response)))
    } else {
        Err(ApiError::NotFound(format!("Zone '{}' not found", zone_name)))
    }
}

/// Create new firewall zone
pub async fn create_zone(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateZoneRequest>,
) -> Result<Json<ApiResponse<FirewallZoneResponse>>, ApiError> {
    let mut firewall_manager = FirewallManager::new();
    
    let input_policy = match request.input.as_str() {
        "accept" => FirewallPolicy::Accept,
        "reject" => FirewallPolicy::Reject,
        "drop" => FirewallPolicy::Drop,
        _ => return Err(ApiError::BadRequest("Invalid input policy".to_string())),
    };
    
    let output_policy = match request.output.as_str() {
        "accept" => FirewallPolicy::Accept,
        "reject" => FirewallPolicy::Reject,
        "drop" => FirewallPolicy::Drop,
        _ => return Err(ApiError::BadRequest("Invalid output policy".to_string())),
    };
    
    let forward_policy = match request.forward.as_str() {
        "accept" => FirewallPolicy::Accept,
        "reject" => FirewallPolicy::Reject,
        "drop" => FirewallPolicy::Drop,
        _ => return Err(ApiError::BadRequest("Invalid forward policy".to_string())),
    };
    
    let zone = FirewallZone {
        name: request.name.clone(),
        network: request.network.clone(),
        input: input_policy,
        output: output_policy,
        forward: forward_policy,
        masq: request.masq.unwrap_or(false),
        mtu_fix: request.mtu_fix.unwrap_or(false),
        log: request.log.unwrap_or(false),
        log_limit: request.log_limit.clone(),
    };
    
    firewall_manager.add_zone(zone.clone())
        .map_err(|e| ApiError::Internal(format!("Failed to create zone: {}", e)))?;
    
    let zone_response = FirewallZoneResponse {
        name: zone.name,
        network: zone.network,
        input: format!("{:?}", zone.input).to_lowercase(),
        output: format!("{:?}", zone.output).to_lowercase(),
        forward: format!("{:?}", zone.forward).to_lowercase(),
        masq: zone.masq,
        mtu_fix: zone.mtu_fix,
        log: zone.log,
        log_limit: zone.log_limit,
    };
    
    Ok(Json(ApiResponse::success(zone_response)))
}

/// Update firewall zone
pub async fn update_zone(
    Path(zone_name): Path<String>,
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateZoneRequest>,
) -> Result<Json<ApiResponse<FirewallZoneResponse>>, ApiError> {
    let mut firewall_manager = FirewallManager::new();
    
    // First check if zone exists
    if firewall_manager.get_zone(&zone_name).is_none() {
        return Err(ApiError::NotFound(format!("Zone '{}' not found", zone_name)));
    }
    
    let input_policy = match request.input.as_str() {
        "accept" => FirewallPolicy::Accept,
        "reject" => FirewallPolicy::Reject,
        "drop" => FirewallPolicy::Drop,
        _ => return Err(ApiError::BadRequest("Invalid input policy".to_string())),
    };
    
    let output_policy = match request.output.as_str() {
        "accept" => FirewallPolicy::Accept,
        "reject" => FirewallPolicy::Reject,
        "drop" => FirewallPolicy::Drop,
        _ => return Err(ApiError::BadRequest("Invalid output policy".to_string())),
    };
    
    let forward_policy = match request.forward.as_str() {
        "accept" => FirewallPolicy::Accept,
        "reject" => FirewallPolicy::Reject,
        "drop" => FirewallPolicy::Drop,
        _ => return Err(ApiError::BadRequest("Invalid forward policy".to_string())),
    };
    
    let zone = FirewallZone {
        name: zone_name.clone(),
        network: request.network.clone(),
        input: input_policy,
        output: output_policy,
        forward: forward_policy,
        masq: request.masq.unwrap_or(false),
        mtu_fix: request.mtu_fix.unwrap_or(false),
        log: request.log.unwrap_or(false),
        log_limit: request.log_limit.clone(),
    };
    
    firewall_manager.update_zone(zone.clone())
        .map_err(|e| ApiError::Internal(format!("Failed to update zone: {}", e)))?;
    
    let zone_response = FirewallZoneResponse {
        name: zone.name,
        network: zone.network,
        input: format!("{:?}", zone.input).to_lowercase(),
        output: format!("{:?}", zone.output).to_lowercase(),
        forward: format!("{:?}", zone.forward).to_lowercase(),
        masq: zone.masq,
        mtu_fix: zone.mtu_fix,
        log: zone.log,
        log_limit: zone.log_limit,
    };
    
    Ok(Json(ApiResponse::success(zone_response)))
}

/// Delete firewall zone
pub async fn delete_zone(
    Path(zone_name): Path<String>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<()>>, ApiError> {
    let mut firewall_manager = FirewallManager::new();

    firewall_manager.remove_zone(&zone_name)
        .map_err(|e| ApiError::Internal(format!("Failed to delete zone: {}", e)))?;

    Ok(Json(ApiResponse::success(())))
}

/// Get all firewall rules
pub async fn get_rules(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<Vec<FirewallRuleResponse>>>, ApiError> {
    let firewall_manager = FirewallManager::new();
    let rules = firewall_manager.get_rules();

    let rule_responses: Vec<FirewallRuleResponse> = rules.iter().map(|rule| {
        FirewallRuleResponse {
            name: rule.name.clone(),
            src: rule.src.clone(),
            dest: rule.dest.clone(),
            src_ip: rule.src_ip.map(|ip| ip.to_string()),
            dest_ip: rule.dest_ip.map(|ip| ip.to_string()),
            src_port: rule.src_port.clone(),
            dest_port: rule.dest_port.clone(),
            proto: rule.proto.clone().map(|p| format!("{}", p)),
            target: format!("{:?}", rule.target).to_lowercase(),
            enabled: rule.enabled,
            family: rule.family.clone(),
        }
    }).collect();

    Ok(Json(ApiResponse::success(rule_responses)))
}

/// Create new firewall rule
pub async fn create_rule(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateRuleRequest>,
) -> Result<Json<ApiResponse<FirewallRuleResponse>>, ApiError> {
    let mut firewall_manager = FirewallManager::new();

    let target = match request.target.as_str() {
        "accept" => FirewallTarget::Accept,
        "reject" => FirewallTarget::Reject,
        "drop" => FirewallTarget::Drop,
        _ => return Err(ApiError::BadRequest("Invalid target".to_string())),
    };

    let proto = if let Some(proto_str) = &request.proto {
        Some(match proto_str.as_str() {
            "tcp" => Protocol::TCP,
            "udp" => Protocol::UDP,
            "icmp" => Protocol::ICMP,
            "all" => Protocol::All,
            _ => return Err(ApiError::BadRequest("Invalid protocol".to_string())),
        })
    } else {
        None
    };

    let src_ip = if let Some(ip_str) = &request.src_ip {
        Some(ip_str.parse().map_err(|_| ApiError::BadRequest("Invalid source IP".to_string()))?)
    } else {
        None
    };

    let dest_ip = if let Some(ip_str) = &request.dest_ip {
        Some(ip_str.parse().map_err(|_| ApiError::BadRequest("Invalid destination IP".to_string()))?)
    } else {
        None
    };

    let rule = FirewallRule {
        name: request.name.clone(),
        src: request.src.clone(),
        dest: request.dest.clone(),
        src_ip,
        dest_ip,
        src_port: request.src_port.clone(),
        dest_port: request.dest_port.clone(),
        proto,
        target,
        enabled: request.enabled.unwrap_or(true),
        family: request.family.clone(),
    };

    firewall_manager.add_rule(rule.clone())
        .map_err(|e| ApiError::Internal(format!("Failed to create rule: {}", e)))?;

    let rule_response = FirewallRuleResponse {
        name: rule.name,
        src: rule.src,
        dest: rule.dest,
        src_ip: rule.src_ip.map(|ip| ip.to_string()),
        dest_ip: rule.dest_ip.map(|ip| ip.to_string()),
        src_port: rule.src_port,
        dest_port: rule.dest_port,
        proto: rule.proto.map(|p| format!("{}", p)),
        target: format!("{:?}", rule.target).to_lowercase(),
        enabled: rule.enabled,
        family: rule.family,
    };

    Ok(Json(ApiResponse::success(rule_response)))
}

/// Get all port forwards
pub async fn get_port_forwards(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<Vec<PortForwardResponse>>>, ApiError> {
    let firewall_manager = FirewallManager::new();
    let forwards = firewall_manager.get_port_forwards();

    let forward_responses: Vec<PortForwardResponse> = forwards.iter().map(|forward| {
        PortForwardResponse {
            name: forward.name.clone(),
            src: forward.src.clone(),
            dest: forward.dest.clone(),
            src_dport: forward.src_dport.clone(),
            dest_ip: forward.dest_ip.to_string(),
            dest_port: forward.dest_port.clone(),
            proto: format!("{}", forward.proto),
            enabled: forward.enabled,
        }
    }).collect();

    Ok(Json(ApiResponse::success(forward_responses)))
}

/// Create new port forward
pub async fn create_port_forward(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreatePortForwardRequest>,
) -> Result<Json<ApiResponse<PortForwardResponse>>, ApiError> {
    let mut firewall_manager = FirewallManager::new();

    let proto = match request.proto.as_str() {
        "tcp" => Protocol::TCP,
        "udp" => Protocol::UDP,
        "all" => Protocol::All,
        _ => return Err(ApiError::BadRequest("Invalid protocol".to_string())),
    };

    let forward = PortForwardRule {
        name: request.name.clone(),
        src: request.src.clone(),
        dest: request.dest.clone(),
        src_dport: request.src_dport.clone(),
        dest_ip: request.dest_ip.parse().map_err(|_| ApiError::BadRequest("Invalid IP address".to_string()))?,
        dest_port: request.dest_port.clone(),
        proto,
        enabled: request.enabled.unwrap_or(true),
    };

    firewall_manager.add_port_forward(forward.clone())
        .map_err(|e| ApiError::Internal(format!("Failed to create port forward: {}", e)))?;

    let forward_response = PortForwardResponse {
        name: forward.name,
        src: forward.src,
        dest: forward.dest,
        src_dport: forward.src_dport,
        dest_ip: forward.dest_ip.to_string(),
        dest_port: forward.dest_port,
        proto: format!("{}", forward.proto),
        enabled: forward.enabled,
    };

    Ok(Json(ApiResponse::success(forward_response)))
}

/// Get firewall statistics
pub async fn get_firewall_stats(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<FirewallStatsResponse>>, ApiError> {
    // In a real implementation, this would gather actual firewall statistics
    // For now, return mock data
    let mut zones = HashMap::new();
    zones.insert("lan".to_string(), ZoneStats {
        packets_in: 12345,
        packets_out: 23456,
        bytes_in: 1234567,
        bytes_out: 2345678,
    });
    zones.insert("wan".to_string(), ZoneStats {
        packets_in: 34567,
        packets_out: 45678,
        bytes_in: 3456789,
        bytes_out: 4567890,
    });

    let mut rules = HashMap::new();
    rules.insert("Allow-DHCP-Renew".to_string(), RuleStats {
        packets: 567,
        bytes: 56789,
        last_seen: Some("2024-01-15T10:30:00Z".to_string()),
    });

    let stats = FirewallStatsResponse {
        zones,
        rules,
        total_packets: 116046,
        total_bytes: 11661914,
    };

    Ok(Json(ApiResponse::success(stats)))
}

/// Create firewall routes
pub fn create_routes() -> Router<Arc<AppState>> {
    Router::new()
        .route("/firewall/zones", get(get_zones).post(create_zone))
        .route("/firewall/zones/:zone_name", get(get_zone).put(update_zone).delete(delete_zone))
        .route("/firewall/rules", get(get_rules).post(create_rule))
        .route("/firewall/port-forwards", get(get_port_forwards).post(create_port_forward))
        .route("/firewall/stats", get(get_firewall_stats))
}
