# Leptos configuration for LuCI Rust migration
# This configuration ensures SSR-only mode for embedded systems

[build]
# Output directory for built assets
output-name = "luci-web-ui"

# Binary package to build
bin-package = "web-ui"

# Site root for serving assets
site-root = "target/site"

# Site package directory
site-pkg-dir = "pkg"

# Assets directory
assets-dir = "assets"

# Style file (Tailwind CSS)
style-file = "style/main.css"

# Tailwind input file
tailwind-input-file = "style/tailwind.css"

# Tailwind config file
tailwind-config-file = "tailwind.config.js"

# Browser binary for opening during development
browser-open = false

# Watch additional files for changes
watch-additional-files = ["style", "assets"]

[server]
# Server port
port = 3000

# Server host
host = "0.0.0.0"

# Reload port for development
reload-port = 3001

# Protocol (http or https)
protocol = "http"

# TLS key file (for HTTPS)
tls-key-file = ""

# TLS cert file (for HTTPS)
tls-cert-file = ""

[client]
# Disable client-side hydration for SSR-only mode
# This is crucial for embedded systems to avoid WASM overhead
hydrate = false

[wasm]
# Disable WASM compilation entirely for embedded deployment
compile = false

[env]
# Environment variables
LEPTOS_OUTPUT_NAME = "luci-web-ui"
LEPTOS_SITE_ROOT = "target/site"
LEPTOS_SITE_PKG_DIR = "pkg"
LEPTOS_SITE_ADDR = "0.0.0.0:3000"
LEPTOS_RELOAD_PORT = "3001"

# OpenWrt specific environment
OPENWRT_TARGET = "true"
EMBEDDED_MODE = "true"
SSR_ONLY = "true"
